package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/azel-ko/final-ddd/internal/infrastructure/crawler"
	"github.com/azel-ko/final-ddd/internal/pkg/logger"
)

func main() {
	// 初始化日志
	logger.Init("info")

	// 创建爬虫
	jinjingCrawler := crawler.NewJinjing365Crawler()

	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	fmt.Println("=== 进京365摄像头爬虫测试 ===")

	// 1. 测试连接
	fmt.Println("\n1. 测试连接...")
	if err := jinjingCrawler.TestConnection(ctx); err != nil {
		log.Printf("连接测试失败: %v", err)
		return
	}
	fmt.Println("✓ 连接测试成功")

	// 2. 爬取摄像头数据
	fmt.Println("\n2. 爬取摄像头数据...")
	cameras, err := jinjingCrawler.CrawlCameras(ctx)
	if err != nil {
		log.Printf("爬取失败: %v", err)
		return
	}

	fmt.Printf("✓ 成功爬取 %d 个摄像头\n", len(cameras))

	// 3. 显示前10个摄像头信息
	fmt.Println("\n3. 摄像头数据样例:")
	count := 10
	if len(cameras) < count {
		count = len(cameras)
	}

	for i := 0; i < count; i++ {
		camera := cameras[i]
		fmt.Printf("摄像头 %d:\n", i+1)
		fmt.Printf("  名称: %s\n", camera.Name)
		fmt.Printf("  位置: (%.6f, %.6f)\n", camera.Latitude, camera.Longitude)
		fmt.Printf("  区域: %s %s %s\n", camera.Province, camera.City, camera.District)
		fmt.Printf("  道路: %s\n", camera.Road)
		fmt.Printf("  方向: %s\n", camera.Direction)
		fmt.Printf("  状态: %s\n", camera.Status)
		fmt.Printf("  可靠性: %d\n", camera.Reliability)
		fmt.Printf("  来源: %s\n", camera.Source)
		fmt.Println()
	}

	// 4. 统计信息
	fmt.Println("4. 统计信息:")

	// 按区域统计
	districtCount := make(map[string]int)
	statusCount := make(map[string]int)
	activeCount := 0

	for _, camera := range cameras {
		if camera.District != "" {
			districtCount[camera.District]++
		}
		statusCount[camera.Status]++
		if camera.Status == "active" {
			activeCount++
		}
	}

	fmt.Printf("  总摄像头数: %d\n", len(cameras))
	fmt.Printf("  活跃摄像头: %d\n", activeCount)
	fmt.Printf("  活跃率: %.1f%%\n", float64(activeCount)/float64(len(cameras))*100)

	fmt.Println("\n  按区域分布:")
	for district, count := range districtCount {
		fmt.Printf("    %s: %d个\n", district, count)
	}

	fmt.Println("\n  按状态分布:")
	for status, count := range statusCount {
		fmt.Printf("    %s: %d个\n", status, count)
	}

	// 5. 验证数据质量
	fmt.Println("\n5. 数据质量验证:")
	validCoords := 0
	validNames := 0
	validDistricts := 0

	for _, camera := range cameras {
		// 验证坐标是否在北京范围内
		if camera.Latitude > 39.4 && camera.Latitude < 41.1 &&
			camera.Longitude > 115.4 && camera.Longitude < 117.5 {
			validCoords++
		}

		if camera.Name != "" {
			validNames++
		}

		if camera.District != "" {
			validDistricts++
		}
	}

	fmt.Printf("  有效坐标: %d/%d (%.1f%%)\n", validCoords, len(cameras),
		float64(validCoords)/float64(len(cameras))*100)
	fmt.Printf("  有效名称: %d/%d (%.1f%%)\n", validNames, len(cameras),
		float64(validNames)/float64(len(cameras))*100)
	fmt.Printf("  有效区域: %d/%d (%.1f%%)\n", validDistricts, len(cameras),
		float64(validDistricts)/float64(len(cameras))*100)

	fmt.Println("\n=== 测试完成 ===")
}
