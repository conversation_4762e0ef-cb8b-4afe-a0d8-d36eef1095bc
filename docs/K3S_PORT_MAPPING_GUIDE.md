# 🚀 K3s 端口映射完整指南

## 📋 **概述**

本指南介绍如何在基于 K3s 的部署环境中灵活管理端口映射，支持本地和远程部署场景。

## 🔧 **已完成的优化**

### 1. **删除 Docker Compose 配置**
- ✅ 移除了 `deploy/development/` 目录下的所有 Docker Compose 相关配置
- ✅ 统一使用 K3s 作为唯一的部署方案

### 2. **统一数据库账户信息**
- ✅ 数据库用户名: `driving_nav_user`
- ✅ 数据库密码: `driving_nav_2024`
- ✅ 数据库名: `driving_navigation`
- ✅ Redis 密码: 默认为空（可配置）

### 3. **优化 K3s 配置文件**
- ✅ 添加持久化存储支持 (PVC)
- ✅ 改进健康检查配置
- ✅ 统一服务标签和命名规范
- ✅ 添加 NodePort 服务支持外部访问

## 🌐 **端口映射方案**

### **默认端口配置**
| 服务 | 内部端口 | 外部端口 (NodePort) | 用途 |
|------|----------|-------------------|------|
| PostgreSQL | 5432 | 30432 | 数据库访问 |
| Redis | 6379 | 30379 | 缓存服务 |
| Backend API | 8080 | 30080 | 后端 API |

### **服务类型说明**
- **ClusterIP**: 集群内部访问（默认）
- **NodePort**: 外部访问（可选启用）

## 🛠️ **使用方法**

### **本地 K3s 部署**

#### 1. 启用默认端口映射
```bash
cd deploy/local-k3s
./port-mapping.sh enable local
```

#### 2. 禁用端口映射
```bash
./port-mapping.sh disable
```

#### 3. 自定义端口映射
```bash
./port-mapping.sh custom local
# 按提示输入自定义端口
```

#### 4. 查看当前状态
```bash
./port-mapping.sh status local
```

#### 5. 测试连接
```bash
./port-mapping.sh test local
```

### **远程 K3s 部署**

#### 1. 启用默认端口映射
```bash
cd deploy/remote
./port-mapping.sh enable -H *************
```

#### 2. 自定义端口映射
```bash
./port-mapping.sh custom -H ************* -u ubuntu
```

#### 3. 查看远程状态
```bash
./port-mapping.sh status -H *************
```

#### 4. 配置防火墙
```bash
./port-mapping.sh firewall -H *************
```

#### 5. 测试远程连接
```bash
./port-mapping.sh test -H *************
```

## 📊 **服务访问示例**

### **本地访问**
```bash
# PostgreSQL 连接
psql -h localhost -p 30432 -U driving_nav_user -d driving_navigation

# Redis 连接
redis-cli -h localhost -p 30379

# Backend API 测试
curl http://localhost:30080/api/health
```

### **远程访问**
```bash
# PostgreSQL 连接
psql -h ************* -p 30432 -U driving_nav_user -d driving_navigation

# Redis 连接
redis-cli -h ************* -p 30379

# Backend API 测试
curl http://*************:30080/api/health
```

## 🔒 **安全配置**

### **防火墙配置**

#### Ubuntu/Debian (UFW)
```bash
sudo ufw allow 30432/tcp comment 'PostgreSQL NodePort'
sudo ufw allow 30379/tcp comment 'Redis NodePort'
sudo ufw allow 30080/tcp comment 'Backend API NodePort'
```

#### CentOS/RHEL (Firewalld)
```bash
sudo firewall-cmd --permanent --add-port=30432/tcp
sudo firewall-cmd --permanent --add-port=30379/tcp
sudo firewall-cmd --permanent --add-port=30080/tcp
sudo firewall-cmd --reload
```

### **网络安全建议**
1. **生产环境**: 仅开放必要的端口
2. **开发环境**: 可以开放所有端口便于调试
3. **使用 VPN**: 对于敏感服务，建议通过 VPN 访问
4. **IP 白名单**: 限制特定 IP 地址访问

## 🔄 **部署流程**

### **完整部署流程**
```bash
# 1. 部署应用
cd deploy/local-k3s
./deploy.sh

# 2. 启用端口映射
./port-mapping.sh enable local

# 3. 验证部署
./port-mapping.sh status local
./port-mapping.sh test local

# 4. 如需禁用端口映射
./port-mapping.sh disable
```

### **远程部署流程**
```bash
# 1. 远程部署
cd deploy/remote
./simple-deploy.sh -H ************* --all

# 2. 启用端口映射
./port-mapping.sh enable -H *************

# 3. 配置防火墙
./port-mapping.sh firewall -H *************

# 4. 验证部署
./port-mapping.sh status -H *************
./port-mapping.sh test -H *************
```

## 🐛 **故障排除**

### **常见问题**

#### 1. 端口被占用
```bash
# 查看端口占用
sudo netstat -tulpn | grep :30432

# 或使用 ss
sudo ss -tulpn | grep :30432
```

#### 2. 服务无法访问
```bash
# 检查 Pod 状态
kubectl get pods -n app

# 检查服务状态
kubectl get services -n app

# 查看 Pod 日志
kubectl logs -n app deployment/postgres
```

#### 3. 防火墙阻止连接
```bash
# 检查防火墙状态
sudo ufw status
# 或
sudo firewall-cmd --list-all
```

#### 4. K3s 集群问题
```bash
# 检查 K3s 状态
sudo systemctl status k3s

# 重启 K3s
sudo systemctl restart k3s

# 查看 K3s 日志
sudo journalctl -u k3s -f
```

### **日志查看**
```bash
# 查看应用日志
kubectl logs -n app deployment/app -f

# 查看数据库日志
kubectl logs -n app deployment/postgres -f

# 查看 Redis 日志
kubectl logs -n app deployment/redis -f
```

## 📈 **监控和维护**

### **健康检查**
```bash
# 检查所有服务健康状态
kubectl get pods -n app -o wide

# 检查服务端点
kubectl get endpoints -n app
```

### **资源监控**
```bash
# 查看资源使用情况
kubectl top pods -n app
kubectl top nodes
```

### **备份建议**
1. **数据库备份**: 定期备份 PostgreSQL 数据
2. **配置备份**: 备份 K3s 配置文件
3. **应用备份**: 备份应用代码和配置

## 🎯 **最佳实践**

### **开发环境**
- ✅ 启用所有端口映射便于调试
- ✅ 使用默认端口配置
- ✅ 定期清理未使用的资源

### **生产环境**
- ✅ 仅启用必要的端口映射
- ✅ 使用自定义端口增强安全性
- ✅ 配置防火墙和访问控制
- ✅ 启用 TLS/SSL 加密
- ✅ 定期更新和维护

### **安全建议**
- 🔒 更改默认密码
- 🔒 使用强密码策略
- 🔒 定期轮换密钥
- 🔒 监控访问日志
- 🔒 限制网络访问

## 📞 **支持和帮助**

### **获取帮助**
```bash
# 本地部署帮助
./deploy/local-k3s/port-mapping.sh help

# 远程部署帮助
./deploy/remote/port-mapping.sh help
```

### **相关文档**
- [K3s 官方文档](https://docs.k3s.io/)
- [Kubernetes NodePort 文档](https://kubernetes.io/docs/concepts/services-networking/service/#nodeport)
- [项目配置修复指南](./CONFIGURATION_FIXES.md)
- [代码质量分析报告](./CODE_QUALITY_ANALYSIS.md)

---

## 🎉 **总结**

通过这套 K3s 端口映射方案，你可以：

1. **灵活控制**: 随时启用/禁用端口映射
2. **统一管理**: 本地和远程部署使用相同的管理方式
3. **安全可控**: 支持自定义端口和防火墙配置
4. **易于维护**: 提供完整的状态查看和测试功能

现在你的数据库账户密码信息已经统一，K3s 部署脚本已经优化，支持灵活的端口映射管理！🚀
