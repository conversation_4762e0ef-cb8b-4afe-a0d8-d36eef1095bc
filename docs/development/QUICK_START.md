# 🚀 快速开始指南

本指南帮助你快速搭建本地开发环境并开始开发。

## 📋 前置要求

### 必需
- **Go 1.24+** - 后端开发语言
- **Node.js 18+** - 前端开发环境
- **Docker** - 容器化环境
- **Git** - 版本控制

### 可选
- **kubectl** - Kubernetes管理工具（用于k3s部署）
- **微信开发者工具** - 小程序开发

## 🔧 环境搭建

### 1. 克隆项目

```bash
git clone https://github.com/your-repo/Driving-Domain-Navigation.git
cd Driving-Domain-Navigation
```

### 2. 启动本地开发环境

```bash
# 一键启动（推荐）
./deploy/development/start.sh
```

这个脚本会自动：
- 启动PostgreSQL数据库容器
- 启动Redis缓存容器
- 设置开发环境变量
- 等待数据库就绪
- 可选择启动Go后端服务

### 3. 验证后端服务

```bash
# 健康检查
curl http://localhost:8080/api/health

# 预期响应
{
  "info": {
    "version": "dev",
    "build_time": "unknown",
    "commit_hash": "unknown",
    "go_version": "go1.24.0",
    "os": "linux",
    "arch": "amd64"
  },
  "status": "ok"
}
```

### 4. 启动前端开发服务器（可选）

```bash
# 新开终端窗口
cd frontend
npm install  # 或 pnpm install
npm run dev  # 或 pnpm dev
```

**前端访问地址**: http://localhost:3000

### 5. 小程序开发（可选）

1. 打开微信开发者工具
2. 选择"导入项目"
3. 项目目录选择：`./miniprogram`
4. AppID：使用测试号或你的小程序AppID

## 🌐 服务地址

| 服务 | 地址 | 说明 |
|------|------|------|
| 后端API | http://localhost:8080 | Go后端服务 |
| WebSocket | ws://localhost:8081/ws | 实时通信 |
| 前端管理后台 | http://localhost:3000 | React Web界面 |
| 数据库 | localhost:5432 | PostgreSQL |
| 缓存 | localhost:6379 | Redis |

## 🧪 测试API

### 基础测试

```bash
# 健康检查
curl http://localhost:8080/api/health

# 获取检查站列表
curl http://localhost:8080/api/checkpoints/

# 获取附近检查站
curl "http://localhost:8080/api/checkpoints/nearby?lat=39.9042&lng=116.4074&radius=10"
```

### 认证测试

```bash
# 用户注册
curl -X POST http://localhost:8080/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "测试用户",
    "email": "<EMAIL>",
    "password": "password123",
    "car_plate": "京A12345"
  }'

# 用户登录
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

## 🔍 开发工具

### 后端开发

```bash
# 实时重载（推荐安装air）
go install github.com/cosmtrek/air@latest
air

# 手动运行
go run cmd/main.go

# 运行测试
go test ./test -v

# 代码格式化
go fmt ./...
```

### 前端开发

```bash
cd frontend

# 安装依赖
npm install

# 开发服务器
npm run dev

# 构建生产版本
npm run build

# 运行测试
npm test

# 代码检查
npm run lint
```

### 数据库管理

```bash
# 连接数据库
docker exec -it app-postgres-dev psql -U postgres -d app_dev

# 查看表结构
\dt

# 查看用户数据
SELECT * FROM users;

# 查看检查站数据
SELECT * FROM checkpoints LIMIT 5;
```

## ⚠️ 常见问题

### 1. 端口被占用

```bash
# 查看端口占用
lsof -i :8080  # 后端
lsof -i :3000  # 前端
lsof -i :5432  # 数据库
lsof -i :6379  # Redis

# 杀死进程
kill -9 <PID>
```

### 2. 数据库连接失败

```bash
# 检查Docker容器状态
docker ps

# 查看数据库日志
docker logs app-postgres-dev

# 重启数据库容器
docker restart app-postgres-dev
```

### 3. Go模块问题

```bash
# 清理模块缓存
go clean -modcache

# 重新下载依赖
go mod download

# 整理依赖
go mod tidy
```

### 4. 前端依赖问题

```bash
cd frontend

# 清理node_modules
rm -rf node_modules package-lock.json

# 重新安装
npm install
```

## 🛠️ 开发配置

### 环境变量

开发环境会自动设置以下环境变量：

```bash
APP_ENV=development
LOG_LEVEL=debug
LOG_FORMAT=text

# 数据库配置
DATABASE_URL="postgresql://postgres:password@localhost:5432/app_dev?sslmode=disable"
DB_HOST=localhost
DB_USER=postgres
DB_PASSWORD=password
DB_NAME=app_dev

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
```

### 自定义配置

如需自定义配置，可以：

1. **修改环境变量**：
   ```bash
   export LOG_LEVEL=info
   export DB_PASSWORD=your_password
   ```

2. **修改配置文件**：
   编辑 `configs/config.yml`

## 🎯 开发流程

### 1. 功能开发

```bash
# 1. 创建功能分支
git checkout -b feature/new-feature

# 2. 开发功能
# 编辑代码...

# 3. 运行测试
go test ./test -v

# 4. 提交代码
git add .
git commit -m "feat: add new feature"

# 5. 推送分支
git push origin feature/new-feature
```

### 2. 数据库变更

```bash
# 1. 创建迁移文件
# 在 internal/infrastructure/migration/ 目录下创建新文件

# 2. 重启应用以应用迁移
./deploy/development/stop.sh
./deploy/development/start.sh

# 3. 验证迁移
docker exec -it app-postgres-dev psql -U postgres -d app_dev -c "\dt"
```

### 3. API开发

```bash
# 1. 定义领域模型（internal/domain/）
# 2. 实现应用服务（internal/application/）
# 3. 添加HTTP处理器（internal/interfaces/http/handlers/）
# 4. 注册路由（internal/interfaces/http/router/）
# 5. 测试API
curl http://localhost:8080/api/your-endpoint
```

## 🚀 下一步

1. ✅ 确认开发环境正常运行
2. 🔧 熟悉项目结构和代码组织
3. 📖 阅读[配置指南](../configuration/CONFIG_GUIDE.md)
4. 🚀 开始功能开发
5. 📱 如需开发小程序，配置微信开发者工具

## 📚 相关文档

- [配置指南](../configuration/CONFIG_GUIDE.md) - 详细配置说明
- [远程部署指南](../deployment/REMOTE_DEPLOYMENT.md) - 生产环境部署
- [本地k3s部署](../deployment/LOCAL_K3S.md) - 本地Kubernetes部署
- [部署重构总结](../../DEPLOY_REFACTOR_SUMMARY.md) - 最新部署结构变更

---

**提示**: 如果遇到问题，请先查看项目根目录的相关文档。