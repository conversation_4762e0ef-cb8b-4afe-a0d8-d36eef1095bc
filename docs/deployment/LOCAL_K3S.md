# 🏠 本地k3s部署指南

本指南介绍如何在本地环境使用k3s部署应用，适合本地测试和开发验证。

## 📋 前提条件

### 系统要求
- **操作系统**: Linux (推荐 Ubuntu 20.04+)
- **内存**: 至少 2GB RAM
- **磁盘**: 至少 10GB 可用空间
- **网络**: 能够访问外部网络下载镜像

### 必需工具
- **curl/wget** - 下载k3s安装脚本
- **Docker/Podman/Buildah** - 构建应用镜像（任选其一）

## 🚀 快速开始

### 1. 安装k3s

```bash
# 安装k3s
./deploy/local-k3s/install-k3s.sh
```

这个脚本会：
- 下载并安装k3s
- 配置kubectl访问
- 验证安装状态
- 创建基础命名空间

### 2. 构建应用镜像

```bash
# 构建镜像
./deploy/local-k3s/build.sh
```

支持的构建工具：
- **Docker** - 最常用的容器构建工具
- **Podman** - 无守护进程的容器工具
- **Buildah** - 专门的镜像构建工具

### 3. 部署应用

```bash
# 部署应用
./deploy/local-k3s/deploy.sh
```

这个脚本会：
- 创建命名空间
- 应用配置和密钥
- 部署PostgreSQL数据库
- 部署Redis缓存
- 部署应用服务
- 验证部署状态

## 🔧 详细步骤

### 安装k3s详解

```bash
# 查看安装选项
./deploy/local-k3s/install-k3s.sh --help

# 基本安装
./deploy/local-k3s/install-k3s.sh

# 验证安装
kubectl get nodes
kubectl get pods -A
```

**安装后的配置：**
- kubeconfig: `~/.kube/config`
- k3s配置: `/etc/rancher/k3s/k3s.yaml`
- 服务状态: `systemctl status k3s`

### 镜像构建详解

```bash
# 查看构建选项
./deploy/local-k3s/build.sh --help

# 使用Docker构建
./deploy/local-k3s/build.sh

# 验证镜像
sudo k3s ctr images list | grep app
```

**构建过程：**
1. 检测可用的构建工具
2. 构建应用镜像
3. 导入到k3s容器运行时
4. 验证镜像可用性

### 应用部署详解

```bash
# 查看部署选项
./deploy/local-k3s/deploy.sh --help

# 执行部署
./deploy/local-k3s/deploy.sh

# 查看部署状态
kubectl get all -n app
```

**部署的组件：**
- **Namespace**: app
- **PostgreSQL**: 数据库服务
- **Redis**: 缓存服务
- **Application**: 主应用服务
- **ConfigMap**: 应用配置
- **Secret**: 敏感信息

## 🌐 访问应用

### 端口转发

```bash
# 转发应用端口
kubectl port-forward -n app svc/app-service 8080:8080

# 在新终端中测试
curl http://localhost:8080/health
```

### 直接访问

```bash
# 获取节点IP
kubectl get nodes -o wide

# 获取服务端口
kubectl get svc -n app

# 访问应用（如果使用NodePort）
curl http://NODE_IP:NODE_PORT/health
```

## 🔍 管理和监控

### 查看资源状态

```bash
# 查看所有资源
kubectl get all -n app

# 查看Pod详情
kubectl describe pod -l app=app -n app

# 查看服务详情
kubectl describe svc app-service -n app
```

### 查看日志

```bash
# 查看应用日志
kubectl logs -f -l app=app -n app

# 查看数据库日志
kubectl logs -f -l app=postgres -n app

# 查看Redis日志
kubectl logs -f -l app=redis -n app
```

### 资源使用情况

```bash
# 查看节点资源
kubectl top nodes

# 查看Pod资源使用
kubectl top pods -n app

# 查看存储使用
kubectl get pv,pvc -n app
```

## 🔄 更新和维护

### 更新应用

```bash
# 重新构建镜像
./deploy/local-k3s/build.sh

# 重启应用
kubectl rollout restart deployment/app -n app

# 查看更新状态
kubectl rollout status deployment/app -n app
```

### 配置更新

```bash
# 编辑ConfigMap
kubectl edit configmap app-config -n app

# 编辑Secret
kubectl edit secret app-secret -n app

# 重启应用以应用新配置
kubectl rollout restart deployment/app -n app
```

### 扩缩容

```bash
# 扩展副本数
kubectl scale deployment app --replicas=3 -n app

# 查看扩展状态
kubectl get pods -l app=app -n app
```

## 🧪 开发和测试

### 开发模式

```bash
# 使用开发镜像标签
export IMAGE_TAG=dev
./deploy/local-k3s/build.sh
./deploy/local-k3s/deploy.sh

# 启用调试日志
kubectl patch deployment app -n app -p '{"spec":{"template":{"spec":{"containers":[{"name":"app","env":[{"name":"LOG_LEVEL","value":"debug"}]}]}}}}'
```

### 数据库操作

```bash
# 连接数据库
kubectl exec -it deployment/postgres -n app -- psql -U postgres -d app

# 查看表结构
kubectl exec -it deployment/postgres -n app -- psql -U postgres -d app -c "\dt"

# 备份数据库
kubectl exec -it deployment/postgres -n app -- pg_dump -U postgres app > backup.sql
```

### Redis操作

```bash
# 连接Redis
kubectl exec -it deployment/redis -n app -- redis-cli

# 查看Redis信息
kubectl exec -it deployment/redis -n app -- redis-cli info

# 清空Redis缓存
kubectl exec -it deployment/redis -n app -- redis-cli flushall
```

## ⚠️ 故障排除

### 常见问题

#### 1. k3s安装失败

```bash
# 检查系统要求
free -h  # 检查内存
df -h    # 检查磁盘空间

# 查看安装日志
sudo journalctl -u k3s

# 重新安装
sudo /usr/local/bin/k3s-uninstall.sh
./deploy/local-k3s/install-k3s.sh
```

#### 2. 镜像构建失败

```bash
# 检查构建工具
docker --version
podman --version
buildah --version

# 手动构建测试
docker build -t app:latest .

# 查看构建日志
docker build --no-cache -t app:latest .
```

#### 3. Pod无法启动

```bash
# 查看Pod状态
kubectl get pods -n app

# 查看Pod详情
kubectl describe pod POD_NAME -n app

# 查看Pod日志
kubectl logs POD_NAME -n app

# 查看事件
kubectl get events -n app --sort-by='.lastTimestamp'
```

#### 4. 服务无法访问

```bash
# 检查服务状态
kubectl get svc -n app

# 检查端点
kubectl get endpoints -n app

# 测试服务连接
kubectl exec -it deployment/app -n app -- wget -qO- http://app-service:8080/health
```

#### 5. 数据库连接问题

```bash
# 检查数据库Pod
kubectl get pods -l app=postgres -n app

# 测试数据库连接
kubectl exec -it deployment/postgres -n app -- pg_isready -U postgres

# 查看数据库日志
kubectl logs -l app=postgres -n app
```

### 重置环境

```bash
# 删除应用
kubectl delete namespace app

# 重新部署
./deploy/local-k3s/deploy.sh

# 完全重置k3s
sudo /usr/local/bin/k3s-uninstall.sh
./deploy/local-k3s/install-k3s.sh
```

## 🔧 自定义配置

### 修改资源配置

编辑 `deploy/local-k3s/manifests/` 目录下的YAML文件：

```bash
# 修改应用配置
vim deploy/local-k3s/manifests/configmap.yaml

# 修改资源限制
vim deploy/local-k3s/manifests/deployment.yaml

# 修改服务配置
vim deploy/local-k3s/manifests/service.yaml
```

### 环境变量配置

```bash
# 在deployment.yaml中添加环境变量
env:
- name: CUSTOM_VAR
  value: "custom_value"
- name: SECRET_VAR
  valueFrom:
    secretKeyRef:
      name: app-secret
      key: SECRET_KEY
```

### 持久化存储

```bash
# 创建PVC
cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-pvc
  namespace: app
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
EOF

# 在postgres.yaml中使用PVC
volumeMounts:
- name: postgres-storage
  mountPath: /var/lib/postgresql/data
volumes:
- name: postgres-storage
  persistentVolumeClaim:
    claimName: postgres-pvc
```

## 🎯 最佳实践

1. **资源限制** - 为所有容器设置合理的资源限制
2. **健康检查** - 配置readiness和liveness探针
3. **配置分离** - 使用ConfigMap和Secret管理配置
4. **日志管理** - 使用结构化日志输出
5. **监控告警** - 配置基础的监控和告警

## 📚 相关文档

- [远程部署指南](REMOTE_DEPLOYMENT.md) - 生产环境部署
- [开发环境指南](../development/QUICK_START.md) - 本地开发环境
- [配置指南](../configuration/CONFIG_GUIDE.md) - 详细配置说明
- [部署重构总结](../../DEPLOY_REFACTOR_SUMMARY.md) - 部署结构变更

---

**提示**: 本地k3s部署适合开发测试，生产环境请使用[远程部署指南](REMOTE_DEPLOYMENT.md)。