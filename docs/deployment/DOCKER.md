# 🐳 Docker部署指南

本指南介绍如何使用Docker和Docker Compose部署应用，适用于开发环境和简单的生产环境。

## 📋 前提条件

### 必需工具
- **Docker 20.10+** - 容器运行时
- **Docker Compose 2.0+** - 容器编排工具
- **Git** - 版本控制

### 系统要求
- **2GB+ 内存** - 推荐4GB以上
- **10GB+ 磁盘空间** - 用于镜像和数据存储

## 🚀 快速开始

### 1. 使用开发环境配置（推荐）

```bash
# 使用新的开发环境部署
./deploy/development/start.sh

# 或者手动使用Docker Compose
docker compose -f deploy/development/docker-compose.yml up -d

# 查看服务状态
docker compose -f deploy/development/docker-compose.yml ps

# 查看日志
docker compose -f deploy/development/docker-compose.yml logs -f
```

### 2. 访问应用

- **数据库**: localhost:5432 (postgres/password)
- **Redis**: localhost:6379
- **应用**: 需要单独启动Go应用

### 3. 启动Go应用

```bash
# 使用开发环境脚本（推荐）
./deploy/development/debug.sh

# 或者手动设置环境变量
export DATABASE_URL="postgresql://postgres:password@localhost:5432/app_dev?sslmode=disable"
export REDIS_URL="redis://localhost:6379"
export APP_ENV=development

# 启动应用
go run cmd/main.go
```

## 🔧 生产环境部署

### 1. 构建生产镜像

```bash
# 构建应用镜像
docker build -t driving-navigation:latest .

# 验证镜像
docker images | grep driving-navigation
```

### 2. 创建生产环境配置

创建 `docker-compose.prod.yml`:

```yaml
version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:13
    container_name: driving-nav-postgres
    environment:
      POSTGRES_DB: driving_navigation
      POSTGRES_USER: driving_nav_user
      POSTGRES_PASSWORD: ${DB_PASSWORD:-driving_nav_2024}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - app-network
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:alpine
    container_name: driving-nav-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - app-network
    restart: unless-stopped

  # 应用服务
  app:
    image: driving-navigation:latest
    container_name: driving-nav-app
    environment:
      - APP_ENV=production
      - LOG_LEVEL=info
      - DATABASE_URL=postgresql://driving_nav_user:${DB_PASSWORD:-driving_nav_2024}@postgres:5432/driving_navigation?sslmode=disable
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - JWT_SECRET=${JWT_SECRET:-driving_nav_jwt_secret_2024}
    ports:
      - "8080:8080"
      - "8081:8081"
    depends_on:
      - postgres
      - redis
    networks:
      - app-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  app-network:
    driver: bridge
```

### 3. 启动生产环境

```bash
# 设置环境变量
export DB_PASSWORD="your_secure_password"
export JWT_SECRET="your_secure_jwt_secret"

# 启动服务
docker compose -f docker-compose.prod.yml up -d

# 查看状态
docker compose -f docker-compose.prod.yml ps
```

## 🌐 访问和测试

### 服务地址

| 服务 | 地址 | 说明 |
|------|------|------|
| API | http://localhost:8080 | 后端API服务 |
| WebSocket | ws://localhost:8081/ws | 实时通信 |
| 数据库 | localhost:5432 | PostgreSQL |
| 缓存 | localhost:6379 | Redis |

### 健康检查

```bash
# API健康检查
curl http://localhost:8080/api/health

# 数据库连接测试
docker exec -it driving-nav-postgres psql -U driving_nav_user -d driving_navigation -c "SELECT version();"

# Redis连接测试
docker exec -it driving-nav-redis redis-cli ping
```

## 🔧 管理命令

### 容器管理

```bash
# 查看运行中的容器
docker ps

# 查看所有容器
docker ps -a

# 停止服务
docker compose -f docker-compose.prod.yml stop

# 启动服务
docker compose -f docker-compose.prod.yml start

# 重启服务
docker compose -f docker-compose.prod.yml restart

# 删除服务
docker compose -f docker-compose.prod.yml down
```

### 日志管理

```bash
# 查看所有服务日志
docker compose -f docker-compose.prod.yml logs -f

# 查看特定服务日志
docker compose -f docker-compose.prod.yml logs -f app
docker compose -f docker-compose.prod.yml logs -f postgres
docker compose -f docker-compose.prod.yml logs -f redis

# 查看最近的日志
docker compose -f docker-compose.prod.yml logs --tail=100 app
```

### 进入容器

```bash
# 进入应用容器
docker exec -it driving-nav-app /bin/sh

# 进入数据库容器
docker exec -it driving-nav-postgres psql -U driving_nav_user -d driving_navigation

# 进入Redis容器
docker exec -it driving-nav-redis redis-cli
```

## 🔄 更新部署

### 应用更新

```bash
# 1. 拉取最新代码
git pull origin main

# 2. 重新构建镜像
docker build -t driving-navigation:latest .

# 3. 重启应用容器
docker compose -f docker-compose.prod.yml up -d app

# 4. 验证更新
curl http://localhost:8080/api/health
```

### 配置更新

```bash
# 1. 修改配置文件
vim configs/config.yml

# 2. 重新构建镜像（如果配置文件打包在镜像中）
docker build -t driving-navigation:latest .

# 3. 重启服务
docker compose -f docker-compose.prod.yml restart app
```

## 🔍 监控和维护

### 资源监控

```bash
# 查看容器资源使用
docker stats

# 查看特定容器资源使用
docker stats driving-nav-app driving-nav-postgres driving-nav-redis

# 查看镜像大小
docker images | grep driving-navigation
```

### 数据备份

```bash
# 备份数据库
docker exec driving-nav-postgres pg_dump -U driving_nav_user driving_navigation > backup.sql

# 备份Redis数据
docker exec driving-nav-redis redis-cli BGSAVE
docker cp driving-nav-redis:/data/dump.rdb ./redis-backup.rdb

# 备份数据卷
docker run --rm -v driving-navigation_postgres_data:/data -v $(pwd):/backup alpine tar czf /backup/postgres-backup.tar.gz /data
```

### 数据恢复

```bash
# 恢复数据库
docker exec -i driving-nav-postgres psql -U driving_nav_user driving_navigation < backup.sql

# 恢复Redis数据
docker cp ./redis-backup.rdb driving-nav-redis:/data/dump.rdb
docker restart driving-nav-redis
```

## 🐛 故障排除

### 常见问题

#### 1. 容器启动失败

```bash
# 查看容器状态
docker ps -a

# 查看容器日志
docker logs driving-nav-app

# 查看详细错误信息
docker compose -f docker-compose.prod.yml logs app
```

#### 2. 数据库连接失败

```bash
# 检查数据库容器状态
docker ps | grep postgres

# 查看数据库日志
docker logs driving-nav-postgres

# 测试数据库连接
docker exec -it driving-nav-postgres psql -U driving_nav_user -d driving_navigation
```

#### 3. 端口冲突

```bash
# 查看端口占用
lsof -i :8080
lsof -i :5432
lsof -i :6379

# 修改端口映射
# 编辑 docker-compose.prod.yml 中的 ports 配置
```

#### 4. 磁盘空间不足

```bash
# 清理未使用的镜像
docker image prune -f

# 清理未使用的容器
docker container prune -f

# 清理未使用的卷
docker volume prune -f

# 清理所有未使用的资源
docker system prune -f
```

### 重置环境

```bash
# 停止并删除所有容器
docker compose -f docker-compose.prod.yml down

# 删除数据卷（注意：会丢失数据）
docker compose -f docker-compose.prod.yml down -v

# 重新启动
docker compose -f docker-compose.prod.yml up -d
```

## 🔐 安全配置

### 1. 环境变量管理

创建 `.env` 文件：

```bash
# .env
DB_PASSWORD=your_secure_database_password
JWT_SECRET=your_secure_jwt_secret
REDIS_PASSWORD=your_redis_password
```

更新 `docker-compose.prod.yml`:

```yaml
services:
  postgres:
    environment:
      POSTGRES_PASSWORD: ${DB_PASSWORD}
  
  redis:
    command: redis-server --requirepass ${REDIS_PASSWORD}
  
  app:
    environment:
      - DB_PASSWORD=${DB_PASSWORD}
      - JWT_SECRET=${JWT_SECRET}
      - REDIS_PASSWORD=${REDIS_PASSWORD}
```

### 2. 网络安全

```yaml
# 限制网络访问
services:
  postgres:
    ports: []  # 不暴露端口到主机
    networks:
      - app-network
  
  redis:
    ports: []  # 不暴露端口到主机
    networks:
      - app-network
```

### 3. 容器安全

```yaml
services:
  app:
    user: "1000:1000"  # 使用非root用户
    read_only: true     # 只读文件系统
    tmpfs:
      - /tmp
    security_opt:
      - no-new-privileges:true
```

## 📊 性能优化

### 1. 资源限制

```yaml
services:
  app:
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M
```

### 2. 健康检查

```yaml
services:
  app:
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
```

### 3. 日志配置

```yaml
services:
  app:
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
```

## 🔄 CI/CD集成

### GitHub Actions示例

```yaml
# .github/workflows/docker-deploy.yml
name: Docker Deploy
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Build Docker image
        run: docker build -t driving-navigation:latest .
      
      - name: Deploy with Docker Compose
        run: |
          docker compose -f docker-compose.prod.yml down
          docker compose -f docker-compose.prod.yml up -d
        env:
          DB_PASSWORD: ${{ secrets.DB_PASSWORD }}
          JWT_SECRET: ${{ secrets.JWT_SECRET }}
```

## 📚 相关文档

- [本地开发指南](../development/QUICK_START.md) - 开发环境搭建
- [远程部署指南](REMOTE_DEPLOYMENT.md) - 云服务器部署
- [配置指南](../configuration/CONFIG_GUIDE.md) - 配置说明

## 💡 最佳实践

1. **使用多阶段构建** - 减小镜像大小
2. **设置健康检查** - 确保服务可用性
3. **限制资源使用** - 防止资源耗尽
4. **定期备份数据** - 防止数据丢失
5. **监控日志大小** - 防止磁盘空间不足
6. **使用环境变量** - 管理敏感信息
7. **网络隔离** - 提高安全性

---

**提示**: Docker部署适用于开发和小规模生产环境，大规模生产环境建议使用[k3s部署](LOCAL_K3S.md)或[远程部署](REMOTE_DEPLOYMENT.md)。