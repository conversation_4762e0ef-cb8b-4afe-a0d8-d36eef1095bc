# 🚀 远程部署指南

本指南介绍如何将应用部署到预装k3s的云服务器。

## 📋 前提条件

### 服务器要求
- ✅ **k3s已安装并运行** - Kubernetes轻量级发行版
- ✅ **SSH访问权限** - 支持密钥或密码认证
- ✅ **足够资源** - 至少2GB内存，20GB磁盘空间
- ✅ **网络连接** - 能够访问外部网络下载镜像

### 本地要求
- ✅ **SSH客户端** - 用于远程连接
- ✅ **项目代码** - 最新版本的项目代码

## 🚀 快速部署

### 方法1: 简化部署（推荐）

```bash
# 基本部署
./deploy/remote/simple-deploy.sh -H ************* --all

# 使用SSH密钥
./deploy/remote/simple-deploy.sh -H server.com -u ubuntu -k ~/.ssh/id_rsa --all
```

### 方法2: Ansible部署（功能更强大）

```bash
# 配置服务器信息
vim deploy/remote/inventory.yml

# 完整部署
./deploy/remote/deploy.sh --all
```

## 📝 部署步骤详解

### 1. 简化部署流程

简化部署脚本会自动执行以下步骤：

```bash
./deploy/remote/simple-deploy.sh -H YOUR_SERVER_IP --all
```

**自动执行的操作：**
1. **SSH连接测试** - 验证服务器连接
2. **k3s状态检查** - 确认k3s服务正常
3. **项目文件上传** - 上传代码到服务器
4. **kubectl配置** - 设置Kubernetes访问
5. **创建命名空间** - 创建应用命名空间
6. **生产环境配置** - 生成环境配置文件
7. **Docker镜像构建** - 在服务器上构建镜像
8. **数据库部署** - 部署PostgreSQL
9. **缓存部署** - 部署Redis
10. **应用部署** - 部署主应用
11. **部署验证** - 验证部署状态

### 2. Ansible部署选项

```bash
./deploy/remote/deploy.sh [选项]

选项:
  --check                   检查服务器连接
  --install-k3s             安装k3s集群
  --deploy-app              部署应用
  --all                     执行完整部署流程
  -h, --help                显示帮助信息
```

### 3. 简化部署选项

```bash
./deploy/remote/simple-deploy.sh [选项]

选项:
  -H, --host HOST           远程主机地址 (必需)
  -u, --user USER           SSH用户名 [默认: root]
  -k, --key PATH            SSH私钥路径
  -p, --port PORT           SSH端口 [默认: 22]
  --install-k3s             安装k3s (如果未安装)
  --deploy-app              部署应用
  --all                     执行完整部署流程
  --check                   仅检查连接
```

## 🔧 部署配置

### 生产环境配置

部署脚本会自动创建生产环境配置：

```yaml
# 应用配置
APP_ENV=production
LOG_LEVEL=info
LOG_FORMAT=json

# 数据库配置
DB_HOST=postgres-service
DB_PORT=5432
DB_NAME=driving_navigation
DB_USER=driving_nav_user
DB_PASSWORD=driving_nav_2024_prod

# Redis配置
REDIS_HOST=redis-service
REDIS_PORT=6379

# JWT配置
JWT_SECRET=driving_nav_jwt_secret_2024_prod
```

### 自定义配置

如需自定义配置，可以在部署前修改：

```bash
# 在服务器上编辑配置
ssh user@server
cd /opt/driving-navigation
vim .env.prod
```

## 🌐 访问应用

### 获取访问地址

部署完成后，脚本会显示访问信息：

```bash
=== 部署完成 ===

服务访问地址:
  API: http://NODE_IP:30080
  WebSocket: ws://NODE_IP:30081/ws
  健康检查: http://NODE_IP:30080/api/health
```

### 端口说明

- **30080** - HTTP API端口
- **30081** - WebSocket端口

### 测试访问

```bash
# 健康检查
curl http://YOUR_SERVER_IP:30080/api/health

# 获取检查站列表
curl http://YOUR_SERVER_IP:30080/api/checkpoints/
```

## 🔍 部署验证

### 检查部署状态

```bash
# SSH连接到服务器
ssh user@your-server

# 查看所有资源
kubectl get all -n driving-navigation

# 查看Pod状态
kubectl get pods -n driving-navigation

# 查看服务状态
kubectl get services -n driving-navigation
```

### 查看应用日志

```bash
# 查看应用日志
kubectl logs -f -l app=driving-navigation -n driving-navigation

# 查看数据库日志
kubectl logs -f -l app=postgres -n driving-navigation

# 查看Redis日志
kubectl logs -f -l app=redis -n driving-navigation
```

## 🔄 更新部署

### 快速更新

当代码有更新时：

```bash
# 方法1: 使用简化脚本（推荐）
./deploy/remote/simple-deploy.sh -H YOUR_SERVER_IP --deploy-app

# 方法2: 使用Ansible脚本
./deploy/remote/deploy.sh --deploy-app
```

### 手动更新

```bash
# SSH到服务器
ssh user@your-server
cd /opt/driving-navigation

# 拉取最新代码
git pull origin main

# 重新构建镜像
docker build -t driving-navigation:latest .

# 重启应用
kubectl rollout restart deployment/driving-navigation -n driving-navigation
```

## ⚠️ 故障排除

### 常见问题

#### 1. SSH连接失败

```bash
# 检查SSH连接
ssh -v user@your-server

# 检查SSH密钥权限
chmod 600 ~/.ssh/id_rsa

# 检查防火墙
# 确保SSH端口(22)开放
```

#### 2. k3s服务未运行

```bash
# 在服务器上检查k3s状态
sudo systemctl status k3s

# 启动k3s服务
sudo systemctl start k3s

# 设置开机自启
sudo systemctl enable k3s
```

#### 3. Pod无法启动

```bash
# 查看Pod详情
kubectl describe pod POD_NAME -n driving-navigation

# 查看事件
kubectl get events -n driving-navigation --sort-by='.lastTimestamp'

# 查看日志
kubectl logs POD_NAME -n driving-navigation
```

#### 4. 镜像构建失败

```bash
# 检查Docker状态
docker info

# 手动构建镜像
docker build -t driving-navigation:latest .

# 查看构建日志
docker build --no-cache -t driving-navigation:latest .
```

#### 5. 数据库连接失败

```bash
# 检查数据库Pod
kubectl get pods -l app=postgres -n driving-navigation

# 查看数据库日志
kubectl logs -l app=postgres -n driving-navigation

# 进入数据库容器测试
kubectl exec -it deployment/postgres -n driving-navigation -- psql -U driving_nav_user -d driving_navigation
```

### 重置部署

如果需要完全重新部署：

```bash
# 清理所有资源
kubectl delete namespace driving-navigation

# 重新部署
./deploy/remote/simple-deploy.sh -H YOUR_SERVER_IP --all
```

## 🔐 安全建议

### 1. SSH安全

```bash
# 使用SSH密钥而不是密码
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"
ssh-copy-id user@your-server

# 禁用密码登录（在服务器上）
sudo vim /etc/ssh/sshd_config
# 设置: PasswordAuthentication no
sudo systemctl restart sshd
```

### 2. 更换默认密码

```bash
# 生成安全密码
openssl rand -base64 32

# 更新数据库密码
kubectl exec -it deployment/postgres -n driving-navigation -- psql -U postgres -c "ALTER USER driving_nav_user WITH PASSWORD 'new_secure_password';"

# 更新应用配置
kubectl patch deployment driving-navigation -n driving-navigation -p '{"spec":{"template":{"spec":{"containers":[{"name":"driving-navigation","env":[{"name":"DB_PASSWORD","value":"new_secure_password"}]}]}}}}'
```

### 3. 网络安全

```bash
# 配置防火墙（示例：ufw）
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 30080/tcp # API
sudo ufw allow 30081/tcp # WebSocket
sudo ufw enable
```

## 📊 监控和维护

### 资源监控

```bash
# 查看节点资源使用
kubectl top nodes

# 查看Pod资源使用
kubectl top pods -n driving-navigation

# 查看存储使用
kubectl get pv,pvc -n driving-navigation
```

### 日志管理

```bash
# 查看实时日志
kubectl logs -f deployment/driving-navigation -n driving-navigation

# 导出日志
kubectl logs deployment/driving-navigation -n driving-navigation > app.log
```

### 备份数据

```bash
# 备份数据库
kubectl exec -it deployment/postgres -n driving-navigation -- pg_dump -U driving_nav_user driving_navigation > backup.sql

# 恢复数据库
kubectl exec -i deployment/postgres -n driving-navigation -- psql -U driving_nav_user driving_navigation < backup.sql
```

## 🎯 最佳实践

1. **定期备份** - 定期备份数据库和配置
2. **监控资源** - 监控CPU、内存和磁盘使用
3. **更新镜像** - 定期更新基础镜像和依赖
4. **安全审计** - 定期检查安全配置
5. **日志分析** - 定期分析应用日志

## 📞 获取帮助

如果遇到问题：

1. **查看日志**: `kubectl logs -f -l app=driving-navigation -n driving-navigation`
2. **检查状态**: `kubectl get all -n driving-navigation`
3. **查看事件**: `kubectl get events -n driving-navigation`
4. **重新部署**: `./deploy/remote/simple-deploy.sh -H YOUR_SERVER_IP --all`

---

**相关文档:**
- [本地k3s部署](LOCAL_K3S.md)
- [配置指南](../configuration/CONFIG_GUIDE.md)
- [部署重构总结](../../DEPLOY_REFACTOR_SUMMARY.md)