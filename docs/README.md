# 驾驶导航避让系统

外地车进京导航避让系统，基于领域驱动设计（DDD）的全栈应用程序，支持Web管理后台和微信小程序。

## ✨ 项目特点

- 🚗 **智能避让**: 实时检查站数据，智能路线规划
- 📱 **多端支持**: Web管理后台 + 微信小程序
- 🏗️ **领域驱动设计**: 清晰的DDD架构，分层设计
- 🌐 **全栈集成**: Go后端 + React前端 + 微信小程序
- ⚡ **实时更新**: WebSocket实时推送检查站状态变化
- 🚀 **云原生**: 支持k3s/Kubernetes部署，容器化架构
- 📦 **多种部署方式**: 开发环境、本地k3s、远程部署三种选择

## 📁 项目结构

```
.
├── cmd/                    # 应用程序入口
├── configs/                # 统一配置文件
│   └── config.yml         # 主配置文件（支持环境变量）
├── frontend/               # React Web管理后台
├── miniprogram/            # 微信小程序
├── internal/               # 内部包 (DDD架构)
│   ├── application/        # 应用层
│   ├── domain/            # 领域层
│   ├── infrastructure/    # 基础设施层
│   └── interfaces/        # 接口层
├── deploy/                 # 部署配置（重构后）
│   ├── development/       # 开发环境部署
│   ├── local-k3s/         # 本地k3s部署
│   └── remote/            # 远程部署
├── test/                  # 测试文件
└── docs/                  # 项目文档
    ├── development/       # 开发文档
    ├── deployment/        # 部署文档
    └── configuration/     # 配置文档
```

## 🚀 快速开始

### 开发环境

```bash
# 启动完整开发环境（推荐）
./deploy/development/start.sh

# 仅启动后端调试
./deploy/development/debug.sh

# 停止开发环境
./deploy/development/stop.sh
```

### 本地k3s部署

```bash
# 安装k3s
./deploy/local-k3s/install-k3s.sh

# 构建镜像
./deploy/local-k3s/build.sh

# 部署应用
./deploy/local-k3s/deploy.sh
```

### 远程部署

```bash
# 简单部署（推荐，仅需SSH）
./deploy/remote/simple-deploy.sh -H ************* --all

# Ansible部署（功能更强大）
./deploy/remote/deploy.sh --all
```

**访问地址:**
- **后端API**: http://localhost:8080
- **WebSocket**: ws://localhost:8081/ws
- **健康检查**: http://localhost:8080/api/health

## 📚 文档导航

### 🛠️ 开发文档
- [快速开始指南](development/QUICK_START.md) - 本地开发环境搭建
- [本地调试解决方案](LOCAL_DEBUG_SOLUTION.md) - 常见问题解决

### 🚀 部署文档
- [远程部署指南](deployment/REMOTE_DEPLOYMENT.md) - 云服务器部署
- [本地k3s部署](deployment/LOCAL_K3S.md) - 本地Kubernetes部署
- [Docker部署](deployment/DOCKER.md) - Docker容器部署

### ⚙️ 配置文档
- [配置指南](configuration/CONFIG_GUIDE.md) - 完整配置说明
- [环境变量](configuration/ENVIRONMENT.md) - 环境变量配置
- [数据库配置](configuration/DATABASE.md) - 数据库设置

### 📋 项目管理
- [项目重组总结](PROJECT_REORGANIZATION.md) - 最新项目结构变更

## 🔧 技术栈

### 后端
- **语言**: Go 1.24+
- **框架**: Gin (HTTP) + GORM (ORM)
- **数据库**: PostgreSQL 13+
- **缓存**: Redis
- **架构**: 领域驱动设计 (DDD)

### 前端
- **Web管理后台**: React + TypeScript + Vite
- **小程序**: 微信小程序原生开发
- **UI组件**: Ant Design / 微信小程序组件

### 部署
- **容器化**: Docker
- **编排**: Kubernetes / k3s
- **监控**: Prometheus + Grafana
- **日志**: 结构化日志 + 可选Loki

## 🌍 环境支持

### 开发环境
- 本地Docker开发环境
- 热重载和实时调试
- 集成测试支持

### 生产环境
- k3s/Kubernetes集群部署
- 高可用和自动扩缩容
- 完整的监控和日志系统

## 🔐 安全特性

- JWT身份认证
- 数据库连接加密
- API访问控制
- 敏感信息环境变量管理

## 📊 监控和运维

- 健康检查端点
- Prometheus指标收集
- 结构化日志输出
- 容器化部署和管理

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](../LICENSE) 文件了解详情。

## 📞 支持

如果遇到问题，请：

1. 查看相关文档
2. 检查 [Issues](https://github.com/your-repo/issues)
3. 创建新的 Issue

---

**快速链接:**
- [开发环境搭建](development/QUICK_START.md)
- [远程部署指南](deployment/REMOTE_DEPLOYMENT.md)
- [配置说明](configuration/CONFIG_GUIDE.md)
- [脚本使用说明](../deploy/scripts/README.md)