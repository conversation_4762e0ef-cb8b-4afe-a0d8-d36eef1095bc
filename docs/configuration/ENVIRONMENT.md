# 🌍 环境变量配置

本文档详细说明了所有支持的环境变量及其用法。

## 📋 环境变量概述

项目支持通过环境变量覆盖所有配置项，实现不同环境的灵活配置。环境变量优先级高于配置文件中的默认值。

## 🔧 核心环境变量

### 应用配置

| 变量名 | 默认值 | 说明 | 示例 |
|--------|--------|------|------|
| `APP_ENV` | `production` | 运行环境 | `development`, `production` |
| `APP_PORT` | `8080` | HTTP服务端口 | `8080`, `3000` |

```bash
# 开发环境
export APP_ENV=development
export APP_PORT=8080

# 生产环境
export APP_ENV=production
export APP_PORT=8080
```

### 数据库配置

| 变量名 | 默认值 | 说明 | 示例 |
|--------|--------|------|------|
| `DATABASE_URL` | - | 完整数据库连接字符串（优先级最高） | `********************************/db` |
| `DB_HOST` | `postgres-service` | 数据库主机 | `localhost`, `postgres-service` |
| `DB_PORT` | `5432` | 数据库端口 | `5432` |
| `DB_NAME` | `driving_navigation` | 数据库名称 | `driving_navigation` |
| `DB_USER` | `driving_nav_user` | 数据库用户名 | `postgres`, `driving_nav_user` |
| `DB_PASSWORD` | `driving_nav_2024` | 数据库密码 | `your_secure_password` |
| `DB_SSL_MODE` | `disable` | SSL模式 | `disable`, `require`, `verify-full` |

```bash
# 方式1: 使用完整URL（推荐）
export DATABASE_URL="postgresql://postgres:password@localhost:5432/app_dev?sslmode=disable"

# 方式2: 使用分离的变量
export DB_HOST=localhost
export DB_PORT=5432
export DB_NAME=app_dev
export DB_USER=postgres
export DB_PASSWORD=password
export DB_SSL_MODE=disable
```

### 数据库连接池配置

| 变量名 | 默认值 | 说明 | 示例 |
|--------|--------|------|------|
| `DB_MAX_OPEN` | `25` | 最大连接数 | `25`, `50` |
| `DB_MAX_IDLE` | `5` | 最大空闲连接数 | `5`, `10` |
| `DB_MAX_LIFETIME` | `300s` | 连接最大生存时间 | `300s`, `600s` |

```bash
export DB_MAX_OPEN=25
export DB_MAX_IDLE=5
export DB_MAX_LIFETIME=300s
```

### JWT配置

| 变量名 | 默认值 | 说明 | 示例 |
|--------|--------|------|------|
| `JWT_SECRET` | `driving_nav_jwt_secret_2024` | JWT签名密钥 | `your_secure_jwt_secret` |

```bash
# 生成安全的JWT密钥
export JWT_SECRET=$(openssl rand -base64 64)
```

### Redis配置

| 变量名 | 默认值 | 说明 | 示例 |
|--------|--------|------|------|
| `REDIS_HOST` | `redis-service` | Redis主机 | `localhost`, `redis-service` |
| `REDIS_PORT` | `6379` | Redis端口 | `6379` |
| `REDIS_PASSWORD` | - | Redis密码 | `your_redis_password` |
| `REDIS_DB` | `0` | Redis数据库编号 | `0`, `1` |

```bash
export REDIS_HOST=localhost
export REDIS_PORT=6379
export REDIS_PASSWORD=""
export REDIS_DB=0
```

## 📝 日志配置

| 变量名 | 默认值 | 说明 | 可选值 |
|--------|--------|------|--------|
| `LOG_LEVEL` | `info` | 日志级别 | `debug`, `info`, `warn`, `error` |
| `LOG_FORMAT` | `json` | 日志格式 | `json`, `text` |
| `LOG_OUTPUT` | `stdout` | 输出目标 | `stdout`, `file`, `both` |
| `LOG_FILE_PATH` | `/var/log/app/app.log` | 日志文件路径 | `/path/to/logfile.log` |
| `LOG_MAX_SIZE` | `100` | 日志文件最大大小(MB) | `100`, `200` |
| `LOG_MAX_BACKUPS` | `5` | 保留日志文件数 | `5`, `10` |
| `LOG_MAX_AGE` | `30` | 日志保留天数 | `30`, `60` |
| `LOG_COMPRESS` | `true` | 是否压缩旧日志 | `true`, `false` |

```bash
# 开发环境日志配置
export LOG_LEVEL=debug
export LOG_FORMAT=text
export LOG_OUTPUT=stdout

# 生产环境日志配置
export LOG_LEVEL=info
export LOG_FORMAT=json
export LOG_OUTPUT=both
export LOG_FILE_PATH=/var/log/app/app.log
```

## 🗺️ 高德地图API配置

| 变量名 | 默认值 | 说明 | 示例 |
|--------|--------|------|------|
| `AMAP_API_KEY` | - | 高德地图API密钥 | `your_amap_api_key` |
| `AMAP_WEB_SERVICE_KEY` | - | 高德地图Web服务密钥 | `your_web_service_key` |
| `AMAP_BASE_URL` | `https://restapi.amap.com` | API基础URL | `https://restapi.amap.com` |
| `AMAP_TIMEOUT` | `30s` | 请求超时时间 | `30s`, `60s` |
| `AMAP_RATE_LIMIT` | `10` | 每秒请求数限制 | `10`, `20` |
| `AMAP_BURST` | `20` | 突发请求数限制 | `20`, `50` |

```bash
export AMAP_API_KEY=your_amap_api_key
export AMAP_WEB_SERVICE_KEY=your_web_service_key
export AMAP_TIMEOUT=30s
```

## 🔌 WebSocket配置

| 变量名 | 默认值 | 说明 | 示例 |
|--------|--------|------|------|
| `WEBSOCKET_ENABLED` | `true` | 是否启用WebSocket | `true`, `false` |
| `WEBSOCKET_PORT` | `8081` | WebSocket端口 | `8081`, `8082` |
| `WEBSOCKET_PATH` | `/ws` | WebSocket路径 | `/ws`, `/websocket` |
| `WS_MAX_CONNECTIONS` | `1000` | 最大连接数 | `1000`, `5000` |
| `WS_READ_BUFFER` | `1024` | 读缓冲区大小 | `1024`, `2048` |
| `WS_WRITE_BUFFER` | `1024` | 写缓冲区大小 | `1024`, `2048` |
| `WS_PING_PERIOD` | `54s` | Ping间隔 | `54s`, `30s` |
| `WS_PONG_WAIT` | `60s` | Pong等待时间 | `60s`, `90s` |
| `WS_WRITE_WAIT` | `10s` | 写超时时间 | `10s`, `15s` |
| `WS_MAX_MESSAGE_SIZE` | `512` | 最大消息大小 | `512`, `1024` |

```bash
export WEBSOCKET_ENABLED=true
export WEBSOCKET_PORT=8081
export WS_MAX_CONNECTIONS=1000
```

## 📊 监控配置

| 变量名 | 默认值 | 说明 | 示例 |
|--------|--------|------|------|
| `METRICS_ENABLED` | `true` | 是否启用指标收集 | `true`, `false` |
| `METRICS_PORT` | `9090` | 指标服务端口 | `9090`, `9091` |
| `METRICS_PATH` | `/metrics` | 指标路径 | `/metrics`, `/prometheus` |
| `LOKI_ENABLED` | `false` | 是否启用Loki日志 | `true`, `false` |
| `LOKI_ENDPOINT` | `http://loki:3100` | Loki服务端点 | `http://loki:3100` |
| `TRACING_ENABLED` | `false` | 是否启用链路追踪 | `true`, `false` |
| `JAEGER_ENDPOINT` | `http://jaeger:14268/api/traces` | Jaeger端点 | `http://jaeger:14268/api/traces` |

```bash
export METRICS_ENABLED=true
export METRICS_PORT=9090
export LOKI_ENABLED=false
export TRACING_ENABLED=false
```

## 🔔 推送通知配置

### Firebase (Android)

| 变量名 | 默认值 | 说明 | 示例 |
|--------|--------|------|------|
| `FIREBASE_ENABLED` | `false` | 是否启用Firebase推送 | `true`, `false` |
| `FIREBASE_CREDENTIALS` | `./configs/firebase-credentials.json` | 凭证文件路径 | `/path/to/credentials.json` |

### APNs (iOS)

| 变量名 | 默认值 | 说明 | 示例 |
|--------|--------|------|------|
| `APNS_ENABLED` | `false` | 是否启用APNs推送 | `true`, `false` |
| `APNS_KEY_FILE` | `./configs/apns-key.p8` | 密钥文件路径 | `/path/to/key.p8` |
| `APNS_KEY_ID` | - | 密钥ID | `your_key_id` |
| `APNS_TEAM_ID` | - | 团队ID | `your_team_id` |
| `APNS_BUNDLE_ID` | `com.example.beijingnavigation` | Bundle ID | `com.yourapp.bundle` |
| `APNS_PRODUCTION` | `false` | 是否生产环境 | `true`, `false` |

### 微信小程序

| 变量名 | 默认值 | 说明 | 示例 |
|--------|--------|------|------|
| `MINIPROGRAM_PUSH_ENABLED` | `false` | 是否启用小程序推送 | `true`, `false` |
| `MINIPROGRAM_APP_ID` | - | 小程序AppID | `your_app_id` |
| `MINIPROGRAM_APP_SECRET` | - | 小程序AppSecret | `your_app_secret` |

```bash
# Firebase配置
export FIREBASE_ENABLED=true
export FIREBASE_CREDENTIALS=/path/to/firebase-credentials.json

# APNs配置
export APNS_ENABLED=true
export APNS_KEY_FILE=/path/to/apns-key.p8
export APNS_KEY_ID=your_key_id
export APNS_TEAM_ID=your_team_id
export APNS_BUNDLE_ID=com.yourapp.bundle
export APNS_PRODUCTION=false

# 微信小程序配置
export MINIPROGRAM_PUSH_ENABLED=true
export MINIPROGRAM_APP_ID=your_app_id
export MINIPROGRAM_APP_SECRET=your_app_secret
```

## 🎯 环境配置模板

### 开发环境 (.env.development)

```bash
# 应用配置
APP_ENV=development
APP_PORT=8080
LOG_LEVEL=debug
LOG_FORMAT=text

# 数据库配置
DATABASE_URL="postgresql://postgres:password@localhost:5432/app_dev?sslmode=disable"
DB_HOST=localhost
DB_USER=postgres
DB_PASSWORD=password
DB_NAME=app_dev
DB_SSL_MODE=disable

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=""

# JWT配置
JWT_SECRET=dev_jwt_secret_key

# 高德地图API（开发环境可使用测试密钥）
AMAP_API_KEY=your_dev_api_key
AMAP_WEB_SERVICE_KEY=your_dev_web_service_key

# WebSocket配置
WEBSOCKET_ENABLED=true
WEBSOCKET_PORT=8081

# 监控配置
METRICS_ENABLED=true
METRICS_PORT=9090
```

### 生产环境 (.env.production)

```bash
# 应用配置
APP_ENV=production
APP_PORT=8080
LOG_LEVEL=info
LOG_FORMAT=json
LOG_OUTPUT=both

# 数据库配置
DATABASE_URL="*******************************************************************/driving_navigation?sslmode=require"
DB_HOST=postgres-service
DB_USER=driving_nav_user
DB_PASSWORD=secure_password
DB_NAME=driving_navigation
DB_SSL_MODE=require

# Redis配置
REDIS_HOST=redis-service
REDIS_PORT=6379
REDIS_PASSWORD=secure_redis_password

# JWT配置
JWT_SECRET=secure_jwt_secret_key

# 高德地图API
AMAP_API_KEY=your_production_api_key
AMAP_WEB_SERVICE_KEY=your_production_web_service_key

# WebSocket配置
WEBSOCKET_ENABLED=true
WEBSOCKET_PORT=8081
WS_MAX_CONNECTIONS=5000

# 监控配置
METRICS_ENABLED=true
METRICS_PORT=9090
LOKI_ENABLED=true
LOKI_ENDPOINT=http://loki:3100

# 推送通知
FIREBASE_ENABLED=true
FIREBASE_CREDENTIALS=/etc/secrets/firebase-credentials.json
APNS_ENABLED=true
APNS_KEY_FILE=/etc/secrets/apns-key.p8
APNS_PRODUCTION=true
```

## 🔧 环境变量管理

### 1. 使用.env文件

```bash
# 创建环境配置文件
cp .env.example .env.development
cp .env.example .env.production

# 加载环境变量
source .env.development
```

### 2. 使用direnv（推荐）

```bash
# 安装direnv
# macOS: brew install direnv
# Ubuntu: apt install direnv

# 创建.envrc文件
echo "source .env.development" > .envrc

# 允许direnv加载
direnv allow
```

### 3. Docker环境变量

```yaml
# docker-compose.yml
services:
  app:
    environment:
      - APP_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - JWT_SECRET=${JWT_SECRET}
    env_file:
      - .env.production
```

### 4. Kubernetes环境变量

```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
spec:
  template:
    spec:
      containers:
      - name: app
        env:
        - name: APP_ENV
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: database-url
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: jwt-secret
```

## 🔐 安全最佳实践

### 1. 敏感信息管理

```bash
# 不要在代码中硬编码敏感信息
# ❌ 错误做法
export JWT_SECRET=hardcoded_secret

# ✅ 正确做法
export JWT_SECRET=$(cat /run/secrets/jwt_secret)
```

### 2. 环境变量验证

```bash
# 启动前验证必需的环境变量
required_vars=("DATABASE_URL" "JWT_SECRET" "AMAP_API_KEY")
for var in "${required_vars[@]}"; do
    if [[ -z "${!var}" ]]; then
        echo "Error: $var is not set"
        exit 1
    fi
done
```

### 3. 权限控制

```bash
# 设置环境文件权限
chmod 600 .env.production
chown app:app .env.production
```

## 🔍 故障排除

### 检查环境变量

```bash
# 查看所有相关环境变量
env | grep -E "(APP_|DB_|REDIS_|JWT_|AMAP_|LOG_)"

# 检查特定变量
echo "DATABASE_URL: $DATABASE_URL"
echo "JWT_SECRET: ${JWT_SECRET:0:10}..." # 只显示前10个字符
```

### 环境变量调试

```bash
# 启用配置调试
export CONFIG_DEBUG=true
export LOG_LEVEL=debug

# 运行应用查看配置加载过程
go run cmd/main.go
```

### 常见问题

1. **环境变量未生效**
   - 检查变量名拼写
   - 确认变量已正确导出
   - 重启应用进程

2. **配置文件语法错误**
   - 检查YAML语法
   - 验证环境变量格式

3. **权限问题**
   - 检查文件权限
   - 确认用户有读取权限

## 📚 相关文档

- [配置指南](CONFIG_GUIDE.md) - 完整配置说明
- [数据库配置](DATABASE.md) - 数据库配置详解
- [开发指南](../development/QUICK_START.md) - 开发环境设置

---

**提示**: 生产环境中的敏感信息（如密码、API密钥）应该通过安全的方式管理，避免在代码仓库中存储明文。