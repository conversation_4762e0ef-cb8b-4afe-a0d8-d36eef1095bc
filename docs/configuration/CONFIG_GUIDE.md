# ⚙️ 配置指南

本文档详细说明了驾驶导航避让系统的配置方式和所有配置项。

## 📋 配置概述

项目使用统一的配置文件 `configs/config.yml`，支持通过环境变量覆盖所有配置项，实现开发和生产环境的灵活切换。

### 配置特点

- ✅ **统一配置文件** - 单一配置文件支持所有环境
- ✅ **环境变量支持** - 所有配置项都支持环境变量覆盖
- ✅ **默认值机制** - 提供合理的默认配置
- ✅ **类型安全** - 自动类型转换和验证

## 🔧 配置文件结构

### 主配置文件

位置：`configs/config.yml`

```yaml
# 应用基础配置
app:
  name: driving-navigation
  port: ${APP_PORT:8080}
  env: ${APP_ENV:production}

# 数据库配置
database:
  url: ${DATABASE_URL:}
  host: ${DB_HOST:postgres-service}
  port: ${DB_PORT:5432}
  name: ${DB_NAME:driving_navigation}
  user: ${DB_USER:driving_nav_user}
  password: ${DB_PASSWORD:driving_nav_2024}
  ssl_mode: ${DB_SSL_MODE:disable}
  
  pool:
    max_open: ${DB_MAX_OPEN:25}
    max_idle: ${DB_MAX_IDLE:5}
    max_lifetime: ${DB_MAX_LIFETIME:300s}

# 其他配置...
```

### 环境变量格式

配置文件使用 `${VAR:default}` 格式：
- `VAR` - 环境变量名
- `default` - 默认值（环境变量不存在时使用）

## 🌍 环境配置

### 开发环境

```bash
# 应用配置
export APP_ENV=development
export LOG_LEVEL=debug
export LOG_FORMAT=text

# 数据库配置
export DATABASE_URL="postgresql://postgres:password@localhost:5432/app_dev?sslmode=disable"
export DB_HOST=localhost
export DB_USER=postgres
export DB_PASSWORD=password
export DB_NAME=app_dev

# Redis配置
export REDIS_HOST=localhost
export REDIS_PORT=6379
```

### 生产环境

```bash
# 应用配置
export APP_ENV=production
export LOG_LEVEL=info
export LOG_FORMAT=json

# 数据库配置
export DATABASE_URL="*******************************************************************/driving_navigation?sslmode=require"
export DB_HOST=postgres-service
export DB_USER=driving_nav_user
export DB_PASSWORD=secure_password
export DB_SSL_MODE=require

# Redis配置
export REDIS_HOST=redis-service
export REDIS_PORT=6379

# 安全配置
export JWT_SECRET=your_secure_jwt_secret
```

## 📝 详细配置项

### 应用配置

```yaml
app:
  name: driving-navigation      # 应用名称
  port: ${APP_PORT:8080}       # HTTP服务端口
  env: ${APP_ENV:production}   # 运行环境 (development|production)
```

**环境变量：**
- `APP_PORT` - HTTP服务端口
- `APP_ENV` - 运行环境

### 数据库配置

```yaml
database:
  # 优先使用完整URL
  url: ${DATABASE_URL:}
  
  # 分离字段配置
  host: ${DB_HOST:postgres-service}
  port: ${DB_PORT:5432}
  name: ${DB_NAME:driving_navigation}
  user: ${DB_USER:driving_nav_user}
  password: ${DB_PASSWORD:driving_nav_2024}
  ssl_mode: ${DB_SSL_MODE:disable}
  
  # 连接池配置
  pool:
    max_open: ${DB_MAX_OPEN:25}      # 最大连接数
    max_idle: ${DB_MAX_IDLE:5}       # 最大空闲连接数
    max_lifetime: ${DB_MAX_LIFETIME:300s}  # 连接最大生存时间
```

**环境变量：**
- `DATABASE_URL` - 完整数据库连接字符串（优先级最高）
- `DB_HOST` - 数据库主机
- `DB_PORT` - 数据库端口
- `DB_NAME` - 数据库名称
- `DB_USER` - 数据库用户名
- `DB_PASSWORD` - 数据库密码
- `DB_SSL_MODE` - SSL模式 (disable|require|verify-ca|verify-full)

### JWT配置

```yaml
jwt:
  key: ${JWT_SECRET:driving_nav_jwt_secret_2024}
```

**环境变量：**
- `JWT_SECRET` - JWT签名密钥

### Redis配置

```yaml
redis:
  host: ${REDIS_HOST:redis-service}
  port: ${REDIS_PORT:6379}
  password: ${REDIS_PASSWORD:}
  db: ${REDIS_DB:0}
```

**环境变量：**
- `REDIS_HOST` - Redis主机
- `REDIS_PORT` - Redis端口
- `REDIS_PASSWORD` - Redis密码
- `REDIS_DB` - Redis数据库编号

### 日志配置

```yaml
log:
  level: ${LOG_LEVEL:info}           # 日志级别
  format: ${LOG_FORMAT:json}         # 日志格式
  output: ${LOG_OUTPUT:stdout}       # 输出目标
  file_path: ${LOG_FILE_PATH:/var/log/app/app.log}
  max_size: ${LOG_MAX_SIZE:100}      # 文件最大大小(MB)
  max_backups: ${LOG_MAX_BACKUPS:5}  # 保留文件数
  max_age: ${LOG_MAX_AGE:30}         # 保留天数
  compress: ${LOG_COMPRESS:true}     # 是否压缩
```

**日志级别：** `debug` | `info` | `warn` | `error`  
**日志格式：** `json` | `text`  
**输出目标：** `stdout` | `file` | `both`

### 高德地图API配置

```yaml
amap:
  api_key: ${AMAP_API_KEY:your_api_key}
  web_service_key: ${AMAP_WEB_SERVICE_KEY:your_web_service_key}
  base_url: ${AMAP_BASE_URL:https://restapi.amap.com}
  timeout: ${AMAP_TIMEOUT:30s}
  rate_limit:
    requests_per_second: ${AMAP_RATE_LIMIT:10}
    burst: ${AMAP_BURST:20}
```

**环境变量：**
- `AMAP_API_KEY` - 高德地图API密钥
- `AMAP_WEB_SERVICE_KEY` - 高德地图Web服务密钥

### WebSocket配置

```yaml
websocket:
  enabled: ${WEBSOCKET_ENABLED:true}
  port: ${WEBSOCKET_PORT:8081}
  path: ${WEBSOCKET_PATH:/ws}
  max_connections: ${WS_MAX_CONNECTIONS:1000}
  read_buffer_size: ${WS_READ_BUFFER:1024}
  write_buffer_size: ${WS_WRITE_BUFFER:1024}
  ping_period: ${WS_PING_PERIOD:54s}
  pong_wait: ${WS_PONG_WAIT:60s}
  write_wait: ${WS_WRITE_WAIT:10s}
  max_message_size: ${WS_MAX_MESSAGE_SIZE:512}
```

### 监控配置

```yaml
monitoring:
  metrics:
    enabled: ${METRICS_ENABLED:true}
    port: ${METRICS_PORT:9090}
    path: ${METRICS_PATH:/metrics}

  loki:
    enabled: ${LOKI_ENABLED:false}
    endpoint: ${LOKI_ENDPOINT:http://loki:3100}

  tracing:
    enabled: ${TRACING_ENABLED:false}
    jaeger_endpoint: ${JAEGER_ENDPOINT:http://jaeger:14268/api/traces}
```

## 🔐 安全配置

### 生产环境安全建议

#### 1. 强密码策略

```bash
# 生成安全的数据库密码
openssl rand -base64 32

# 生成安全的JWT密钥
openssl rand -base64 64
```

#### 2. SSL/TLS配置

```bash
# 生产环境启用SSL
export DB_SSL_MODE=require

# 配置SSL证书路径（如果需要）
export DB_SSL_CERT=/path/to/client-cert.pem
export DB_SSL_KEY=/path/to/client-key.pem
export DB_SSL_ROOT_CERT=/path/to/ca-cert.pem
```

#### 3. 敏感信息管理

```bash
# 使用环境变量而不是硬编码
export DB_PASSWORD="$(cat /run/secrets/db_password)"
export JWT_SECRET="$(cat /run/secrets/jwt_secret)"
```

## 🛠️ 配置管理

### 验证配置

```bash
# 检查配置加载
go run cmd/main.go --config-check

# 或查看应用启动日志
tail -f logs/app.log | grep "config"
```

### 配置热重载

应用支持通过信号重新加载配置：

```bash
# 发送SIGHUP信号重新加载配置
kill -HUP $(pgrep driving-navigation)
```

### 配置备份

```bash
# 备份当前配置
cp configs/config.yml configs/config.yml.backup.$(date +%Y%m%d)

# 恢复配置
cp configs/config.yml.backup.20240101 configs/config.yml
```

## 🔍 故障排除

### 常见配置问题

#### 1. 数据库连接失败

```bash
# 检查数据库配置
echo $DATABASE_URL
echo $DB_HOST $DB_PORT $DB_NAME $DB_USER

# 测试数据库连接
psql "$DATABASE_URL" -c "SELECT version();"
```

#### 2. 环境变量未生效

```bash
# 检查环境变量是否设置
env | grep -E "(DB_|REDIS_|APP_)"

# 检查配置文件语法
go run -c 'import yaml; yaml.safe_load(open("configs/config.yml"))'
```

#### 3. 日志配置问题

```bash
# 检查日志目录权限
ls -la /var/log/app/

# 创建日志目录
sudo mkdir -p /var/log/app
sudo chown $USER:$USER /var/log/app
```

### 配置调试

启用配置调试模式：

```bash
export LOG_LEVEL=debug
export CONFIG_DEBUG=true
go run cmd/main.go
```

## 📊 配置最佳实践

### 1. 环境分离

```bash
# 开发环境配置文件
.env.development

# 生产环境配置文件
.env.production

# 加载对应环境配置
source .env.${APP_ENV}
```

### 2. 配置验证

```go
// 在应用启动时验证关键配置
func validateConfig(cfg *Config) error {
    if cfg.Database.Host == "" {
        return errors.New("database host is required")
    }
    if cfg.JWT.Key == "" {
        return errors.New("JWT secret is required")
    }
    return nil
}
```

### 3. 配置文档化

```yaml
# 在配置文件中添加注释
database:
  host: ${DB_HOST:postgres-service}  # 数据库主机地址
  port: ${DB_PORT:5432}             # 数据库端口，默认5432
```

### 4. 敏感信息保护

```bash
# 使用.gitignore忽略敏感配置
echo ".env.production" >> .gitignore
echo "configs/secrets.yml" >> .gitignore

# 使用模板文件
cp configs/config.yml.template configs/config.yml
```

## 📚 相关文档

- [环境变量配置](ENVIRONMENT.md) - 环境变量详细说明
- [数据库配置](DATABASE.md) - 数据库配置详解
- [开发指南](../development/QUICK_START.md) - 开发环境配置
- [部署指南](../deployment/REMOTE_DEPLOYMENT.md) - 生产环境配置

## 💡 配置技巧

1. **使用有意义的默认值** - 让开发环境开箱即用
2. **环境变量命名规范** - 使用统一的命名前缀
3. **配置分组** - 将相关配置项组织在一起
4. **文档化配置** - 为每个配置项添加注释
5. **验证配置** - 在应用启动时验证关键配置
6. **敏感信息保护** - 使用环境变量管理敏感信息

---

**提示**: 修改配置后需要重启应用才能生效，除非使用了配置热重载功能。