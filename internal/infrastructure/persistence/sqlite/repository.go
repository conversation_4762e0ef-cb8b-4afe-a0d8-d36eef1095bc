// internal/infrastructure/persistence/sqlite/repository.go
package sqlite

import (
	"time"

	"github.com/azel-ko/final-ddd/internal/domain/entities"
	"github.com/azel-ko/final-ddd/internal/domain/repository"
	"gorm.io/gorm"
)

type sqliteRepository struct {
	db *gorm.DB
}

func NewSQLiteRepository(db *gorm.DB) repository.Repository {
	return &sqliteRepository{db: db}
}

func (r *sqliteRepository) CreateUser(user *entities.User) error {
	return r.db.Create(user).Error
}

func (r *sqliteRepository) GetUser(id int) (*entities.User, error) {
	var user entities.User
	if err := r.db.First(&user, id).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

func (r *sqliteRepository) UpdateUser(user *entities.User) error {
	return r.db.Save(user).Error
}

func (r *sqliteRepository) DeleteUser(id int) error {
	return r.db.Delete(&entities.User{}, id).Error
}

func (r *sqliteRepository) GetUserByEmail(email string) (*entities.User, error) {
	var user entities.User
	if err := r.db.Where("email = ?", email).First(&user).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

func (r *sqliteRepository) UpdateUserProfile(user *entities.User) error {
	return r.db.Model(user).Updates(map[string]interface{}{
		"name":  user.Name,
		"email": user.Email,
	}).Error
}

// Checkpoint operations
func (r *sqliteRepository) CreateCheckpoint(checkpoint *entities.Checkpoint) error {
	return r.db.Create(checkpoint).Error
}

func (r *sqliteRepository) GetCheckpoint(id uint) (*entities.Checkpoint, error) {
	var checkpoint entities.Checkpoint
	if err := r.db.First(&checkpoint, id).Error; err != nil {
		return nil, err
	}
	return &checkpoint, nil
}

func (r *sqliteRepository) UpdateCheckpoint(checkpoint *entities.Checkpoint) error {
	return r.db.Save(checkpoint).Error
}

func (r *sqliteRepository) DeleteCheckpoint(id uint) error {
	return r.db.Delete(&entities.Checkpoint{}, id).Error
}

func (r *sqliteRepository) ListCheckpoints(params repository.CheckpointQueryParams) ([]*entities.Checkpoint, int64, error) {
	var checkpoints []*entities.Checkpoint
	var total int64

	query := r.db.Model(&entities.Checkpoint{})

	// 添加过滤条件
	if params.Province != "" {
		query = query.Where("province = ?", params.Province)
	}
	if params.City != "" {
		query = query.Where("city = ?", params.City)
	}
	if params.District != "" {
		query = query.Where("district = ?", params.District)
	}
	if params.Status != "" {
		query = query.Where("status = ?", params.Status)
	}
	if params.Type != "" {
		query = query.Where("type = ?", params.Type)
	}

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 添加排序
	orderBy := "created_at DESC"
	if params.SortBy != "" {
		direction := "ASC"
		if params.SortOrder == "desc" {
			direction = "DESC"
		}
		orderBy = params.SortBy + " " + direction
	}
	query = query.Order(orderBy)

	// 添加分页
	if params.Page > 0 && params.PageSize > 0 {
		offset := (params.Page - 1) * params.PageSize
		query = query.Offset(offset).Limit(params.PageSize)
	}

	if err := query.Find(&checkpoints).Error; err != nil {
		return nil, 0, err
	}

	return checkpoints, total, nil
}

func (r *sqliteRepository) GetNearbyCheckpoints(lat, lng float64, radius int) ([]*entities.Checkpoint, error) {
	var checkpoints []*entities.Checkpoint

	// SQLite版本的距离计算（简化版）
	query := `
		SELECT *, (
			(? - latitude) * (? - latitude) + 
			(? - longitude) * (? - longitude)
		) AS distance_sq 
		FROM checkpoints 
		WHERE (
			(? - latitude) * (? - latitude) + 
			(? - longitude) * (? - longitude)
		) <= ? 
		ORDER BY distance_sq
	`

	// 简化的距离计算，radius需要转换为度数的平方
	radiusDegSq := float64(radius) / 111.0 // 大约每度111公里
	radiusDegSq = radiusDegSq * radiusDegSq

	if err := r.db.Raw(query, lat, lat, lng, lng, lat, lat, lng, lng, radiusDegSq).Scan(&checkpoints).Error; err != nil {
		return nil, err
	}

	return checkpoints, nil
}

func (r *sqliteRepository) BatchCreateCheckpoints(checkpoints []*entities.Checkpoint) error {
	if len(checkpoints) == 0 {
		return nil
	}

	// SQLite批量插入，每批处理50条记录（SQLite限制较小）
	batchSize := 50
	for i := 0; i < len(checkpoints); i += batchSize {
		end := i + batchSize
		if end > len(checkpoints) {
			end = len(checkpoints)
		}

		batch := checkpoints[i:end]
		if err := r.db.Create(&batch).Error; err != nil {
			return err
		}
	}

	return nil
}

func (r *sqliteRepository) BatchUpdateCheckpoints(checkpoints []*entities.Checkpoint) error {
	if len(checkpoints) == 0 {
		return nil
	}

	// 使用事务批量更新
	return r.db.Transaction(func(tx *gorm.DB) error {
		for _, checkpoint := range checkpoints {
			if err := tx.Save(checkpoint).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

// Checkpoint Report operations
func (r *sqliteRepository) CreateCheckpointReport(report *entities.CheckpointReport) error {
	return r.db.Create(report).Error
}

func (r *sqliteRepository) GetCheckpointReports(checkpointID uint) ([]*entities.CheckpointReport, error) {
	var reports []*entities.CheckpointReport
	if err := r.db.Where("checkpoint_id = ?", checkpointID).
		Order("created_at DESC").
		Find(&reports).Error; err != nil {
		return nil, err
	}
	return reports, nil
}

func (r *sqliteRepository) UpdateCheckpointReport(report *entities.CheckpointReport) error {
	return r.db.Save(report).Error
}

// Route operations
func (r *sqliteRepository) CreateRoute(route *entities.Route) error {
	return r.db.Create(route).Error
}

func (r *sqliteRepository) GetUserRoutes(userID uint, limit int) ([]*entities.Route, error) {
	var routes []*entities.Route
	query := r.db.Where("user_id = ?", userID).Order("created_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	if err := query.Find(&routes).Error; err != nil {
		return nil, err
	}
	return routes, nil
}

func (r *sqliteRepository) DeleteRoute(id uint) error {
	return r.db.Delete(&entities.Route{}, id).Error
}

// User Preference operations
func (r *sqliteRepository) GetUserPreference(userID uint) (*entities.UserPreference, error) {
	var preference entities.UserPreference
	if err := r.db.Where("user_id = ?", userID).First(&preference).Error; err != nil {
		return nil, err
	}
	return &preference, nil
}

func (r *sqliteRepository) CreateOrUpdateUserPreference(preference *entities.UserPreference) error {
	// 使用UPSERT操作
	return r.db.Save(preference).Error
}

// Data Update Log operations
func (r *sqliteRepository) CreateDataUpdateLog(log *entities.DataUpdateLog) error {
	return r.db.Create(log).Error
}

func (r *sqliteRepository) GetLatestDataUpdateLog(source string) (*entities.DataUpdateLog, error) {
	var log entities.DataUpdateLog
	if err := r.db.Where("source = ?", source).
		Order("created_at DESC").
		First(&log).Error; err != nil {
		return nil, err
	}
	return &log, nil
}

// Session operations
func (r *sqliteRepository) CreateSession(session *entities.UserSession) error {
	return r.db.Create(session).Error
}

func (r *sqliteRepository) GetSession(sessionID string) (*entities.UserSession, error) {
	var session entities.UserSession
	if err := r.db.Where("id = ? AND is_active = ?", sessionID, true).First(&session).Error; err != nil {
		return nil, err
	}
	return &session, nil
}

func (r *sqliteRepository) UpdateSession(session *entities.UserSession) error {
	return r.db.Save(session).Error
}

func (r *sqliteRepository) DeleteSession(sessionID string) error {
	return r.db.Where("id = ?", sessionID).Delete(&entities.UserSession{}).Error
}

func (r *sqliteRepository) GetUserSessions(userID uint) ([]*entities.UserSession, error) {
	var sessions []*entities.UserSession
	if err := r.db.Where("user_id = ? AND is_active = ?", userID, true).Find(&sessions).Error; err != nil {
		return nil, err
	}
	return sessions, nil
}

func (r *sqliteRepository) DeleteUserSessions(userID uint) error {
	return r.db.Where("user_id = ?", userID).Delete(&entities.UserSession{}).Error
}

func (r *sqliteRepository) DeleteExpiredSessions() error {
	return r.db.Where("expires_at < ? OR is_active = ?", time.Now(), false).Delete(&entities.UserSession{}).Error
}

// Push Device operations
func (r *sqliteRepository) CreatePushDevice(device *entities.PushDevice) error {
	return r.db.Create(device).Error
}

func (r *sqliteRepository) GetPushDeviceByToken(token string) (*entities.PushDevice, error) {
	var device entities.PushDevice
	if err := r.db.Where("token = ?", token).First(&device).Error; err != nil {
		return nil, err
	}
	return &device, nil
}

func (r *sqliteRepository) GetUserPushDevices(userID uint) ([]*entities.PushDevice, error) {
	var devices []*entities.PushDevice
	if err := r.db.Where("user_id = ? AND is_active = ?", userID, true).Find(&devices).Error; err != nil {
		return nil, err
	}
	return devices, nil
}

func (r *sqliteRepository) UpdatePushDevice(device *entities.PushDevice) error {
	return r.db.Save(device).Error
}

func (r *sqliteRepository) DeletePushDevice(id uint) error {
	return r.db.Delete(&entities.PushDevice{}, id).Error
}

func (r *sqliteRepository) DeleteInactivePushDevices(days int) error {
	cutoffTime := time.Now().AddDate(0, 0, -days)
	return r.db.Where("is_active = ? OR last_used < ?", false, cutoffTime).Delete(&entities.PushDevice{}).Error
}

// Push Log operations
func (r *sqliteRepository) CreatePushLog(log *entities.PushLog) error {
	return r.db.Create(log).Error
}

func (r *sqliteRepository) GetPushLogs(userID uint, limit int) ([]*entities.PushLog, error) {
	var logs []*entities.PushLog
	query := r.db.Where("user_id = ?", userID).Order("created_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	if err := query.Find(&logs).Error; err != nil {
		return nil, err
	}
	return logs, nil
}

func (r *sqliteRepository) GetPushStatistics(userID uint, days int) (*repository.PushStatisticsData, error) {
	cutoffTime := time.Now().AddDate(0, 0, -days)

	var stats repository.PushStatisticsData

	// 统计推送数据
	if err := r.db.Model(&entities.PushLog{}).
		Where("user_id = ? AND created_at >= ?", userID, cutoffTime).
		Select("COUNT(*) as total_sent, COUNT(CASE WHEN success = 1 THEN 1 END) as total_delivered, COUNT(CASE WHEN success = 0 THEN 1 END) as total_failed").
		Scan(&stats).Error; err != nil {
		return nil, err
	}

	// 统计设备数量
	var deviceCount int64
	if err := r.db.Model(&entities.PushDevice{}).
		Where("user_id = ? AND is_active = ?", userID, true).
		Count(&deviceCount).Error; err != nil {
		return nil, err
	}
	stats.DeviceCount = int(deviceCount)

	return &stats, nil
}

func (r *sqliteRepository) DeleteOldPushLogs(days int) error {
	cutoffTime := time.Now().AddDate(0, 0, -days)
	return r.db.Where("created_at < ?", cutoffTime).Delete(&entities.PushLog{}).Error
}

// Push Template operations
func (r *sqliteRepository) CreatePushTemplate(template *entities.PushTemplate) error {
	return r.db.Create(template).Error
}

func (r *sqliteRepository) GetPushTemplate(name string) (*entities.PushTemplate, error) {
	var template entities.PushTemplate
	if err := r.db.Where("name = ? AND is_active = ?", name, true).First(&template).Error; err != nil {
		return nil, err
	}
	return &template, nil
}

func (r *sqliteRepository) GetPushTemplates() ([]*entities.PushTemplate, error) {
	var templates []*entities.PushTemplate
	if err := r.db.Where("is_active = ?", true).Find(&templates).Error; err != nil {
		return nil, err
	}
	return templates, nil
}

func (r *sqliteRepository) UpdatePushTemplate(template *entities.PushTemplate) error {
	return r.db.Save(template).Error
}

func (r *sqliteRepository) DeletePushTemplate(id uint) error {
	return r.db.Delete(&entities.PushTemplate{}, id).Error
}

// Push Subscription operations
func (r *sqliteRepository) GetPushSubscription(userID uint) (*entities.PushSubscription, error) {
	var subscription entities.PushSubscription
	if err := r.db.Where("user_id = ?", userID).First(&subscription).Error; err != nil {
		return nil, err
	}
	return &subscription, nil
}

func (r *sqliteRepository) CreateOrUpdatePushSubscription(subscription *entities.PushSubscription) error {
	return r.db.Save(subscription).Error
}
