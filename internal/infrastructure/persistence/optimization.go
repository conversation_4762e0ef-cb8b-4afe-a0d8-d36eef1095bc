package persistence

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/azel-ko/final-ddd/internal/pkg/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// QueryOptimizer 数据库查询优化器
type QueryOptimizer struct {
	db *gorm.DB
}

// NewQueryOptimizer 创建查询优化器
func NewQueryOptimizer(db *gorm.DB) *QueryOptimizer {
	return &QueryOptimizer{db: db}
}

// OptimizedQuery 优化的查询结构
type OptimizedQuery struct {
	SQL      string
	Args     []interface{}
	Duration time.Duration
	Rows     int64
}

// QueryPerformanceStats 查询性能统计
type QueryPerformanceStats struct {
	TotalQueries   int64
	SlowQueries    int64
	AverageTime    time.Duration
	MaxTime        time.Duration
	MinTime        time.Duration
	CacheHitRate   float64
	IndexUsageRate float64
}

// PerformanceMonitor 性能监控器
type PerformanceMonitor struct {
	queries []OptimizedQuery
	stats   QueryPerformanceStats
}

// NewPerformanceMonitor 创建性能监控器
func NewPerformanceMonitor() *PerformanceMonitor {
	return &PerformanceMonitor{
		queries: make([]OptimizedQuery, 0),
		stats: QueryPerformanceStats{
			MinTime: time.Hour, // 初始化为一个大值
		},
	}
}

// RecordQuery 记录查询
func (pm *PerformanceMonitor) RecordQuery(query OptimizedQuery) {
	pm.queries = append(pm.queries, query)
	pm.updateStats(query)
}

// updateStats 更新统计信息
func (pm *PerformanceMonitor) updateStats(query OptimizedQuery) {
	pm.stats.TotalQueries++

	// 更新平均时间
	totalTime := time.Duration(pm.stats.TotalQueries-1)*pm.stats.AverageTime + query.Duration
	pm.stats.AverageTime = totalTime / time.Duration(pm.stats.TotalQueries)

	// 更新最大最小时间
	if query.Duration > pm.stats.MaxTime {
		pm.stats.MaxTime = query.Duration
	}
	if query.Duration < pm.stats.MinTime {
		pm.stats.MinTime = query.Duration
	}

	// 检查是否为慢查询（超过1秒）
	if query.Duration > time.Second {
		pm.stats.SlowQueries++
	}
}

// GetStats 获取统计信息
func (pm *PerformanceMonitor) GetStats() QueryPerformanceStats {
	return pm.stats
}

// GetSlowQueries 获取慢查询列表
func (pm *PerformanceMonitor) GetSlowQueries(threshold time.Duration) []OptimizedQuery {
	var slowQueries []OptimizedQuery
	for _, query := range pm.queries {
		if query.Duration > threshold {
			slowQueries = append(slowQueries, query)
		}
	}
	return slowQueries
}

// DatabaseIndexOptimizer 数据库索引优化器
type DatabaseIndexOptimizer struct {
	db *gorm.DB
}

// NewDatabaseIndexOptimizer 创建索引优化器
func NewDatabaseIndexOptimizer(db *gorm.DB) *DatabaseIndexOptimizer {
	return &DatabaseIndexOptimizer{db: db}
}

// CreateOptimalIndexes 创建优化索引
func (dio *DatabaseIndexOptimizer) CreateOptimalIndexes(ctx context.Context) error {
	logger.Info("Creating optimal database indexes...")

	// 检查站表索引
	checkpointIndexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_checkpoints_location ON checkpoints (latitude, longitude);",
		"CREATE INDEX IF NOT EXISTS idx_checkpoints_province_city ON checkpoints (province, city);",
		"CREATE INDEX IF NOT EXISTS idx_checkpoints_status ON checkpoints (status);",
		"CREATE INDEX IF NOT EXISTS idx_checkpoints_type ON checkpoints (type);",
		"CREATE INDEX IF NOT EXISTS idx_checkpoints_created_at ON checkpoints (created_at);",
		"CREATE INDEX IF NOT EXISTS idx_checkpoints_updated_at ON checkpoints (updated_at);",
	}

	// 用户表索引
	userIndexes := []string{
		"CREATE UNIQUE INDEX IF NOT EXISTS idx_users_email ON users (email);",
		"CREATE INDEX IF NOT EXISTS idx_users_created_at ON users (created_at);",
		"CREATE INDEX IF NOT EXISTS idx_users_role ON users (role);",
	}

	// 路线表索引
	routeIndexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_routes_user_id ON routes (user_id);",
		"CREATE INDEX IF NOT EXISTS idx_routes_created_at ON routes (created_at);",
		"CREATE INDEX IF NOT EXISTS idx_routes_user_created ON routes (user_id, created_at);",
	}

	// 会话表索引
	sessionIndexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON user_sessions (user_id);",
		"CREATE INDEX IF NOT EXISTS idx_sessions_expires_at ON user_sessions (expires_at);",
		"CREATE INDEX IF NOT EXISTS idx_sessions_is_active ON user_sessions (is_active);",
	}

	// 推送设备表索引
	pushDeviceIndexes := []string{
		"CREATE UNIQUE INDEX IF NOT EXISTS idx_push_devices_token ON push_devices (token);",
		"CREATE INDEX IF NOT EXISTS idx_push_devices_user_id ON push_devices (user_id);",
		"CREATE INDEX IF NOT EXISTS idx_push_devices_is_active ON push_devices (is_active);",
		"CREATE INDEX IF NOT EXISTS idx_push_devices_last_used ON push_devices (last_used);",
	}

	// 推送日志表索引
	pushLogIndexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_push_logs_user_id ON push_logs (user_id);",
		"CREATE INDEX IF NOT EXISTS idx_push_logs_created_at ON push_logs (created_at);",
		"CREATE INDEX IF NOT EXISTS idx_push_logs_success ON push_logs (success);",
	}

	// 合并所有索引
	allIndexes := append(checkpointIndexes, userIndexes...)
	allIndexes = append(allIndexes, routeIndexes...)
	allIndexes = append(allIndexes, sessionIndexes...)
	allIndexes = append(allIndexes, pushDeviceIndexes...)
	allIndexes = append(allIndexes, pushLogIndexes...)

	// 执行索引创建
	for _, indexSQL := range allIndexes {
		if err := dio.db.WithContext(ctx).Exec(indexSQL).Error; err != nil {
			logger.Error("Failed to create index",
				zap.String("sql", indexSQL),
				zap.Error(err))
			// 继续执行其他索引，不因为一个失败而停止
		} else {
			logger.Debug("Index created successfully", zap.String("sql", indexSQL))
		}
	}

	logger.Info("Database index optimization completed")
	return nil
}

// AnalyzeQueryPerformance 分析查询性能
func (dio *DatabaseIndexOptimizer) AnalyzeQueryPerformance(ctx context.Context) (*QueryPerformanceStats, error) {
	stats := &QueryPerformanceStats{}

	// 获取数据库类型
	dbType := dio.db.Dialector.Name()

	switch dbType {
	case "postgres":
		return dio.analyzePostgresPerformance(ctx)
	case "mysql":
		return dio.analyzeMySQLPerformance(ctx)
	case "sqlite":
		return dio.analyzeSQLitePerformance(ctx)
	default:
		logger.Warn("Unsupported database type for performance analysis", zap.String("type", dbType))
		return stats, nil
	}
}

// analyzePostgresPerformance 分析PostgreSQL性能
func (dio *DatabaseIndexOptimizer) analyzePostgresPerformance(ctx context.Context) (*QueryPerformanceStats, error) {
	stats := &QueryPerformanceStats{}

	// 查询慢查询统计
	var slowQueryCount int64
	err := dio.db.WithContext(ctx).Raw(`
		SELECT COUNT(*) 
		FROM pg_stat_statements 
		WHERE mean_time > 1000
	`).Scan(&slowQueryCount).Error

	if err != nil {
		logger.Debug("Could not get slow query stats from pg_stat_statements", zap.Error(err))
		// pg_stat_statements可能未启用，这是正常的
	} else {
		stats.SlowQueries = slowQueryCount
	}

	// 查询索引使用情况
	var indexUsage float64
	err = dio.db.WithContext(ctx).Raw(`
		SELECT 
			ROUND(
				100.0 * SUM(idx_scan) / NULLIF(SUM(seq_scan + idx_scan), 0), 2
			) as index_usage_rate
		FROM pg_stat_user_tables
	`).Scan(&indexUsage).Error

	if err != nil {
		logger.Debug("Could not get index usage stats", zap.Error(err))
	} else {
		stats.IndexUsageRate = indexUsage
	}

	return stats, nil
}

// analyzeMySQLPerformance 分析MySQL性能
func (dio *DatabaseIndexOptimizer) analyzeMySQLPerformance(ctx context.Context) (*QueryPerformanceStats, error) {
	stats := &QueryPerformanceStats{}

	// 查询慢查询数量
	var slowQueryCount int64
	err := dio.db.WithContext(ctx).Raw("SHOW GLOBAL STATUS LIKE 'Slow_queries'").Scan(&slowQueryCount).Error
	if err != nil {
		logger.Debug("Could not get slow query count", zap.Error(err))
	} else {
		stats.SlowQueries = slowQueryCount
	}

	return stats, nil
}

// analyzeSQLitePerformance 分析SQLite性能
func (dio *DatabaseIndexOptimizer) analyzeSQLitePerformance(ctx context.Context) (*QueryPerformanceStats, error) {
	stats := &QueryPerformanceStats{}

	// SQLite的性能分析相对简单
	// 可以通过EXPLAIN QUERY PLAN来分析查询计划
	logger.Debug("SQLite performance analysis is limited")

	return stats, nil
}

// OptimizeTableStatistics 优化表统计信息
func (dio *DatabaseIndexOptimizer) OptimizeTableStatistics(ctx context.Context) error {
	logger.Info("Optimizing table statistics...")

	dbType := dio.db.Dialector.Name()

	switch dbType {
	case "postgres":
		// PostgreSQL: 更新统计信息
		tables := []string{"users", "checkpoints", "routes", "user_sessions", "push_devices", "push_logs"}
		for _, table := range tables {
			sql := fmt.Sprintf("ANALYZE %s", table)
			if err := dio.db.WithContext(ctx).Exec(sql).Error; err != nil {
				logger.Error("Failed to analyze table", zap.String("table", table), zap.Error(err))
			}
		}

	case "mysql":
		// MySQL: 更新统计信息
		tables := []string{"users", "checkpoints", "routes", "user_sessions", "push_devices", "push_logs"}
		for _, table := range tables {
			sql := fmt.Sprintf("ANALYZE TABLE %s", table)
			if err := dio.db.WithContext(ctx).Exec(sql).Error; err != nil {
				logger.Error("Failed to analyze table", zap.String("table", table), zap.Error(err))
			}
		}

	case "sqlite":
		// SQLite: 更新统计信息
		if err := dio.db.WithContext(ctx).Exec("ANALYZE").Error; err != nil {
			logger.Error("Failed to analyze SQLite database", zap.Error(err))
		}
	}

	logger.Info("Table statistics optimization completed")
	return nil
}

// QueryOptimizationSuggestions 查询优化建议
type QueryOptimizationSuggestions struct {
	MissingIndexes   []string
	SlowQueries      []string
	OptimizationTips []string
}

// GetOptimizationSuggestions 获取优化建议
func (dio *DatabaseIndexOptimizer) GetOptimizationSuggestions(ctx context.Context) (*QueryOptimizationSuggestions, error) {
	suggestions := &QueryOptimizationSuggestions{
		MissingIndexes:   make([]string, 0),
		SlowQueries:      make([]string, 0),
		OptimizationTips: make([]string, 0),
	}

	// 检查缺失的索引
	missingIndexes := dio.checkMissingIndexes(ctx)
	suggestions.MissingIndexes = missingIndexes

	// 添加通用优化建议
	suggestions.OptimizationTips = []string{
		"使用LIMIT限制查询结果数量",
		"避免SELECT *，只查询需要的字段",
		"使用适当的WHERE条件过滤数据",
		"考虑使用缓存减少数据库查询",
		"定期更新表统计信息",
		"监控慢查询并进行优化",
		"使用连接池管理数据库连接",
		"考虑读写分离提高性能",
	}

	return suggestions, nil
}

// checkMissingIndexes 检查缺失的索引
func (dio *DatabaseIndexOptimizer) checkMissingIndexes(ctx context.Context) []string {
	var missingIndexes []string

	dbType := dio.db.Dialector.Name()

	switch dbType {
	case "postgres":
		// 检查PostgreSQL缺失的索引
		var result []struct {
			TableName string `gorm:"column:table_name"`
			IndexName string `gorm:"column:index_name"`
		}

		// 这是一个简化的检查，实际项目中可能需要更复杂的逻辑
		err := dio.db.WithContext(ctx).Raw(`
			SELECT 
				schemaname||'.'||tablename as table_name,
				'Missing index on frequently queried columns' as index_name
			FROM pg_tables 
			WHERE schemaname = 'public'
			AND tablename IN ('checkpoints', 'users', 'routes')
		`).Scan(&result).Error

		if err != nil {
			logger.Debug("Could not check missing indexes", zap.Error(err))
		}

		for _, r := range result {
			missingIndexes = append(missingIndexes, fmt.Sprintf("%s: %s", r.TableName, r.IndexName))
		}

	case "mysql":
		// MySQL的索引检查逻辑
		missingIndexes = append(missingIndexes, "Consider adding composite indexes for frequently used WHERE clauses")

	case "sqlite":
		// SQLite的索引检查逻辑
		missingIndexes = append(missingIndexes, "SQLite: Consider adding indexes on frequently queried columns")
	}

	return missingIndexes
}

// ConnectionPoolOptimizer 连接池优化器
type ConnectionPoolOptimizer struct {
	db *gorm.DB
}

// NewConnectionPoolOptimizer 创建连接池优化器
func NewConnectionPoolOptimizer(db *gorm.DB) *ConnectionPoolOptimizer {
	return &ConnectionPoolOptimizer{db: db}
}

// OptimizeConnectionPool 优化连接池设置
func (cpo *ConnectionPoolOptimizer) OptimizeConnectionPool() error {
	sqlDB, err := cpo.db.DB()
	if err != nil {
		return err
	}

	// 设置最大打开连接数
	sqlDB.SetMaxOpenConns(25)

	// 设置最大空闲连接数
	sqlDB.SetMaxIdleConns(10)

	// 设置连接最大生存时间
	sqlDB.SetConnMaxLifetime(5 * time.Minute)

	// 设置连接最大空闲时间
	sqlDB.SetConnMaxIdleTime(1 * time.Minute)

	logger.Info("Database connection pool optimized",
		zap.Int("max_open_conns", 25),
		zap.Int("max_idle_conns", 10),
		zap.Duration("conn_max_lifetime", 5*time.Minute),
		zap.Duration("conn_max_idle_time", 1*time.Minute))

	return nil
}

// GetConnectionPoolStats 获取连接池统计
func (cpo *ConnectionPoolOptimizer) GetConnectionPoolStats() map[string]interface{} {
	sqlDB, err := cpo.db.DB()
	if err != nil {
		return map[string]interface{}{"error": err.Error()}
	}

	stats := sqlDB.Stats()
	return map[string]interface{}{
		"max_open_connections": stats.MaxOpenConnections,
		"open_connections":     stats.OpenConnections,
		"in_use":               stats.InUse,
		"idle":                 stats.Idle,
		"wait_count":           stats.WaitCount,
		"wait_duration":        stats.WaitDuration.String(),
		"max_idle_closed":      stats.MaxIdleClosed,
		"max_idle_time_closed": stats.MaxIdleTimeClosed,
		"max_lifetime_closed":  stats.MaxLifetimeClosed,
	}
}

// QueryCacheManager 查询缓存管理器
type QueryCacheManager struct {
	cache map[string]interface{}
}

// NewQueryCacheManager 创建查询缓存管理器
func NewQueryCacheManager() *QueryCacheManager {
	return &QueryCacheManager{
		cache: make(map[string]interface{}),
	}
}

// CacheQuery 缓存查询结果
func (qcm *QueryCacheManager) CacheQuery(key string, result interface{}) {
	qcm.cache[key] = result
}

// GetCachedQuery 获取缓存的查询结果
func (qcm *QueryCacheManager) GetCachedQuery(key string) (interface{}, bool) {
	result, exists := qcm.cache[key]
	return result, exists
}

// ClearCache 清空缓存
func (qcm *QueryCacheManager) ClearCache() {
	qcm.cache = make(map[string]interface{})
}

// generateCacheKey 生成缓存键
func generateCacheKey(sql string, args []interface{}) string {
	key := sql
	for _, arg := range args {
		key += fmt.Sprintf("_%v", arg)
	}
	return strings.ReplaceAll(key, " ", "_")
}
