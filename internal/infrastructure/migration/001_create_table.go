package migration

import (
	"time"

	"gorm.io/gorm"
)

// UserTableMigration 创建用户表的迁移
type UserTableMigration struct{}

func (m *UserTableMigration) ID() string {
	return "001_create_users_table"
}

func (m *UserTableMigration) Up(db *gorm.DB) error {
	// 自动迁移表结构
	if err := db.AutoMigrate(&User{}); err != nil {
		return err
	}

	// 插入默认数据
	now := time.Now()
	trialExpiry := now.Add(72 * time.Hour) // 3 days trial

	users := []User{
		{
			Name:         "Alice",
			Email:        "<EMAIL>",
			Password:     "password123",
			Role:         "admin",
			CarPlate:     "京A12345",
			PlateRegion:  "北京",
			Subscription: "premium",
			TrialExpiry:  nil, // Premium users don't have trial expiry
			Preferences:  `{"defaultAvoidLevel": 2, "notificationTypes": ["checkpoint_update", "route_change"]}`,
			CreatedAt:    now,
			UpdatedAt:    now,
		},
		{
			Name:         "Bob",
			Email:        "<EMAIL>",
			Password:     "password123",
			Role:         "user",
			CarPlate:     "沪B67890",
			PlateRegion:  "上海",
			Subscription: "trial",
			TrialExpiry:  &trialExpiry,
			Preferences:  `{"defaultAvoidLevel": 1, "notificationTypes": ["checkpoint_update"]}`,
			CreatedAt:    now,
			UpdatedAt:    now,
		},
	}

	return db.Create(&users).Error
}

func (m *UserTableMigration) Down(db *gorm.DB) error {
	return db.Migrator().DropTable(&User{})
}

// User 定义用户表的结构
type User struct {
	ID           uint       `gorm:"primarykey"`
	Name         string     `gorm:"size:255;not null"`
	Email        string     `gorm:"size:255;not null;unique"`
	Password     string     `gorm:"size:255;not null"`
	Role         string     `gorm:"size:255;not null;default:'user'"`
	CarPlate     string     `gorm:"size:20"`
	PlateRegion  string     `gorm:"size:10"`
	Subscription string     `gorm:"size:20;not null;default:'trial'"` // trial, free, premium
	TrialExpiry  *time.Time `gorm:"type:timestamp"`
	Preferences  string     `gorm:"type:text"` // JSON string for user preferences
	CreatedAt    time.Time  `gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt    time.Time  `gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP"`
	LastLoginAt  *time.Time `gorm:"type:timestamp"`
}
