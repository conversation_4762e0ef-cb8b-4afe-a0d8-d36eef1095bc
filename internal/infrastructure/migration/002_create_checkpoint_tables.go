package migration

import (
	"time"

	"gorm.io/gorm"
)

// CheckpointTablesMigration 创建检查站相关表的迁移
type CheckpointTablesMigration struct{}

func (m *CheckpointTablesMigration) ID() string {
	return "002_create_checkpoint_tables"
}

func (m *CheckpointTablesMigration) Up(db *gorm.DB) error {
	// 自动迁移检查站相关表结构
	if err := db.AutoMigrate(
		&Checkpoint{},
		&CheckpointReport{},
		&Route{},
		&UserPreference{},
		&UserSession{},
		&DataUpdateLog{},
	); err != nil {
		return err
	}

	// 插入示例检查站数据
	now := time.Now()
	checkpoints := []Checkpoint{
		{
			Name:        "京藏高速进京检查站",
			Location:    "京藏高速公路进京方向",
			Latitude:    40.0776,
			Longitude:   116.3297,
			Province:    "北京",
			City:        "北京",
			District:    "昌平区",
			Road:        "京藏高速",
			Direction:   "进京方向",
			Status:      "active",
			Type:        "checkpoint",
			Source:      "web",
			Reliability: 85,
			LastSeen:    &now,
			CreatedAt:   now,
			UpdatedAt:   now,
		},
		{
			Name:        "京沪高速进京检查站",
			Location:    "京沪高速公路进京方向",
			Latitude:    39.7284,
			Longitude:   116.2734,
			Province:    "北京",
			City:        "北京",
			District:    "大兴区",
			Road:        "京沪高速",
			Direction:   "进京方向",
			Status:      "active",
			Type:        "checkpoint",
			Source:      "web",
			Reliability: 90,
			LastSeen:    &now,
			CreatedAt:   now,
			UpdatedAt:   now,
		},
		{
			Name:        "京港澳高速进京检查站",
			Location:    "京港澳高速公路进京方向",
			Latitude:    39.7891,
			Longitude:   116.2446,
			Province:    "北京",
			City:        "北京",
			District:    "丰台区",
			Road:        "京港澳高速",
			Direction:   "进京方向",
			Status:      "active",
			Type:        "checkpoint",
			Source:      "web",
			Reliability: 88,
			LastSeen:    &now,
			CreatedAt:   now,
			UpdatedAt:   now,
		},
		{
			Name:        "京承高速进京检查站",
			Location:    "京承高速公路进京方向",
			Latitude:    40.1234,
			Longitude:   116.5678,
			Province:    "北京",
			City:        "北京",
			District:    "朝阳区",
			Road:        "京承高速",
			Direction:   "进京方向",
			Status:      "active",
			Type:        "checkpoint",
			Source:      "web",
			Reliability: 82,
			LastSeen:    &now,
			CreatedAt:   now,
			UpdatedAt:   now,
		},
	}

	if err := db.Create(&checkpoints).Error; err != nil {
		return err
	}

	// 为现有用户创建默认偏好设置
	var users []User
	if err := db.Find(&users).Error; err != nil {
		return err
	}

	for _, user := range users {
		preference := UserPreference{
			UserID:            user.ID,
			DefaultAvoidLevel: 2,
			NotificationTypes: `["checkpoint_update", "route_change"]`,
			AutoUpdate:        true,
			VoiceNavigation:   true,
			NightMode:         false,
			MapStyle:          "standard",
			UpdatedAt:         now,
		}
		if err := db.Create(&preference).Error; err != nil {
			// 如果偏好设置已存在，忽略错误
			continue
		}
	}

	return nil
}

func (m *CheckpointTablesMigration) Down(db *gorm.DB) error {
	return db.Migrator().DropTable(
		&DataUpdateLog{},
		&UserPreference{},
		&Route{},
		&CheckpointReport{},
		&Checkpoint{},
	)
}

// 定义表结构（与entities中的结构保持一致）
type Checkpoint struct {
	ID          uint       `gorm:"primarykey"`
	Name        string     `gorm:"size:255;not null;index"`
	Location    string     `gorm:"size:500;not null"`
	Latitude    float64    `gorm:"type:decimal(10,8);not null;index"`
	Longitude   float64    `gorm:"type:decimal(11,8);not null;index"`
	Province    string     `gorm:"size:50;not null;index"`
	City        string     `gorm:"size:50;not null;index"`
	District    string     `gorm:"size:50;index"`
	Road        string     `gorm:"size:255;index"`
	Direction   string     `gorm:"size:100"`
	Status      string     `gorm:"size:20;not null;default:'unknown';index"`
	Type        string     `gorm:"size:50;not null;default:'checkpoint'"`
	Source      string     `gorm:"size:100;not null;default:'web'"`
	Reliability int        `gorm:"default:50"`
	LastSeen    *time.Time `gorm:"type:timestamp"`
	CreatedAt   time.Time  `gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt   time.Time  `gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP"`
}

type CheckpointReport struct {
	ID           uint      `gorm:"primarykey"`
	CheckpointID uint      `gorm:"not null;index"`
	UserID       uint      `gorm:"not null;index"`
	Status       string    `gorm:"size:20;not null"`
	Description  string    `gorm:"type:text"`
	Latitude     float64   `gorm:"type:decimal(10,8)"`
	Longitude    float64   `gorm:"type:decimal(11,8)"`
	Verified     bool      `gorm:"default:false"`
	CreatedAt    time.Time `gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP"`
}

type Route struct {
	ID            uint      `gorm:"primarykey"`
	UserID        uint      `gorm:"not null;index"`
	StartLat      float64   `gorm:"type:decimal(10,8);not null"`
	StartLng      float64   `gorm:"type:decimal(11,8);not null"`
	EndLat        float64   `gorm:"type:decimal(10,8);not null"`
	EndLng        float64   `gorm:"type:decimal(11,8);not null"`
	StartAddress  string    `gorm:"size:500"`
	EndAddress    string    `gorm:"size:500"`
	Distance      int       `gorm:"not null"`
	Duration      int       `gorm:"not null"`
	RouteData     string    `gorm:"type:text"`
	AvoidLevel    int       `gorm:"default:1"`
	CheckpointIDs string    `gorm:"type:text"`
	CreatedAt     time.Time `gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP"`
}

type UserPreference struct {
	ID                uint      `gorm:"primarykey"`
	UserID            uint      `gorm:"not null;unique;index"`
	DefaultAvoidLevel int       `gorm:"default:1"`
	NotificationTypes string    `gorm:"type:text"`
	AutoUpdate        bool      `gorm:"default:true"`
	VoiceNavigation   bool      `gorm:"default:true"`
	NightMode         bool      `gorm:"default:false"`
	MapStyle          string    `gorm:"size:50;default:'standard'"`
	UpdatedAt         time.Time `gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP"`
}

type UserSession struct {
	ID         string    `gorm:"primarykey;size:255"`
	UserID     uint      `gorm:"not null;index"`
	DeviceID   string    `gorm:"size:255;not null"`
	Platform   string    `gorm:"size:50;not null"`
	IPAddress  string    `gorm:"size:45"`
	UserAgent  string    `gorm:"type:text"`
	IsActive   bool      `gorm:"default:true"`
	ExpiresAt  time.Time `gorm:"not null"`
	CreatedAt  time.Time `gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt  time.Time `gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP"`
	LastUsedAt time.Time `gorm:"not null"`
}

type DataUpdateLog struct {
	ID             uint      `gorm:"primarykey"`
	Source         string    `gorm:"size:100;not null;index"`
	Type           string    `gorm:"size:50;not null"`
	Status         string    `gorm:"size:20;not null"`
	RecordsAdded   int       `gorm:"default:0"`
	RecordsUpdated int       `gorm:"default:0"`
	RecordsDeleted int       `gorm:"default:0"`
	ErrorMessage   string    `gorm:"type:text"`
	Duration       int       `gorm:"default:0"`
	CreatedAt      time.Time `gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP"`
}
