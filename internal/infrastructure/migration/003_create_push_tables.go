package migration

import (
	"github.com/azel-ko/final-ddd/internal/domain/entities"
	baseMigration "github.com/azel-ko/final-ddd/internal/pkg/database/migration"
	"gorm.io/gorm"
)

// PushTablesMigration 推送表迁移
type PushTablesMigration struct{}

func (m *PushTablesMigration) ID() string {
	return "003_create_push_tables"
}

func (m *PushTablesMigration) Up(db *gorm.DB) error {
	return Migration003CreatePushTables(db)
}

func (m *PushTablesMigration) Down(db *gorm.DB) error {
	// 删除推送相关表
	return db.Migrator().DropTable(
		&entities.PushSubscription{},
		&entities.PushTemplate{},
		&entities.PushLog{},
		&entities.PushDevice{},
	)
}

var _ baseMigration.Migration = &PushTablesMigration{}

// Migration003CreatePushTables 创建推送相关表
func Migration003CreatePushTables(db *gorm.DB) error {
	// 创建推送设备表
	if err := db.AutoMigrate(&entities.PushDevice{}); err != nil {
		return err
	}

	// 创建推送日志表
	if err := db.AutoMigrate(&entities.PushLog{}); err != nil {
		return err
	}

	// 创建推送模板表
	if err := db.AutoMigrate(&entities.PushTemplate{}); err != nil {
		return err
	}

	// 创建推送订阅表
	if err := db.AutoMigrate(&entities.PushSubscription{}); err != nil {
		return err
	}

	// 创建索引
	if err := createPushIndexes(db); err != nil {
		return err
	}

	// 插入默认推送模板
	if err := insertDefaultPushTemplates(db); err != nil {
		return err
	}

	return nil
}

// createPushIndexes 创建推送相关索引
func createPushIndexes(db *gorm.DB) error {
	// 推送设备表索引
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_push_devices_user_id ON push_devices(user_id)").Error; err != nil {
		return err
	}

	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_push_devices_token ON push_devices(token)").Error; err != nil {
		return err
	}

	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_push_devices_active ON push_devices(is_active)").Error; err != nil {
		return err
	}

	// 推送日志表索引
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_push_logs_user_id ON push_logs(user_id)").Error; err != nil {
		return err
	}

	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_push_logs_created_at ON push_logs(created_at)").Error; err != nil {
		return err
	}

	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_push_logs_success ON push_logs(success)").Error; err != nil {
		return err
	}

	// 推送订阅表索引
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_push_subscriptions_user_id ON push_subscriptions(user_id)").Error; err != nil {
		return err
	}

	return nil
}

// insertDefaultPushTemplates 插入默认推送模板
func insertDefaultPushTemplates(db *gorm.DB) error {
	templates := []*entities.PushTemplate{
		{
			Name:     "checkpoint_update",
			Type:     "checkpoint_update",
			Title:    "检查站状态更新",
			Body:     "{{.CheckpointName}} 状态从 {{.OldStatus}} 变更为 {{.NewStatus}}",
			Data:     `{"type":"checkpoint_update"}`,
			IsActive: true,
		},
		{
			Name:     "route_change",
			Type:     "route_change",
			Title:    "路线需要重新规划",
			Body:     "由于{{.Reason}}，建议重新规划路线",
			Data:     `{"type":"route_change"}`,
			IsActive: true,
		},
		{
			Name:     "system_alert",
			Type:     "system_alert",
			Title:    "系统通知",
			Body:     "{{.Content}}",
			Data:     `{"type":"system_alert"}`,
			IsActive: true,
		},
		{
			Name:     "trial_expiry_warning",
			Type:     "system_alert",
			Title:    "试用期即将到期",
			Body:     "您的试用期将在{{.Days}}天后到期，请及时续费以继续使用完整功能",
			Data:     `{"type":"trial_expiry","action":"upgrade"}`,
			IsActive: true,
		},
		{
			Name:     "subscription_expired",
			Type:     "system_alert",
			Title:    "订阅已过期",
			Body:     "您的订阅已过期，现在只能查看检查站信息，无法使用导航功能",
			Data:     `{"type":"subscription_expired","action":"renew"}`,
			IsActive: true,
		},
	}

	for _, template := range templates {
		// 检查模板是否已存在
		var existing entities.PushTemplate
		if err := db.Where("name = ?", template.Name).First(&existing).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				// 模板不存在，创建新模板
				if err := db.Create(template).Error; err != nil {
					return err
				}
			} else {
				return err
			}
		}
	}

	return nil
}
