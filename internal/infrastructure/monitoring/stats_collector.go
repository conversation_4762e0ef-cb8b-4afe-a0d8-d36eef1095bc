package monitoring

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/azel-ko/final-ddd/internal/infrastructure/cache"
	"github.com/azel-ko/final-ddd/internal/pkg/logger"
	"go.uber.org/zap"
)

// APICallStats API调用统计
type APICallStats struct {
	Endpoint     string    `json:"endpoint"`
	Method       string    `json:"method"`
	StatusCode   int       `json:"status_code"`
	ResponseTime int64     `json:"response_time"` // 毫秒
	Timestamp    time.Time `json:"timestamp"`
	ClientIP     string    `json:"client_ip"`
	UserAgent    string    `json:"user_agent"`
	UserID       string    `json:"user_id,omitempty"`
	RequestSize  int       `json:"request_size"`
	ResponseSize int       `json:"response_size"`
	ErrorMessage string    `json:"error_message,omitempty"`
}

// ErrorEntry 错误条目
type ErrorEntry struct {
	ID         string                 `json:"id"`
	Timestamp  time.Time              `json:"timestamp"`
	ErrorType  string                 `json:"error_type"`
	Message    string                 `json:"message"`
	StackTrace string                 `json:"stack_trace,omitempty"`
	UserID     string                 `json:"user_id,omitempty"`
	RequestID  string                 `json:"request_id,omitempty"`
	Endpoint   string                 `json:"endpoint"`
	Method     string                 `json:"method"`
	ClientIP   string                 `json:"client_ip"`
	UserAgent  string                 `json:"user_agent"`
	Context    map[string]interface{} `json:"context,omitempty"`
}

// LogEntry 日志条目
type LogEntry struct {
	ID        string                 `json:"id"`
	Timestamp time.Time              `json:"timestamp"`
	Level     string                 `json:"level"`
	Message   string                 `json:"message"`
	Source    string                 `json:"source"`
	Details   map[string]interface{} `json:"details,omitempty"`
}

// StatsCollector 统计收集器
type StatsCollector struct {
	cache      *cache.RedisCache
	mu         sync.RWMutex
	apiStats   map[string]*APICallStats
	errors     []ErrorEntry
	logs       []LogEntry
	maxEntries int
}

// NewStatsCollector 创建统计收集器
func NewStatsCollector(cache *cache.RedisCache) *StatsCollector {
	return &StatsCollector{
		cache:      cache,
		apiStats:   make(map[string]*APICallStats),
		errors:     make([]ErrorEntry, 0),
		logs:       make([]LogEntry, 0),
		maxEntries: 10000, // 最大条目数
	}
}

// RecordAPICall 记录API调用
func (sc *StatsCollector) RecordAPICall(stats APICallStats) {
	ctx := context.Background()

	// 生成统计键
	statsKey := fmt.Sprintf("api_stats:%s:%s", stats.Method, stats.Endpoint)

	// 更新Redis中的统计数据
	sc.updateAPIStatsInRedis(ctx, statsKey, stats)

	// 记录到内存（用于实时查询）
	sc.mu.Lock()
	sc.apiStats[statsKey] = &stats
	sc.mu.Unlock()

	logger.Debug("API call recorded",
		zap.String("endpoint", stats.Endpoint),
		zap.String("method", stats.Method),
		zap.Int("status", stats.StatusCode),
		zap.Int64("response_time", stats.ResponseTime))
}

// RecordError 记录错误
func (sc *StatsCollector) RecordError(entry ErrorEntry) {
	ctx := context.Background()

	// 生成唯一ID
	if entry.ID == "" {
		entry.ID = fmt.Sprintf("error_%d", time.Now().UnixNano())
	}

	// 存储到Redis
	errorKey := fmt.Sprintf("error:%s", entry.ID)
	errorData, _ := json.Marshal(entry)
	sc.cache.Set(ctx, errorKey, string(errorData), 24*time.Hour) // 保存24小时

	// 添加到错误列表
	errorListKey := "errors:list"
	sc.cache.LPush(ctx, errorListKey, entry.ID)
	sc.cache.LTrim(ctx, errorListKey, 0, int64(sc.maxEntries-1)) // 保持最大条目数

	// 记录到内存
	sc.mu.Lock()
	sc.errors = append([]ErrorEntry{entry}, sc.errors...)
	if len(sc.errors) > sc.maxEntries {
		sc.errors = sc.errors[:sc.maxEntries]
	}
	sc.mu.Unlock()

	logger.Error("Error recorded",
		zap.String("error_id", entry.ID),
		zap.String("error_type", entry.ErrorType),
		zap.String("message", entry.Message))
}

// RecordLog 记录日志
func (sc *StatsCollector) RecordLog(entry LogEntry) {
	ctx := context.Background()

	// 生成唯一ID
	if entry.ID == "" {
		entry.ID = fmt.Sprintf("log_%d", time.Now().UnixNano())
	}

	// 存储到Redis
	logKey := fmt.Sprintf("log:%s", entry.ID)
	logData, _ := json.Marshal(entry)
	sc.cache.Set(ctx, logKey, string(logData), 12*time.Hour) // 保存12小时

	// 添加到日志列表
	logListKey := "logs:list"
	sc.cache.LPush(ctx, logListKey, entry.ID)
	sc.cache.LTrim(ctx, logListKey, 0, int64(sc.maxEntries-1)) // 保持最大条目数

	// 记录到内存
	sc.mu.Lock()
	sc.logs = append([]LogEntry{entry}, sc.logs...)
	if len(sc.logs) > sc.maxEntries {
		sc.logs = sc.logs[:sc.maxEntries]
	}
	sc.mu.Unlock()
}

// GetAPIStats 获取API统计
func (sc *StatsCollector) GetAPIStats(ctx context.Context, endpoint, method string) (map[string]interface{}, error) {
	statsKey := fmt.Sprintf("api_stats:%s:%s", method, endpoint)

	// 从Redis获取统计数据
	data, err := sc.cache.HGetAll(ctx, statsKey)
	if err != nil {
		return nil, err
	}

	if len(data) == 0 {
		// 返回默认值
		return map[string]interface{}{
			"total_calls":       "0",
			"success_rate":      "100.0",
			"avg_response_time": "0.0",
			"error_count":       "0",
			"last_called":       time.Now().Format(time.RFC3339),
		}, nil
	}

	// 转换为interface{}类型
	result := make(map[string]interface{})
	for k, v := range data {
		result[k] = v
	}

	return result, nil
}

// GetAllAPIStats 获取所有API统计
func (sc *StatsCollector) GetAllAPIStats(ctx context.Context) ([]map[string]interface{}, error) {
	// 获取所有API统计键
	keys, err := sc.cache.Keys(ctx, "api_stats:*")
	if err != nil {
		return nil, err
	}

	var results []map[string]interface{}
	for _, key := range keys {
		data, err := sc.cache.HGetAll(ctx, key)
		if err != nil {
			continue
		}

		// 转换为interface{}类型
		result := make(map[string]interface{})
		for k, v := range data {
			result[k] = v
		}

		// 解析键获取endpoint和method
		parts := parseStatsKey(key)
		if len(parts) >= 2 {
			result["endpoint"] = parts[1]
			result["method"] = parts[0]
		}

		results = append(results, result)
	}

	return results, nil
}

// GetErrors 获取错误列表
func (sc *StatsCollector) GetErrors(ctx context.Context, limit int, offset int) ([]ErrorEntry, int64, error) {
	// 从Redis获取错误ID列表
	errorListKey := "errors:list"
	errorIDs, err := sc.cache.LRange(ctx, errorListKey, int64(offset), int64(offset+limit-1))
	if err != nil {
		return nil, 0, err
	}

	var errors []ErrorEntry
	for _, id := range errorIDs {
		errorKey := fmt.Sprintf("error:%s", id)
		errorData, err := sc.cache.GetString(ctx, errorKey)
		if err != nil {
			continue
		}

		var entry ErrorEntry
		if err := json.Unmarshal([]byte(errorData), &entry); err != nil {
			continue
		}

		errors = append(errors, entry)
	}

	// 获取总数
	total, _ := sc.cache.LLen(ctx, errorListKey)

	return errors, total, nil
}

// GetLogs 获取日志列表
func (sc *StatsCollector) GetLogs(ctx context.Context, limit int, offset int) ([]LogEntry, int64, error) {
	// 从Redis获取日志ID列表
	logListKey := "logs:list"
	logIDs, err := sc.cache.LRange(ctx, logListKey, int64(offset), int64(offset+limit-1))
	if err != nil {
		return nil, 0, err
	}

	var logs []LogEntry
	for _, id := range logIDs {
		logKey := fmt.Sprintf("log:%s", id)
		logData, err := sc.cache.GetString(ctx, logKey)
		if err != nil {
			continue
		}

		var entry LogEntry
		if err := json.Unmarshal([]byte(logData), &entry); err != nil {
			continue
		}

		logs = append(logs, entry)
	}

	// 获取总数
	total, _ := sc.cache.LLen(ctx, logListKey)

	return logs, total, nil
}

// updateAPIStatsInRedis 更新Redis中的API统计
func (sc *StatsCollector) updateAPIStatsInRedis(ctx context.Context, key string, stats APICallStats) {
	// 使用Redis Hash存储统计数据
	pipe := sc.cache.Pipeline()

	// 增加总调用次数
	pipe.HIncrBy(ctx, key, "total_calls", 1)

	// 更新成功/失败计数
	if stats.StatusCode >= 200 && stats.StatusCode < 400 {
		pipe.HIncrBy(ctx, key, "success_count", 1)
	} else {
		pipe.HIncrBy(ctx, key, "error_count", 1)
	}

	// 更新响应时间统计
	pipe.HIncrBy(ctx, key, "total_response_time", stats.ResponseTime)

	// 更新最后调用时间
	pipe.HSet(ctx, key, "last_called", stats.Timestamp.Format(time.RFC3339))

	// 设置过期时间（7天）
	pipe.Expire(ctx, key, 7*24*time.Hour)

	_, err := pipe.Exec(ctx)
	if err != nil {
		logger.Error("Failed to update API stats in Redis", zap.Error(err))
	}
}

// parseStatsKey 解析统计键
func parseStatsKey(key string) []string {
	// key格式: api_stats:METHOD:ENDPOINT
	if len(key) > 10 { // "api_stats:" 长度为10
		parts := key[10:] // 去掉前缀
		// 简单分割，实际项目中可能需要更复杂的解析
		return []string{"GET", parts} // 简化处理
	}
	return []string{}
}

// Cleanup 清理过期数据
func (sc *StatsCollector) Cleanup(ctx context.Context) {
	// 清理过期的错误和日志数据
	// Redis会自动处理过期键，这里主要清理内存数据

	sc.mu.Lock()
	defer sc.mu.Unlock()

	// 清理超过24小时的错误
	cutoff := time.Now().Add(-24 * time.Hour)
	var validErrors []ErrorEntry
	for _, err := range sc.errors {
		if err.Timestamp.After(cutoff) {
			validErrors = append(validErrors, err)
		}
	}
	sc.errors = validErrors

	// 清理超过12小时的日志
	cutoff = time.Now().Add(-12 * time.Hour)
	var validLogs []LogEntry
	for _, log := range sc.logs {
		if log.Timestamp.After(cutoff) {
			validLogs = append(validLogs, log)
		}
	}
	sc.logs = validLogs

	logger.Info("Monitoring data cleanup completed",
		zap.Int("errors_count", len(sc.errors)),
		zap.Int("logs_count", len(sc.logs)))
}
