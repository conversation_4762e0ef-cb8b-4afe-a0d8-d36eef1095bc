package websocket

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"github.com/azel-ko/final-ddd/internal/pkg/config"
	"github.com/azel-ko/final-ddd/internal/pkg/logger"
	"go.uber.org/zap"
)

// Hub WebSocket连接管理中心
type Hub struct {
	config      *config.WebSocketConfig
	clients     map[*Client]bool
	broadcast   chan []byte
	register    chan *Client
	unregister  chan *Client
	mu          sync.RWMutex
	ctx         context.Context
	cancel      context.CancelFunc
	running     bool
}

// Client WebSocket客户端
type Client struct {
	hub      *Hub
	conn     *websocket.Conn
	send     chan []byte
	userID   uint
	clientID string
	topics   map[string]bool // 订阅的主题
	mu       sync.RWMutex
}

// Message WebSocket消息结构
type Message struct {
	Type      string      `json:"type"`
	Topic     string      `json:"topic,omitempty"`
	Data      interface{} `json:"data,omitempty"`
	Timestamp time.Time   `json:"timestamp"`
	ClientID  string      `json:"client_id,omitempty"`
}

// MessageType 消息类型常量
const (
	MessageTypeSubscribe   = "subscribe"
	MessageTypeUnsubscribe = "unsubscribe"
	MessageTypeNotification = "notification"
	MessageTypeHeartbeat   = "heartbeat"
	MessageTypeError       = "error"
)

// Topic 主题常量
const (
	TopicCheckpointUpdate = "checkpoint_update"
	TopicRouteChange      = "route_change"
	TopicSystemAlert      = "system_alert"
)

var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		// 在生产环境中应该检查Origin
		return true
	},
}

// NewHub 创建新的WebSocket Hub
func NewHub(cfg *config.WebSocketConfig) *Hub {
	ctx, cancel := context.WithCancel(context.Background())
	
	return &Hub{
		config:     cfg,
		clients:    make(map[*Client]bool),
		broadcast:  make(chan []byte, 256),
		register:   make(chan *Client),
		unregister: make(chan *Client),
		ctx:        ctx,
		cancel:     cancel,
	}
}

// Run 启动Hub
func (h *Hub) Run() {
	if !h.config.Enabled {
		logger.Info("WebSocket is disabled")
		return
	}

	h.running = true
	logger.Info("WebSocket Hub started", zap.Int("max_connections", h.config.MaxConnections))

	for {
		select {
		case client := <-h.register:
			h.registerClient(client)

		case client := <-h.unregister:
			h.unregisterClient(client)

		case message := <-h.broadcast:
			h.broadcastMessage(message)

		case <-h.ctx.Done():
			logger.Info("WebSocket Hub stopping...")
			h.running = false
			return
		}
	}
}

// registerClient 注册客户端
func (h *Hub) registerClient(client *Client) {
	h.mu.Lock()
	defer h.mu.Unlock()

	if len(h.clients) >= h.config.MaxConnections {
		logger.Warn("Max connections reached, rejecting client", 
			zap.String("client_id", client.clientID))
		client.conn.Close()
		return
	}

	h.clients[client] = true
	logger.Info("Client registered", 
		zap.String("client_id", client.clientID),
		zap.Uint("user_id", client.userID),
		zap.Int("total_clients", len(h.clients)))

	// 发送欢迎消息
	welcomeMsg := Message{
		Type:      "welcome",
		Data:      map[string]interface{}{"client_id": client.clientID},
		Timestamp: time.Now(),
	}
	client.sendMessage(welcomeMsg)
}

// unregisterClient 注销客户端
func (h *Hub) unregisterClient(client *Client) {
	h.mu.Lock()
	defer h.mu.Unlock()

	if _, ok := h.clients[client]; ok {
		delete(h.clients, client)
		close(client.send)
		logger.Info("Client unregistered", 
			zap.String("client_id", client.clientID),
			zap.Int("total_clients", len(h.clients)))
	}
}

// broadcastMessage 广播消息
func (h *Hub) broadcastMessage(message []byte) {
	h.mu.RLock()
	defer h.mu.RUnlock()

	var msg Message
	if err := json.Unmarshal(message, &msg); err != nil {
		logger.Error("Failed to unmarshal broadcast message", zap.Error(err))
		return
	}

	for client := range h.clients {
		// 检查客户端是否订阅了该主题
		if msg.Topic != "" && !client.isSubscribed(msg.Topic) {
			continue
		}

		select {
		case client.send <- message:
		default:
			// 客户端发送缓冲区满，关闭连接
			h.unregisterClient(client)
		}
	}
}

// BroadcastToTopic 向特定主题广播消息
func (h *Hub) BroadcastToTopic(topic string, data interface{}) {
	message := Message{
		Type:      MessageTypeNotification,
		Topic:     topic,
		Data:      data,
		Timestamp: time.Now(),
	}

	messageBytes, err := json.Marshal(message)
	if err != nil {
		logger.Error("Failed to marshal broadcast message", zap.Error(err))
		return
	}

	select {
	case h.broadcast <- messageBytes:
	default:
		logger.Warn("Broadcast channel full, dropping message", zap.String("topic", topic))
	}
}

// GetClientCount 获取当前连接数
func (h *Hub) GetClientCount() int {
	h.mu.RLock()
	defer h.mu.RUnlock()
	return len(h.clients)
}

// Stop 停止Hub
func (h *Hub) Stop() {
	if h.running {
		h.cancel()
		
		// 关闭所有客户端连接
		h.mu.Lock()
		for client := range h.clients {
			client.conn.Close()
		}
		h.mu.Unlock()
	}
}

// HandleWebSocket 处理WebSocket连接
func (h *Hub) HandleWebSocket(c *gin.Context) {
	// 从JWT中获取用户ID（这里简化处理）
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}

	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		logger.Error("Failed to upgrade connection", zap.Error(err))
		return
	}

	clientID := fmt.Sprintf("client_%d_%d", userID, time.Now().Unix())
	client := &Client{
		hub:      h,
		conn:     conn,
		send:     make(chan []byte, 256),
		userID:   userID.(uint),
		clientID: clientID,
		topics:   make(map[string]bool),
	}

	// 注册客户端
	h.register <- client

	// 启动读写协程
	go client.writePump()
	go client.readPump()
}

// readPump 读取客户端消息
func (c *Client) readPump() {
	defer func() {
		c.hub.unregister <- c
		c.conn.Close()
	}()

	// 设置读取参数
	c.conn.SetReadLimit(int64(c.hub.config.MaxMessageSize))
	
	pongWait, _ := time.ParseDuration(c.hub.config.PongWait)
	c.conn.SetReadDeadline(time.Now().Add(pongWait))
	
	c.conn.SetPongHandler(func(string) error {
		c.conn.SetReadDeadline(time.Now().Add(pongWait))
		return nil
	})

	for {
		_, messageBytes, err := c.conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				logger.Error("WebSocket error", zap.Error(err))
			}
			break
		}

		var msg Message
		if err := json.Unmarshal(messageBytes, &msg); err != nil {
			logger.Error("Failed to unmarshal message", zap.Error(err))
			continue
		}

		c.handleMessage(msg)
	}
}

// writePump 向客户端发送消息
func (c *Client) writePump() {
	pingPeriod, _ := time.ParseDuration(c.hub.config.PingPeriod)
	ticker := time.NewTicker(pingPeriod)
	defer func() {
		ticker.Stop()
		c.conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.send:
			writeWait, _ := time.ParseDuration(c.hub.config.WriteWait)
			c.conn.SetWriteDeadline(time.Now().Add(writeWait))
			
			if !ok {
				c.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			w, err := c.conn.NextWriter(websocket.TextMessage)
			if err != nil {
				return
			}
			w.Write(message)

			// 批量发送队列中的其他消息
			n := len(c.send)
			for i := 0; i < n; i++ {
				w.Write([]byte{'\n'})
				w.Write(<-c.send)
			}

			if err := w.Close(); err != nil {
				return
			}

		case <-ticker.C:
			writeWait, _ := time.ParseDuration(c.hub.config.WriteWait)
			c.conn.SetWriteDeadline(time.Now().Add(writeWait))
			if err := c.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// handleMessage 处理客户端消息
func (c *Client) handleMessage(msg Message) {
	switch msg.Type {
	case MessageTypeSubscribe:
		if topic, ok := msg.Data.(string); ok {
			c.subscribe(topic)
		}
	case MessageTypeUnsubscribe:
		if topic, ok := msg.Data.(string); ok {
			c.unsubscribe(topic)
		}
	case MessageTypeHeartbeat:
		// 响应心跳
		response := Message{
			Type:      MessageTypeHeartbeat,
			Timestamp: time.Now(),
		}
		c.sendMessage(response)
	}
}

// subscribe 订阅主题
func (c *Client) subscribe(topic string) {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	c.topics[topic] = true
	logger.Info("Client subscribed to topic", 
		zap.String("client_id", c.clientID),
		zap.String("topic", topic))

	// 发送订阅确认
	response := Message{
		Type:      "subscribed",
		Topic:     topic,
		Timestamp: time.Now(),
	}
	c.sendMessage(response)
}

// unsubscribe 取消订阅主题
func (c *Client) unsubscribe(topic string) {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	delete(c.topics, topic)
	logger.Info("Client unsubscribed from topic", 
		zap.String("client_id", c.clientID),
		zap.String("topic", topic))

	// 发送取消订阅确认
	response := Message{
		Type:      "unsubscribed",
		Topic:     topic,
		Timestamp: time.Now(),
	}
	c.sendMessage(response)
}

// isSubscribed 检查是否订阅了主题
func (c *Client) isSubscribed(topic string) bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.topics[topic]
}

// sendMessage 发送消息给客户端
func (c *Client) sendMessage(msg Message) {
	messageBytes, err := json.Marshal(msg)
	if err != nil {
		logger.Error("Failed to marshal message", zap.Error(err))
		return
	}

	select {
	case c.send <- messageBytes:
	default:
		logger.Warn("Client send buffer full", zap.String("client_id", c.clientID))
	}
}