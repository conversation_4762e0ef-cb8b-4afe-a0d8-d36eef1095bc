package push

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/azel-ko/final-ddd/internal/pkg/logger"
	"go.uber.org/zap"
)

// MiniprogramClient 小程序推送客户端
type MiniprogramClient struct {
	appID     string
	appSecret string
	client    *http.Client
}

// MiniprogramConfig 小程序配置
type MiniprogramConfig struct {
	Enabled   bool   `mapstructure:"enabled"`
	AppID     string `mapstructure:"app_id"`
	AppSecret string `mapstructure:"app_secret"`
}

// AccessTokenResponse 访问令牌响应
type AccessTokenResponse struct {
	AccessToken string `json:"access_token"`
	ExpiresIn   int    `json:"expires_in"`
	ErrCode     int    `json:"errcode"`
	ErrMsg      string `json:"errmsg"`
}

// TemplateMessageRequest 模板消息请求
type TemplateMessageRequest struct {
	ToUser     string                 `json:"touser"`
	TemplateID string                 `json:"template_id"`
	Page       string                 `json:"page,omitempty"`
	Data       map[string]interface{} `json:"data"`
}

// TemplateMessageResponse 模板消息响应
type TemplateMessageResponse struct {
	ErrCode int    `json:"errcode"`
	ErrMsg  string `json:"errmsg"`
	MsgID   int64  `json:"msgid,omitempty"`
}

// NewMiniprogramClient 创建小程序客户端
func NewMiniprogramClient(config *MiniprogramConfig) (*MiniprogramClient, error) {
	if !config.Enabled {
		return nil, fmt.Errorf("miniprogram push is disabled")
	}

	if config.AppID == "" || config.AppSecret == "" {
		return nil, fmt.Errorf("miniprogram configuration incomplete: app_id and app_secret are required")
	}

	return &MiniprogramClient{
		appID:     config.AppID,
		appSecret: config.AppSecret,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}, nil
}

// SendTemplateMessage 发送模板消息
func (c *MiniprogramClient) SendTemplateMessage(ctx context.Context, openID string, message PushMessage) (string, error) {
	// 获取访问令牌
	accessToken, err := c.getAccessToken(ctx)
	if err != nil {
		return "", fmt.Errorf("failed to get access token: %w", err)
	}

	// 构建模板消息数据
	templateData := c.buildTemplateData(message)

	request := TemplateMessageRequest{
		ToUser:     openID,
		TemplateID: c.getTemplateID(message),
		Data:       templateData,
	}

	// 如果有页面跳转信息
	if page, ok := message.GetData()["page"].(string); ok {
		request.Page = page
	}

	// 发送模板消息
	url := fmt.Sprintf("https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=%s", accessToken)

	jsonData, err := json.Marshal(request)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %w", err)
	}

	resp, err := c.client.Post(url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	var response TemplateMessageResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return "", fmt.Errorf("failed to decode response: %w", err)
	}

	if response.ErrCode != 0 {
		return "", fmt.Errorf("wechat API error: %s (code: %d)", response.ErrMsg, response.ErrCode)
	}

	logger.Info("Miniprogram template message sent successfully",
		zap.String("open_id", openID),
		zap.Int64("msg_id", response.MsgID))

	return fmt.Sprintf("%d", response.MsgID), nil
}

// getAccessToken 获取访问令牌
func (c *MiniprogramClient) getAccessToken(ctx context.Context) (string, error) {
	url := fmt.Sprintf("https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s",
		c.appID, c.appSecret)

	resp, err := c.client.Get(url)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	var tokenResp AccessTokenResponse
	if err := json.NewDecoder(resp.Body).Decode(&tokenResp); err != nil {
		return "", err
	}

	if tokenResp.ErrCode != 0 {
		return "", fmt.Errorf("failed to get access token: %s (code: %d)", tokenResp.ErrMsg, tokenResp.ErrCode)
	}

	return tokenResp.AccessToken, nil
}

// buildTemplateData 构建模板数据
func (c *MiniprogramClient) buildTemplateData(message PushMessage) map[string]interface{} {
	data := make(map[string]interface{})

	// 标准字段
	data["first"] = map[string]string{"value": message.GetTitle()}
	data["remark"] = map[string]string{"value": message.GetBody()}

	// 自定义数据字段
	if customData := message.GetData(); customData != nil {
		for key, value := range customData {
			if key != "template_id" && key != "page" {
				data[key] = map[string]string{"value": fmt.Sprintf("%v", value)}
			}
		}
	}

	return data
}

// getTemplateID 获取模板ID
func (c *MiniprogramClient) getTemplateID(message PushMessage) string {
	if data := message.GetData(); data != nil {
		if templateID, ok := data["template_id"].(string); ok {
			return templateID
		}
	}

	// 根据消息类型返回默认模板ID
	if data := message.GetData(); data != nil {
		if msgType, ok := data["type"].(string); ok {
			switch msgType {
			case "checkpoint_update":
				return "checkpoint_update_template_id"
			case "route_change":
				return "route_change_template_id"
			case "system_alert":
				return "system_alert_template_id"
			}
		}
	}

	return "default_template_id"
}
