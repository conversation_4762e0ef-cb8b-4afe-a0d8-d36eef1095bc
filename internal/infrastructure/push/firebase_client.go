package push

import (
	"context"
	"encoding/json"
	"fmt"

	firebase "firebase.google.com/go/v4"
	"firebase.google.com/go/v4/messaging"
	"google.golang.org/api/option"

	"github.com/azel-ko/final-ddd/internal/pkg/config"
	"github.com/azel-ko/final-ddd/internal/pkg/logger"
	"go.uber.org/zap"
)

// FirebaseClient Firebase推送客户端
type FirebaseClient struct {
	client *messaging.Client
	config *config.FirebaseConfig
}

// PushMessage 推送消息接口
type PushMessage interface {
	GetTitle() string
	GetBody() string
	GetData() map[string]interface{}
	GetPriority() string
	GetSound() string
}

// NewFirebaseClient 创建Firebase客户端
func NewFirebaseClient(config *config.FirebaseConfig) (*FirebaseClient, error) {
	if !config.Enabled {
		return nil, fmt.Errorf("Firebase push is disabled")
	}

	// 初始化Firebase应用
	opt := option.WithCredentialsFile(config.CredentialsFile)
	app, err := firebase.NewApp(context.Background(), nil, opt)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize Firebase app: %w", err)
	}

	// 获取Messaging客户端
	client, err := app.Messaging(context.Background())
	if err != nil {
		return nil, fmt.Errorf("failed to get Firebase messaging client: %w", err)
	}

	return &FirebaseClient{
		client: client,
		config: config,
	}, nil
}

// SendToToken 向指定token发送推送
func (c *FirebaseClient) SendToToken(ctx context.Context, token string, message PushMessage) (string, error) {
	// 构建Firebase消息
	fcmMessage := &messaging.Message{
		Token: token,
		Notification: &messaging.Notification{
			Title: message.GetTitle(),
			Body:  message.GetBody(),
		},
		Data: c.convertDataToStringMap(message.GetData()),
		Android: &messaging.AndroidConfig{
			Priority: c.getAndroidPriority(message.GetPriority()),
			Notification: &messaging.AndroidNotification{
				Sound:                "default",
				DefaultSound:         true,
				DefaultVibrateTimings: true,
				DefaultLightSettings:  true,
			},
		},
	}

	// 发送消息
	response, err := c.client.Send(ctx, fcmMessage)
	if err != nil {
		logger.Error("Failed to send Firebase message", 
			zap.String("token", token),
			zap.String("title", message.GetTitle()),
			zap.Error(err))
		return "", err
	}

	logger.Info("Firebase message sent successfully", 
		zap.String("message_id", response),
		zap.String("token", token))

	return response, nil
}

// SendToTokens 向多个token发送推送
func (c *FirebaseClient) SendToTokens(ctx context.Context, tokens []string, message PushMessage) (*messaging.BatchResponse, error) {
	if len(tokens) == 0 {
		return nil, fmt.Errorf("no tokens provided")
	}

	// 构建多播消息
	multicastMessage := &messaging.MulticastMessage{
		Notification: &messaging.Notification{
			Title: message.GetTitle(),
			Body:  message.GetBody(),
		},
		Data:   c.convertDataToStringMap(message.GetData()),
		Tokens: tokens,
		Android: &messaging.AndroidConfig{
			Priority: c.getAndroidPriority(message.GetPriority()),
			Notification: &messaging.AndroidNotification{
				Sound:                "default",
				DefaultSound:         true,
				DefaultVibrateTimings: true,
				DefaultLightSettings:  true,
			},
		},
	}

	// 发送多播消息
	response, err := c.client.SendMulticast(ctx, multicastMessage)
	if err != nil {
		logger.Error("Failed to send Firebase multicast message", 
			zap.Int("token_count", len(tokens)),
			zap.String("title", message.GetTitle()),
			zap.Error(err))
		return nil, err
	}

	logger.Info("Firebase multicast message sent", 
		zap.Int("success_count", response.SuccessCount),
		zap.Int("failure_count", response.FailureCount),
		zap.Int("total_tokens", len(tokens)))

	return response, nil
}

// SendToTopic 向主题发送推送
func (c *FirebaseClient) SendToTopic(ctx context.Context, topic string, message PushMessage) (string, error) {
	// 构建主题消息
	topicMessage := &messaging.Message{
		Topic: topic,
		Notification: &messaging.Notification{
			Title: message.GetTitle(),
			Body:  message.GetBody(),
		},
		Data: c.convertDataToStringMap(message.GetData()),
		Android: &messaging.AndroidConfig{
			Priority: c.getAndroidPriority(message.GetPriority()),
			Notification: &messaging.AndroidNotification{
				Sound:                "default",
				DefaultSound:         true,
				DefaultVibrateTimings: true,
				DefaultLightSettings:  true,
			},
		},
	}

	// 发送消息
	response, err := c.client.Send(ctx, topicMessage)
	if err != nil {
		logger.Error("Failed to send Firebase topic message", 
			zap.String("topic", topic),
			zap.String("title", message.GetTitle()),
			zap.Error(err))
		return "", err
	}

	logger.Info("Firebase topic message sent successfully", 
		zap.String("message_id", response),
		zap.String("topic", topic))

	return response, nil
}

// SubscribeToTopic 订阅主题
func (c *FirebaseClient) SubscribeToTopic(ctx context.Context, tokens []string, topic string) error {
	if len(tokens) == 0 {
		return fmt.Errorf("no tokens provided")
	}

	response, err := c.client.SubscribeToTopic(ctx, tokens, topic)
	if err != nil {
		logger.Error("Failed to subscribe to topic", 
			zap.String("topic", topic),
			zap.Int("token_count", len(tokens)),
			zap.Error(err))
		return err
	}

	logger.Info("Subscribed to topic successfully", 
		zap.String("topic", topic),
		zap.Int("success_count", response.SuccessCount),
		zap.Int("failure_count", response.FailureCount))

	return nil
}

// UnsubscribeFromTopic 取消订阅主题
func (c *FirebaseClient) UnsubscribeFromTopic(ctx context.Context, tokens []string, topic string) error {
	if len(tokens) == 0 {
		return fmt.Errorf("no tokens provided")
	}

	response, err := c.client.UnsubscribeFromTopic(ctx, tokens, topic)
	if err != nil {
		logger.Error("Failed to unsubscribe from topic", 
			zap.String("topic", topic),
			zap.Int("token_count", len(tokens)),
			zap.Error(err))
		return err
	}

	logger.Info("Unsubscribed from topic successfully", 
		zap.String("topic", topic),
		zap.Int("success_count", response.SuccessCount),
		zap.Int("failure_count", response.FailureCount))

	return nil
}

// ValidateToken 验证token有效性
func (c *FirebaseClient) ValidateToken(ctx context.Context, token string) error {
	// 发送一个测试消息来验证token
	testMessage := &messaging.Message{
		Token: token,
		Data: map[string]string{
			"test": "true",
		},
		Android: &messaging.AndroidConfig{
			Priority: "normal",
		},
	}

	_, err := c.client.Send(ctx, testMessage)
	return err
}

// 私有方法

// convertDataToStringMap 将数据转换为字符串映射
func (c *FirebaseClient) convertDataToStringMap(data map[string]interface{}) map[string]string {
	if data == nil {
		return nil
	}

	stringMap := make(map[string]string)
	for key, value := range data {
		switch v := value.(type) {
		case string:
			stringMap[key] = v
		case int, int32, int64, float32, float64, bool:
			stringMap[key] = fmt.Sprintf("%v", v)
		default:
			// 复杂类型转换为JSON字符串
			if jsonBytes, err := json.Marshal(v); err == nil {
				stringMap[key] = string(jsonBytes)
			}
		}
	}

	return stringMap
}

// getAndroidPriority 获取Android优先级
func (c *FirebaseClient) getAndroidPriority(priority string) string {
	switch priority {
	case "high":
		return "high"
	case "normal":
		return "normal"
	default:
		return "normal"
	}
}

// SimplePushMessage 简单推送消息实现
type SimplePushMessage struct {
	Title    string                 `json:"title"`
	Body     string                 `json:"body"`
	Data     map[string]interface{} `json:"data,omitempty"`
	Priority string                 `json:"priority,omitempty"`
	Sound    string                 `json:"sound,omitempty"`
}

func (m *SimplePushMessage) GetTitle() string                 { return m.Title }
func (m *SimplePushMessage) GetBody() string                  { return m.Body }
func (m *SimplePushMessage) GetData() map[string]interface{}  { return m.Data }
func (m *SimplePushMessage) GetPriority() string             { return m.Priority }
func (m *SimplePushMessage) GetSound() string                { return m.Sound }