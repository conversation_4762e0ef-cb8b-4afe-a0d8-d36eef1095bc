package push

import (
	"context"
	"fmt"

	"github.com/sideshow/apns2"
	"github.com/sideshow/apns2/payload"
	"github.com/sideshow/apns2/token"

	"github.com/azel-ko/final-ddd/internal/pkg/config"
	"github.com/azel-ko/final-ddd/internal/pkg/logger"
	"go.uber.org/zap"
)

// APNSClient APNs推送客户端
type APNSClient struct {
	client *apns2.Client
	config *config.APNSConfig
}

// NewAPNSClient 创建APNs客户端
func NewAPNSClient(config *config.APNSConfig) (*APNSClient, error) {
	if !config.Enabled {
		return nil, fmt.Errorf("APNs push is disabled")
	}

	var client *apns2.Client

	// 使用Token认证（推荐方式）
	if config.KeyFile != "" && config.KeyID != "" && config.TeamID != "" {
		authKey, err := token.AuthKeyFromFile(config.KeyFile)
		if err != nil {
			return nil, fmt.Errorf("failed to load APNs auth key: %w", err)
		}

		tokenProvider := &token.Token{
			AuthKey: authKey,
			KeyID:   config.KeyID,
			TeamID:  config.TeamID,
		}

		if config.Production {
			client = apns2.NewTokenClient(tokenProvider).Production()
		} else {
			client = apns2.NewTokenClient(tokenProvider).Development()
		}
	} else {
		return nil, fmt.Errorf("APNs configuration incomplete: key_file, key_id, and team_id are required")
	}

	return &APNSClient{
		client: client,
		config: config,
	}, nil
}

// SendToToken 向指定token发送推送
func (c *APNSClient) SendToToken(ctx context.Context, deviceToken string, message PushMessage) (string, error) {
	// 构建APNs通知
	notification := &apns2.Notification{
		DeviceToken: deviceToken,
		Topic:       c.config.BundleID,
		Payload:     c.buildPayload(message),
		Priority:    c.getPriority(message.GetPriority()),
	}

	// 发送通知
	response, err := c.client.PushWithContext(ctx, notification)
	if err != nil {
		logger.Error("Failed to send APNs notification",
			zap.String("device_token", deviceToken),
			zap.String("title", message.GetTitle()),
			zap.Error(err))
		return "", err
	}

	// 检查响应状态
	if response.StatusCode != 200 {
		errorMsg := fmt.Sprintf("APNs error: %s (status: %d)", response.Reason, response.StatusCode)
		logger.Error("APNs notification failed",
			zap.String("device_token", deviceToken),
			zap.String("reason", response.Reason),
			zap.Int("status_code", response.StatusCode))
		return "", fmt.Errorf(errorMsg)
	}

	logger.Info("APNs notification sent successfully",
		zap.String("apns_id", response.ApnsID),
		zap.String("device_token", deviceToken))

	return response.ApnsID, nil
}

// SendToTokens 向多个token发送推送
func (c *APNSClient) SendToTokens(ctx context.Context, deviceTokens []string, message PushMessage) ([]APNSResult, error) {
	if len(deviceTokens) == 0 {
		return nil, fmt.Errorf("no device tokens provided")
	}

	results := make([]APNSResult, 0, len(deviceTokens))

	// 并发发送通知
	resultChan := make(chan APNSResult, len(deviceTokens))

	for _, token := range deviceTokens {
		go func(deviceToken string) {
			messageID, err := c.SendToToken(ctx, deviceToken, message)
			result := APNSResult{
				DeviceToken: deviceToken,
				Success:     err == nil,
				MessageID:   messageID,
			}
			if err != nil {
				result.Error = err.Error()
			}
			resultChan <- result
		}(token)
	}

	// 收集结果
	for i := 0; i < len(deviceTokens); i++ {
		result := <-resultChan
		results = append(results, result)
	}

	// 统计结果
	successCount := 0
	for _, result := range results {
		if result.Success {
			successCount++
		}
	}

	logger.Info("APNs batch notification completed",
		zap.Int("total_tokens", len(deviceTokens)),
		zap.Int("success_count", successCount),
		zap.Int("failure_count", len(deviceTokens)-successCount))

	return results, nil
}

// ValidateToken 验证token有效性
func (c *APNSClient) ValidateToken(ctx context.Context, deviceToken string) error {
	// 发送一个测试通知来验证token
	testMessage := &SimplePushMessage{
		Title: "Test",
		Body:  "Token validation",
		Data: map[string]interface{}{
			"test": true,
		},
		Priority: "normal",
	}

	_, err := c.SendToToken(ctx, deviceToken, testMessage)
	return err
}

// 私有方法

// buildPayload 构建APNs载荷
func (c *APNSClient) buildPayload(message PushMessage) *payload.Payload {
	p := payload.NewPayload()

	// 设置通知内容
	p.Alert(message.GetTitle())
	p.AlertBody(message.GetBody())
	p.Sound(c.getSound(message.GetSound()))

	// 设置自定义数据
	if data := message.GetData(); data != nil {
		for key, value := range data {
			p.Custom(key, value)
		}
	}

	// 设置badge（如果有）
	if badgeData, ok := message.GetData()["badge"]; ok {
		if badge, ok := badgeData.(int); ok {
			p.Badge(badge)
		}
	}

	return p
}

// getPriority 获取推送优先级
func (c *APNSClient) getPriority(priority string) int {
	switch priority {
	case "high":
		return apns2.PriorityHigh
	case "normal":
		return apns2.PriorityLow
	default:
		return apns2.PriorityLow
	}
}

// getSound 获取声音设置
func (c *APNSClient) getSound(sound string) string {
	if sound == "" {
		return "default"
	}
	return sound
}

// APNSResult APNs推送结果
type APNSResult struct {
	DeviceToken string `json:"device_token"`
	Success     bool   `json:"success"`
	MessageID   string `json:"message_id,omitempty"`
	Error       string `json:"error,omitempty"`
}
