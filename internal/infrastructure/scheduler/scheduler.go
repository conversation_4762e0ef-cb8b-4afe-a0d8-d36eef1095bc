package scheduler

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/azel-ko/final-ddd/internal/pkg/config"
	"github.com/azel-ko/final-ddd/internal/pkg/logger"
	"github.com/robfig/cron/v3"
	"go.uber.org/zap"
)

// Scheduler 定时任务调度器
type Scheduler struct {
	cron    *cron.Cron
	config  *config.SchedulerConfig
	jobs    map[string]*JobWrapper
	mu      sync.RWMutex
	ctx     context.Context
	cancel  context.CancelFunc
	running bool
}

// JobWrapper 任务包装器
type JobWrapper struct {
	ID       string
	Name     string
	Spec     string
	Job      Job
	Config   config.JobConfig
	EntryID  cron.EntryID
	LastRun  time.Time
	NextRun  time.Time
	RunCount int64
	Status   JobStatus
}

// Job 任务接口
type Job interface {
	Execute(ctx context.Context) error
	Name() string
}

// JobStatus 任务状态
type JobStatus string

const (
	JobStatusIdle    JobStatus = "idle"
	JobStatusRunning JobStatus = "running"
	JobStatusError   JobStatus = "error"
	JobStatusStopped JobStatus = "stopped"
)

// NewScheduler 创建新的调度器
func NewScheduler(cfg *config.SchedulerConfig) *Scheduler {
	ctx, cancel := context.WithCancel(context.Background())

	// 设置时区
	location, err := time.LoadLocation(cfg.Timezone)
	if err != nil {
		logger.Error("Failed to load timezone, using UTC", zap.String("timezone", cfg.Timezone), zap.Error(err))
		location = time.UTC
	}

	cronInstance := cron.New(
		cron.WithLocation(location),
		cron.WithLogger(cron.DefaultLogger),
		cron.WithChain(cron.Recover(cron.DefaultLogger)),
	)

	return &Scheduler{
		cron:   cronInstance,
		config: cfg,
		jobs:   make(map[string]*JobWrapper),
		ctx:    ctx,
		cancel: cancel,
	}
}

// AddJob 添加任务
func (s *Scheduler) AddJob(id string, job Job, spec string, jobConfig config.JobConfig) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !jobConfig.Enabled {
		logger.Info("Job is disabled, skipping", zap.String("job_id", id))
		return nil
	}

	// 创建任务包装器
	wrapper := &JobWrapper{
		ID:     id,
		Name:   job.Name(),
		Spec:   spec,
		Job:    job,
		Config: jobConfig,
		Status: JobStatusIdle,
	}

	// 创建带超时的任务执行器
	timeoutJob := s.createTimeoutJob(wrapper)

	// 添加到cron调度器
	entryID, err := s.cron.AddFunc(spec, timeoutJob)
	if err != nil {
		return fmt.Errorf("failed to add job %s: %w", id, err)
	}

	wrapper.EntryID = entryID
	s.jobs[id] = wrapper

	logger.Info("Job added successfully",
		zap.String("job_id", id),
		zap.String("job_name", job.Name()),
		zap.String("spec", spec))

	return nil
}

// createTimeoutJob 创建带超时的任务执行器
func (s *Scheduler) createTimeoutJob(wrapper *JobWrapper) func() {
	return func() {
		// 解析超时时间
		timeout, err := time.ParseDuration(wrapper.Config.Timeout)
		if err != nil {
			logger.Error("Invalid timeout duration, using default 60s",
				zap.String("job_id", wrapper.ID),
				zap.String("timeout", wrapper.Config.Timeout),
				zap.Error(err))
			timeout = 60 * time.Second
		}

		// 创建带超时的上下文
		ctx, cancel := context.WithTimeout(s.ctx, timeout)
		defer cancel()

		// 更新任务状态
		s.updateJobStatus(wrapper.ID, JobStatusRunning)
		wrapper.LastRun = time.Now()
		wrapper.RunCount++

		logger.Info("Job started",
			zap.String("job_id", wrapper.ID),
			zap.String("job_name", wrapper.Name),
			zap.Int64("run_count", wrapper.RunCount))

		// 执行任务
		err = wrapper.Job.Execute(ctx)

		if err != nil {
			s.updateJobStatus(wrapper.ID, JobStatusError)
			logger.Error("Job execution failed",
				zap.String("job_id", wrapper.ID),
				zap.String("job_name", wrapper.Name),
				zap.Error(err))
		} else {
			s.updateJobStatus(wrapper.ID, JobStatusIdle)
			logger.Info("Job completed successfully",
				zap.String("job_id", wrapper.ID),
				zap.String("job_name", wrapper.Name))
		}
	}
}

// updateJobStatus 更新任务状态
func (s *Scheduler) updateJobStatus(jobID string, status JobStatus) {
	s.mu.Lock()
	defer s.mu.Unlock()

	if wrapper, exists := s.jobs[jobID]; exists {
		wrapper.Status = status
		if status == JobStatusIdle {
			// 更新下次运行时间
			for _, entry := range s.cron.Entries() {
				if entry.ID == wrapper.EntryID {
					wrapper.NextRun = entry.Next
					break
				}
			}
		}
	}
}

// Start 启动调度器
func (s *Scheduler) Start() error {
	if !s.config.Enabled {
		logger.Info("Scheduler is disabled")
		return nil
	}

	s.mu.Lock()
	defer s.mu.Unlock()

	if s.running {
		return fmt.Errorf("scheduler is already running")
	}

	s.cron.Start()
	s.running = true

	logger.Info("Scheduler started",
		zap.Int("job_count", len(s.jobs)),
		zap.String("timezone", s.config.Timezone))

	return nil
}

// Stop 停止调度器
func (s *Scheduler) Stop() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.running {
		return nil
	}

	// 停止cron调度器
	ctx := s.cron.Stop()

	// 等待所有任务完成或超时
	select {
	case <-ctx.Done():
		logger.Info("All jobs completed gracefully")
	case <-time.After(30 * time.Second):
		logger.Warn("Timeout waiting for jobs to complete")
	}

	// 取消上下文
	s.cancel()
	s.running = false

	// 更新所有任务状态
	for _, wrapper := range s.jobs {
		wrapper.Status = JobStatusStopped
	}

	logger.Info("Scheduler stopped")
	return nil
}

// RemoveJob 移除任务
func (s *Scheduler) RemoveJob(jobID string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	wrapper, exists := s.jobs[jobID]
	if !exists {
		return fmt.Errorf("job %s not found", jobID)
	}

	s.cron.Remove(wrapper.EntryID)
	delete(s.jobs, jobID)

	logger.Info("Job removed", zap.String("job_id", jobID))
	return nil
}

// GetJobStatus 获取任务状态
func (s *Scheduler) GetJobStatus(jobID string) (*JobWrapper, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	wrapper, exists := s.jobs[jobID]
	if !exists {
		return nil, fmt.Errorf("job %s not found", jobID)
	}

	// 创建副本以避免并发问题
	result := *wrapper
	return &result, nil
}

// GetAllJobs 获取所有任务状态
func (s *Scheduler) GetAllJobs() map[string]*JobWrapper {
	s.mu.RLock()
	defer s.mu.RUnlock()

	result := make(map[string]*JobWrapper)
	for id, wrapper := range s.jobs {
		// 创建副本以避免并发问题
		jobCopy := *wrapper
		result[id] = &jobCopy
	}

	return result
}

// IsRunning 检查调度器是否运行中
func (s *Scheduler) IsRunning() bool {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.running
}
