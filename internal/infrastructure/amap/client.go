package amap

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/azel-ko/final-ddd/internal/pkg/config"
	"github.com/azel-ko/final-ddd/internal/pkg/logger"
	"go.uber.org/zap"
	"golang.org/x/time/rate"
)

// FlexibleStringArray 可以处理字符串或字符串数组的JSON字段
type FlexibleStringArray []string

// UnmarshalJSON 自定义JSON反序列化，支持字符串和字符串数组
func (fsa *FlexibleStringArray) UnmarshalJSON(data []byte) error {
	// 尝试解析为字符串数组
	var arr []string
	if err := json.Unmarshal(data, &arr); err == nil {
		*fsa = FlexibleStringArray(arr)
		return nil
	}

	// 如果失败，尝试解析为单个字符串
	var str string
	if err := json.Unmarshal(data, &str); err == nil {
		*fsa = FlexibleStringArray([]string{str})
		return nil
	}

	// 如果都失败，返回空数组
	*fsa = FlexibleStringArray([]string{})
	return nil
}

// FlexibleString 可以处理字符串或字符串数组的JSON字段，但返回单个字符串
type FlexibleString string

// UnmarshalJSON 自定义JSON反序列化，支持字符串和字符串数组，返回第一个字符串
func (fs *FlexibleString) UnmarshalJSON(data []byte) error {
	// 尝试解析为字符串
	var str string
	if err := json.Unmarshal(data, &str); err == nil {
		*fs = FlexibleString(str)
		return nil
	}

	// 如果失败，尝试解析为字符串数组，取第一个
	var arr []string
	if err := json.Unmarshal(data, &arr); err == nil {
		if len(arr) > 0 {
			*fs = FlexibleString(arr[0])
		} else {
			*fs = FlexibleString("")
		}
		return nil
	}

	// 如果都失败，返回空字符串
	*fs = FlexibleString("")
	return nil
}

// Client 高德地图API客户端
type Client struct {
	httpClient *http.Client
	config     *config.AmapConfig
	limiter    *rate.Limiter
	baseURL    string
}

// NewClient 创建高德地图API客户端
func NewClient(cfg *config.AmapConfig) *Client {
	timeout, err := time.ParseDuration(cfg.Timeout)
	if err != nil {
		logger.Warn("Invalid timeout duration, using default 30s",
			zap.String("timeout", cfg.Timeout))
		timeout = 30 * time.Second
	}

	httpClient := &http.Client{
		Timeout: timeout,
		Transport: &http.Transport{
			MaxIdleConns:        100,
			MaxIdleConnsPerHost: 10,
			IdleConnTimeout:     90 * time.Second,
		},
	}

	// 创建限流器，确保有合理的默认值
	requestsPerSecond := cfg.RateLimit.RequestsPerSecond
	if requestsPerSecond <= 0 {
		requestsPerSecond = 10 // 默认每秒10个请求
	}

	burst := cfg.RateLimit.Burst
	if burst <= 0 {
		burst = 20 // 默认突发20个请求
	}

	limiter := rate.NewLimiter(
		rate.Limit(requestsPerSecond),
		burst,
	)

	logger.Info("Amap client initialized",
		zap.Int("requests_per_second", requestsPerSecond),
		zap.Int("burst", burst),
		zap.String("base_url", cfg.BaseURL))

	return &Client{
		httpClient: httpClient,
		config:     cfg,
		limiter:    limiter,
		baseURL:    cfg.BaseURL,
	}
}

// APIResponse 高德地图API通用响应结构
type APIResponse struct {
	Status     string      `json:"status"`
	Info       string      `json:"info"`
	InfoCode   string      `json:"infocode"`
	Count      string      `json:"count,omitempty"`
	Suggestion interface{} `json:"suggestion,omitempty"`
}

// request 发送HTTP请求
func (c *Client) request(ctx context.Context, endpoint string, params map[string]string, result interface{}) error {
	// 限流控制
	if err := c.limiter.Wait(ctx); err != nil {
		return fmt.Errorf("rate limit wait failed: %w", err)
	}

	// 构建URL
	reqURL, err := c.buildURL(endpoint, params)
	if err != nil {
		return fmt.Errorf("failed to build URL: %w", err)
	}

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "GET", reqURL, nil)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Beijing-Navigation/1.0")
	req.Header.Set("Accept", "application/json")

	// 发送请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body: %w", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("HTTP error: %d, body: %s", resp.StatusCode, string(body))
	}

	// 解析响应
	if err := json.Unmarshal(body, result); err != nil {
		return fmt.Errorf("failed to unmarshal response: %w", err)
	}

	// 检查API响应状态
	if apiResp, ok := result.(interface{ GetStatus() string }); ok {
		if status := apiResp.GetStatus(); status != "1" {
			return fmt.Errorf("API error: status=%s", status)
		}
	}

	logger.Debug("AMap API request completed",
		zap.String("endpoint", endpoint),
		zap.String("url", reqURL),
		zap.Int("status", resp.StatusCode),
		zap.Int("response_size", len(body)))

	return nil
}

// buildURL 构建请求URL
func (c *Client) buildURL(endpoint string, params map[string]string) (string, error) {
	u, err := url.Parse(c.baseURL + endpoint)
	if err != nil {
		return "", err
	}

	q := u.Query()

	// 添加API密钥
	q.Set("key", c.config.WebServiceKey)

	// 添加其他参数
	for key, value := range params {
		q.Set(key, value)
	}

	u.RawQuery = q.Encode()
	return u.String(), nil
}

// TestConnection 测试API连接
func (c *Client) TestConnection(ctx context.Context) error {
	logger.Info("Testing AMap API connection")

	// 使用IP定位接口测试连接
	params := map[string]string{
		"ip": "***************", // 使用公共DNS IP测试
	}

	var result struct {
		APIResponse
		Province FlexibleString `json:"province"`
		City     FlexibleString `json:"city"`
	}

	err := c.request(ctx, "/v3/ip", params, &result)
	if err != nil {
		return fmt.Errorf("connection test failed: %w", err)
	}

	logger.Info("AMap API connection test successful",
		zap.String("province", string(result.Province)),
		zap.String("city", string(result.City)))

	return nil
}

// GetAPIStats 获取API统计信息
func (c *Client) GetAPIStats() map[string]interface{} {
	return map[string]interface{}{
		"base_url":            c.baseURL,
		"timeout":             c.httpClient.Timeout.String(),
		"rate_limit_per_sec":  c.limiter.Limit(),
		"rate_limit_burst":    c.limiter.Burst(),
		"max_idle_conns":      100,
		"max_idle_conns_host": 10,
	}
}

// GeocodeRequest 地理编码请求参数
type GeocodeRequest struct {
	Address string // 地址
	City    string // 城市，可选
}

// GeocodeResponse 地理编码响应
type GeocodeResponse struct {
	APIResponse
	Geocodes []Geocode `json:"geocodes"`
}

// GetStatus 实现接口方法
func (r *GeocodeResponse) GetStatus() string {
	return r.Status
}

// Geocode 地理编码结果
type Geocode struct {
	FormattedAddress string         `json:"formatted_address"` // 格式化地址
	Country          FlexibleString `json:"country"`           // 国家
	Province         FlexibleString `json:"province"`          // 省份
	City             FlexibleString `json:"city"`              // 城市
	Citycode         FlexibleString `json:"citycode"`          // 城市编码
	District         FlexibleString `json:"district"`          // 区县
	Township         FlexibleString `json:"township"`          // 乡镇
	Neighborhood     FlexibleString `json:"neighborhood"`      // 社区
	Building         FlexibleString `json:"building"`          // 建筑
	Adcode           FlexibleString `json:"adcode"`            // 区域编码
	Street           FlexibleString `json:"street"`            // 街道
	Number           FlexibleString `json:"number"`            // 门牌号
	Location         string         `json:"location"`          // 坐标 "经度,纬度"
	Level            FlexibleString `json:"level"`             // 匹配级别
}

// GetLocation 获取坐标
func (g *Geocode) GetLocation() (lat, lng float64, err error) {
	if g.Location == "" {
		return 0, 0, fmt.Errorf("location is empty")
	}

	coords := strings.Split(g.Location, ",")
	if len(coords) != 2 {
		return 0, 0, fmt.Errorf("invalid location format: %s", g.Location)
	}

	lng, err = strconv.ParseFloat(coords[0], 64)
	if err != nil {
		return 0, 0, fmt.Errorf("invalid longitude: %s", coords[0])
	}

	lat, err = strconv.ParseFloat(coords[1], 64)
	if err != nil {
		return 0, 0, fmt.Errorf("invalid latitude: %s", coords[1])
	}

	return lat, lng, nil
}

// Geocode 地理编码 - 将地址转换为坐标
func (c *Client) Geocode(ctx context.Context, req *GeocodeRequest) (*GeocodeResponse, error) {
	params := map[string]string{
		"address": req.Address,
		"output":  "json",
	}

	if req.City != "" {
		params["city"] = req.City
	}

	var result GeocodeResponse
	err := c.request(ctx, "/v3/geocode/geo", params, &result)
	if err != nil {
		return nil, fmt.Errorf("geocode request failed: %w", err)
	}

	logger.Debug("Geocode completed",
		zap.String("address", req.Address),
		zap.Int("results", len(result.Geocodes)))

	return &result, nil
}

// ReverseGeocodeRequest 逆地理编码请求参数
type ReverseGeocodeRequest struct {
	Location   string // 坐标 "经度,纬度"
	Radius     int    // 搜索半径，默认1000米
	Extensions string // 返回结果控制，base/all
}

// ReverseGeocodeResponse 逆地理编码响应
type ReverseGeocodeResponse struct {
	APIResponse
	Regeocode Regeocode `json:"regeocode"`
}

// GetStatus 实现接口方法
func (r *ReverseGeocodeResponse) GetStatus() string {
	return r.Status
}

// Regeocode 逆地理编码结果
type Regeocode struct {
	FormattedAddress FlexibleString `json:"formatted_address"` // 格式化地址
	AddressComponent Component      `json:"addressComponent"`  // 地址组件
}

// Component 地址组件
type Component struct {
	Country      FlexibleString `json:"country"`      // 国家
	Province     FlexibleString `json:"province"`     // 省份
	City         FlexibleString `json:"city"`         // 城市
	Citycode     FlexibleString `json:"citycode"`     // 城市编码
	District     FlexibleString `json:"district"`     // 区县
	Adcode       FlexibleString `json:"adcode"`       // 区域编码
	Township     FlexibleString `json:"township"`     // 乡镇
	Neighborhood FlexibleString `json:"neighborhood"` // 社区
	Building     FlexibleString `json:"building"`     // 建筑
	StreetNumber FlexibleString `json:"streetNumber"` // 街道信息
}

// ReverseGeocode 逆地理编码 - 将坐标转换为地址
func (c *Client) ReverseGeocode(ctx context.Context, req *ReverseGeocodeRequest) (*ReverseGeocodeResponse, error) {
	params := map[string]string{
		"location": req.Location,
		"output":   "json",
	}

	if req.Radius > 0 {
		params["radius"] = strconv.Itoa(req.Radius)
	}

	if req.Extensions != "" {
		params["extensions"] = req.Extensions
	}

	var result ReverseGeocodeResponse
	err := c.request(ctx, "/v3/geocode/regeo", params, &result)
	if err != nil {
		return nil, fmt.Errorf("reverse geocode request failed: %w", err)
	}

	logger.Debug("Reverse geocode completed",
		zap.String("location", req.Location),
		zap.String("address", string(result.Regeocode.FormattedAddress)))

	return &result, nil
}

// DirectionRequest 路线规划请求参数
type DirectionRequest struct {
	Origin        string // 起点坐标 "经度,纬度"
	Destination   string // 终点坐标 "经度,纬度"
	Waypoints     string // 途经点，可选
	Strategy      int    // 路径规划策略
	Extensions    string // 返回结果详细程度，base/all
	AvoidPolygons string // 避让区域，可选
	AvoidRoad     string // 避让道路，可选
}

// DirectionResponse 路线规划响应
type DirectionResponse struct {
	APIResponse
	Route Route `json:"route"`
}

// GetStatus 实现接口方法
func (r *DirectionResponse) GetStatus() string {
	return r.Status
}

// Route 路线信息
type Route struct {
	Origin      string `json:"origin"`      // 起点坐标
	Destination string `json:"destination"` // 终点坐标
	Paths       []Path `json:"paths"`       // 路径列表
}

// Path 路径信息
type Path struct {
	Distance     string `json:"distance"`      // 距离，单位：米
	Duration     string `json:"duration"`      // 时间，单位：秒
	Strategy     string `json:"strategy"`      // 导航策略
	Tolls        string `json:"tolls"`         // 收费，单位：元
	TollDistance string `json:"toll_distance"` // 收费路段距离
	Steps        []Step `json:"steps"`         // 路段列表
}

// GetDistanceInt 获取距离（整数）
func (p *Path) GetDistanceInt() (int, error) {
	return strconv.Atoi(p.Distance)
}

// GetDurationInt 获取时间（整数）
func (p *Path) GetDurationInt() (int, error) {
	return strconv.Atoi(p.Duration)
}

// Step 路段信息
type Step struct {
	Instruction     string              `json:"instruction"`      // 行驶指示
	Orientation     string              `json:"orientation"`      // 方向
	Road            string              `json:"road"`             // 道路名称
	Distance        string              `json:"distance"`         // 距离
	Duration        string              `json:"duration"`         // 时间
	Polyline        string              `json:"polyline"`         // 路径坐标点串
	Action          FlexibleString      `json:"action"`           // 导航主要动作
	AssistantAction FlexibleStringArray `json:"assistant_action"` // 导航辅助动作
}

// Direction 路线规划 - 驾车路径规划
func (c *Client) Direction(ctx context.Context, req *DirectionRequest) (*DirectionResponse, error) {
	params := map[string]string{
		"origin":      req.Origin,
		"destination": req.Destination,
		"output":      "json",
	}

	if req.Waypoints != "" {
		params["waypoints"] = req.Waypoints
	}

	if req.Strategy > 0 {
		params["strategy"] = strconv.Itoa(req.Strategy)
	}

	if req.Extensions != "" {
		params["extensions"] = req.Extensions
	}

	if req.AvoidPolygons != "" {
		params["avoidpolygons"] = req.AvoidPolygons
	}

	if req.AvoidRoad != "" {
		params["avoidroad"] = req.AvoidRoad
	}

	var result DirectionResponse
	err := c.request(ctx, "/v3/direction/driving", params, &result)
	if err != nil {
		return nil, fmt.Errorf("direction request failed: %w", err)
	}

	logger.Debug("Direction planning completed",
		zap.String("origin", req.Origin),
		zap.String("destination", req.Destination),
		zap.Int("paths", len(result.Route.Paths)))

	return &result, nil
}

// DistanceRequest 距离测量请求参数
type DistanceRequest struct {
	Origins     string // 起点坐标集，支持多个起点
	Destination string // 终点坐标
	Type        int    // 路径计算的方式和方法
}

// DistanceResponse 距离测量响应
type DistanceResponse struct {
	APIResponse
	Results []DistanceResult `json:"results"`
}

// GetStatus 实现接口方法
func (r *DistanceResponse) GetStatus() string {
	return r.Status
}

// DistanceResult 距离测量结果
type DistanceResult struct {
	OriginID string `json:"origin_id"` // 起点ID
	DestID   string `json:"dest_id"`   // 终点ID
	Distance string `json:"distance"`  // 距离，单位：米
	Duration string `json:"duration"`  // 时间，单位：秒
}

// Distance 距离测量
func (c *Client) Distance(ctx context.Context, req *DistanceRequest) (*DistanceResponse, error) {
	params := map[string]string{
		"origins":     req.Origins,
		"destination": req.Destination,
		"output":      "json",
	}

	if req.Type > 0 {
		params["type"] = strconv.Itoa(req.Type)
	}

	var result DistanceResponse
	err := c.request(ctx, "/v3/distance", params, &result)
	if err != nil {
		return nil, fmt.Errorf("distance request failed: %w", err)
	}

	logger.Debug("Distance measurement completed",
		zap.String("origins", req.Origins),
		zap.String("destination", req.Destination),
		zap.Int("results", len(result.Results)))

	return &result, nil
}

// 路径规划策略常量
const (
	StrategyFastest          = 0 // 速度优先（时间）
	StrategyNoHighway        = 1 // 费用优先（不走收费路段的最快道路）
	StrategyDistance         = 2 // 距离优先
	StrategyNoHighwayFast    = 3 // 速度优先（不走快速路）
	StrategyAvoidJam         = 4 // 躲避拥堵
	StrategyNoHighwayJam     = 5 // 多策略（同时使用速度优先、费用优先、距离优先三个策略）
	StrategyNoHighwayDist    = 6 // 速度优先（不走高速且避免收费）
	StrategyDistanceJam      = 7 // 距离优先且躲避拥堵
	StrategyNoHighwayDistJam = 8 // 费用优先且躲避拥堵
	StrategyFastestJam       = 9 // 速度优先且躲避拥堵
)

// FormatLocation 格式化坐标
func FormatLocation(lat, lng float64) string {
	return fmt.Sprintf("%.6f,%.6f", lng, lat)
}

// ParseLocation 解析坐标
func ParseLocation(location string) (lat, lng float64, err error) {
	if location == "" {
		return 0, 0, fmt.Errorf("location is empty")
	}

	coords := strings.Split(location, ",")
	if len(coords) != 2 {
		return 0, 0, fmt.Errorf("invalid location format: %s", location)
	}

	lng, err = strconv.ParseFloat(coords[0], 64)
	if err != nil {
		return 0, 0, fmt.Errorf("invalid longitude: %s", coords[0])
	}

	lat, err = strconv.ParseFloat(coords[1], 64)
	if err != nil {
		return 0, 0, fmt.Errorf("invalid latitude: %s", coords[1])
	}

	return lat, lng, nil
}
