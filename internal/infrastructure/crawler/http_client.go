package crawler

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/azel-ko/final-ddd/internal/pkg/config"
	"github.com/azel-ko/final-ddd/internal/pkg/logger"
	"go.uber.org/zap"
)

// HTTPClient HTTP客户端封装
type HTTPClient struct {
	client    *http.Client
	config    *config.CheckpointConfig
	userAgent string
}

// NewHTTPClient 创建新的HTTP客户端
func NewHTTPClient(cfg *config.CheckpointConfig) *HTTPClient {
	timeout, err := time.ParseDuration(cfg.Timeout)
	if err != nil {
		logger.Warn("Invalid timeout duration, using default 30s", 
			zap.String("timeout", cfg.Timeout))
		timeout = 30 * time.Second
	}

	client := &http.Client{
		Timeout: timeout,
		Transport: &http.Transport{
			MaxIdleConns:        100,
			MaxIdleConnsPerHost: 10,
			IdleConnTimeout:     90 * time.Second,
		},
	}

	return &HTTPClient{
		client:    client,
		config:    cfg,
		userAgent: cfg.UserAgent,
	}
}

// Get 发送GET请求
func (c *HTTPClient) Get(ctx context.Context, url string) ([]byte, error) {
	return c.request(ctx, "GET", url, nil)
}

// Post 发送POST请求
func (c *HTTPClient) Post(ctx context.Context, url string, body io.Reader) ([]byte, error) {
	return c.request(ctx, "POST", url, body)
}

// request 发送HTTP请求
func (c *HTTPClient) request(ctx context.Context, method, url string, body io.Reader) ([]byte, error) {
	req, err := http.NewRequestWithContext(ctx, method, url, body)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	req.Header.Set("User-Agent", c.userAgent)
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
	req.Header.Set("Accept-Encoding", "gzip, deflate")
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("Upgrade-Insecure-Requests", "1")

	// 发送请求
	resp, err := c.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	// 读取响应体
	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	logger.Debug("HTTP request completed", 
		zap.String("method", method),
		zap.String("url", url),
		zap.Int("status", resp.StatusCode),
		zap.Int("size", len(data)))

	return data, nil
}

// GetWithRetry 带重试的GET请求
func (c *HTTPClient) GetWithRetry(ctx context.Context, url string) ([]byte, error) {
	var lastErr error
	
	retryDelay, err := time.ParseDuration(c.config.RetryDelay)
	if err != nil {
		retryDelay = 5 * time.Second
	}

	for i := 0; i <= c.config.RetryCount; i++ {
		if i > 0 {
			logger.Info("Retrying request", 
				zap.String("url", url),
				zap.Int("attempt", i),
				zap.Int("max_retries", c.config.RetryCount))
			
			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			case <-time.After(retryDelay):
				// 继续重试
			}
		}

		data, err := c.Get(ctx, url)
		if err == nil {
			return data, nil
		}

		lastErr = err
		logger.Warn("Request failed", 
			zap.String("url", url),
			zap.Int("attempt", i+1),
			zap.Error(err))
	}

	return nil, fmt.Errorf("request failed after %d retries: %w", c.config.RetryCount, lastErr)
}