package crawler

import (
	"fmt"
	"regexp"
	"strings"

	"github.com/azel-ko/final-ddd/internal/domain/entities"
	"github.com/azel-ko/final-ddd/internal/pkg/logger"
	"go.uber.org/zap"
)

// CheckpointValidator 检查站数据验证器
type CheckpointValidator struct {
	nameRegex     *regexp.Regexp
	locationRegex *regexp.Regexp
	minNameLength int
	maxNameLength int
}

// NewCheckpointValidator 创建检查站验证器
func NewCheckpointValidator() *CheckpointValidator {
	return &CheckpointValidator{
		nameRegex:     regexp.MustCompile(`^[\p{L}\p{N}\s\-\(\)（）]+$`),
		locationRegex: regexp.MustCompile(`^[\p{L}\p{N}\s\-\(\)（）,，。.]+$`),
		minNameLength: 3,
		maxNameLength: 100,
	}
}

// ValidateCheckpoint 验证检查站数据
func (v *CheckpointValidator) ValidateCheckpoint(checkpoint *entities.Checkpoint) error {
	if err := v.validateName(checkpoint.Name); err != nil {
		return fmt.Errorf("name validation failed: %w", err)
	}

	if err := v.validateLocation(checkpoint.Location); err != nil {
		return fmt.Errorf("location validation failed: %w", err)
	}

	if err := v.validateCoordinates(checkpoint.Latitude, checkpoint.Longitude); err != nil {
		return fmt.Errorf("coordinates validation failed: %w", err)
	}

	if err := v.validateStatus(checkpoint.Status); err != nil {
		return fmt.Errorf("status validation failed: %w", err)
	}

	if err := v.validateReliability(checkpoint.Reliability); err != nil {
		return fmt.Errorf("reliability validation failed: %w", err)
	}

	return nil
}

// validateName 验证检查站名称
func (v *CheckpointValidator) validateName(name string) error {
	if name == "" {
		return fmt.Errorf("name cannot be empty")
	}

	if len(name) < v.minNameLength {
		return fmt.Errorf("name too short: minimum %d characters", v.minNameLength)
	}

	if len(name) > v.maxNameLength {
		return fmt.Errorf("name too long: maximum %d characters", v.maxNameLength)
	}

	if !v.nameRegex.MatchString(name) {
		return fmt.Errorf("name contains invalid characters")
	}

	return nil
}

// validateLocation 验证位置信息
func (v *CheckpointValidator) validateLocation(location string) error {
	if location == "" {
		return fmt.Errorf("location cannot be empty")
	}

	if len(location) > 500 {
		return fmt.Errorf("location too long: maximum 500 characters")
	}

	if !v.locationRegex.MatchString(location) {
		return fmt.Errorf("location contains invalid characters")
	}

	return nil
}

// validateCoordinates 验证坐标
func (v *CheckpointValidator) validateCoordinates(lat, lng float64) error {
	// 中国大陆坐标范围验证
	if lat < 18.0 || lat > 54.0 {
		return fmt.Errorf("latitude out of range: %f (expected 18.0-54.0)", lat)
	}

	if lng < 73.0 || lng > 135.0 {
		return fmt.Errorf("longitude out of range: %f (expected 73.0-135.0)", lng)
	}

	// 北京地区坐标范围验证（更严格）
	if lat >= 39.4 && lat <= 41.1 && lng >= 115.4 && lng <= 117.5 {
		// 在北京范围内，坐标有效
		return nil
	}

	// 如果不在北京范围内，记录警告但不阻止
	logger.Warn("Coordinates outside Beijing area", 
		zap.Float64("latitude", lat),
		zap.Float64("longitude", lng))

	return nil
}

// validateStatus 验证状态
func (v *CheckpointValidator) validateStatus(status string) error {
	validStatuses := []string{"active", "inactive", "unknown"}
	
	for _, validStatus := range validStatuses {
		if status == validStatus {
			return nil
		}
	}

	return fmt.Errorf("invalid status: %s (expected: %v)", status, validStatuses)
}

// validateReliability 验证可靠性评分
func (v *CheckpointValidator) validateReliability(reliability int) error {
	if reliability < 0 || reliability > 100 {
		return fmt.Errorf("reliability out of range: %d (expected 0-100)", reliability)
	}

	return nil
}

// CleanCheckpoint 清洗检查站数据
func (v *CheckpointValidator) CleanCheckpoint(checkpoint *entities.Checkpoint) *entities.Checkpoint {
	cleaned := *checkpoint

	// 清洗名称
	cleaned.Name = v.cleanText(cleaned.Name)
	
	// 清洗位置信息
	cleaned.Location = v.cleanText(cleaned.Location)
	
	// 清洗省市区信息
	cleaned.Province = v.cleanText(cleaned.Province)
	cleaned.City = v.cleanText(cleaned.City)
	cleaned.District = v.cleanText(cleaned.District)
	cleaned.Road = v.cleanText(cleaned.Road)
	cleaned.Direction = v.cleanText(cleaned.Direction)

	// 标准化状态
	cleaned.Status = v.normalizeStatus(cleaned.Status)

	// 标准化类型
	cleaned.Type = v.normalizeType(cleaned.Type)

	// 确保可靠性评分在有效范围内
	if cleaned.Reliability < 0 {
		cleaned.Reliability = 0
	} else if cleaned.Reliability > 100 {
		cleaned.Reliability = 100
	}

	return &cleaned
}

// cleanText 清洗文本内容
func (v *CheckpointValidator) cleanText(text string) string {
	if text == "" {
		return text
	}

	// 移除前后空白
	text = strings.TrimSpace(text)

	// 移除多余的空白字符
	text = regexp.MustCompile(`\s+`).ReplaceAllString(text, " ")

	// 移除特殊字符（保留中文、英文、数字、常用标点）
	text = regexp.MustCompile(`[^\p{L}\p{N}\s\-\(\)（）,，。.]`).ReplaceAllString(text, "")

	// 再次移除前后空白
	text = strings.TrimSpace(text)

	return text
}

// normalizeStatus 标准化状态
func (v *CheckpointValidator) normalizeStatus(status string) string {
	status = strings.ToLower(strings.TrimSpace(status))

	switch status {
	case "active", "正常", "活跃", "开放", "运行":
		return "active"
	case "inactive", "停用", "关闭", "暂停":
		return "inactive"
	default:
		return "unknown"
	}
}

// normalizeType 标准化类型
func (v *CheckpointValidator) normalizeType(checkpointType string) string {
	checkpointType = strings.ToLower(strings.TrimSpace(checkpointType))

	switch checkpointType {
	case "checkpoint", "检查站", "检查点":
		return "checkpoint"
	case "inspection", "检验站":
		return "inspection"
	case "toll", "收费站":
		return "toll"
	case "service", "服务区":
		return "service"
	default:
		return "checkpoint"
	}
}

// ValidateAndCleanCheckpoints 批量验证和清洗检查站数据
func (v *CheckpointValidator) ValidateAndCleanCheckpoints(checkpoints []*entities.Checkpoint) ([]*entities.Checkpoint, []error) {
	var validCheckpoints []*entities.Checkpoint
	var errors []error

	for i, checkpoint := range checkpoints {
		// 清洗数据
		cleaned := v.CleanCheckpoint(checkpoint)

		// 验证数据
		if err := v.ValidateCheckpoint(cleaned); err != nil {
			logger.Warn("Checkpoint validation failed", 
				zap.Int("index", i),
				zap.String("name", checkpoint.Name),
				zap.Error(err))
			errors = append(errors, fmt.Errorf("checkpoint %d: %w", i, err))
			continue
		}

		validCheckpoints = append(validCheckpoints, cleaned)
	}

	logger.Info("Checkpoint validation completed", 
		zap.Int("total", len(checkpoints)),
		zap.Int("valid", len(validCheckpoints)),
		zap.Int("invalid", len(errors)))

	return validCheckpoints, errors
}

// DeduplicateCheckpoints 去重检查站数据
func (v *CheckpointValidator) DeduplicateCheckpoints(checkpoints []*entities.Checkpoint) []*entities.Checkpoint {
	seen := make(map[string]bool)
	var unique []*entities.Checkpoint

	for _, checkpoint := range checkpoints {
		// 创建唯一标识符（基于名称和坐标）
		key := fmt.Sprintf("%s_%.6f_%.6f", 
			checkpoint.Name, 
			checkpoint.Latitude, 
			checkpoint.Longitude)

		if !seen[key] {
			seen[key] = true
			unique = append(unique, checkpoint)
		}
	}

	logger.Info("Checkpoint deduplication completed", 
		zap.Int("original", len(checkpoints)),
		zap.Int("unique", len(unique)),
		zap.Int("duplicates", len(checkpoints)-len(unique)))

	return unique
}