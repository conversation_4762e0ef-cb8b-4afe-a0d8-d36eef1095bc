package crawler

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/PuerkitoBio/goquery"
	"github.com/azel-ko/final-ddd/internal/domain/entities"
	"github.com/azel-ko/final-ddd/internal/pkg/logger"
	"go.uber.org/zap"
)

// CheckpointParser 检查站数据解析器
type CheckpointParser struct {
	locationRegex *regexp.Regexp
	coordRegex    *regexp.Regexp
}

// NewCheckpointParser 创建检查站解析器
func NewCheckpointParser() *CheckpointParser {
	return &CheckpointParser{
		locationRegex: regexp.MustCompile(`([^省]+省)?([^市]+市)?([^区县]+[区县])?`),
		coordRegex:    regexp.MustCompile(`(\d+\.\d+),(\d+\.\d+)`),
	}
}

// ParseCheckpoints 解析检查站数据
func (p *CheckpointParser) ParseCheckpoints(htmlContent []byte) ([]*entities.Checkpoint, error) {
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(string(htmlContent)))
	if err != nil {
		return nil, fmt.Errorf("failed to parse HTML: %w", err)
	}

	var checkpoints []*entities.Checkpoint
	now := time.Now()

	// 查找检查站信息的表格或列表
	doc.Find(".checkpoint-item, .check-point, .station-info, tr").Each(func(i int, s *goquery.Selection) {
		checkpoint := p.parseCheckpointItem(s, now)
		if checkpoint != nil {
			checkpoints = append(checkpoints, checkpoint)
		}
	})

	// 如果没有找到标准格式，尝试解析文本内容
	if len(checkpoints) == 0 {
		checkpoints = p.parseTextContent(doc, now)
	}

	logger.Info("Parsed checkpoints from HTML",
		zap.Int("count", len(checkpoints)))

	return checkpoints, nil
}

// parseCheckpointItem 解析单个检查站项目
func (p *CheckpointParser) parseCheckpointItem(s *goquery.Selection, now time.Time) *entities.Checkpoint {
	// 提取检查站名称
	name := p.extractText(s, ".name, .station-name, .checkpoint-name, td:first-child")
	if name == "" {
		return nil
	}

	// 提取位置信息
	location := p.extractText(s, ".location, .address, .position, td:nth-child(2)")

	// 提取状态信息
	status := p.extractStatus(s)

	// 提取坐标信息
	lat, lng := p.extractCoordinates(s)

	// 解析地理位置
	province, city, district := p.parseLocation(location)

	// 提取道路信息
	road := p.extractRoad(name, location)

	checkpoint := &entities.Checkpoint{
		Name:        p.cleanText(name),
		Location:    p.cleanText(location),
		Latitude:    lat,
		Longitude:   lng,
		Province:    province,
		City:        city,
		District:    district,
		Road:        road,
		Status:      status,
		Type:        "checkpoint",
		Source:      "web",
		Reliability: 70, // 默认可靠性评分
		LastSeen:    &now,
		CreatedAt:   now,
		UpdatedAt:   now,
	}

	return checkpoint
}

// parseTextContent 解析纯文本内容
func (p *CheckpointParser) parseTextContent(doc *goquery.Document, now time.Time) []*entities.Checkpoint {
	var checkpoints []*entities.Checkpoint

	text := doc.Text()
	lines := strings.Split(text, "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if len(line) < 5 { // 过滤太短的行
			continue
		}

		// 检查是否包含检查站关键词
		if p.containsCheckpointKeywords(line) {
			checkpoint := p.parseTextLine(line, now)
			if checkpoint != nil {
				checkpoints = append(checkpoints, checkpoint)
			}
		}
	}

	return checkpoints
}

// parseTextLine 解析文本行
func (p *CheckpointParser) parseTextLine(line string, now time.Time) *entities.Checkpoint {
	// 提取坐标
	lat, lng := p.extractCoordinatesFromText(line)

	// 解析地理位置
	province, city, district := p.parseLocation(line)

	// 提取道路信息
	road := p.extractRoad(line, line)

	checkpoint := &entities.Checkpoint{
		Name:        p.generateCheckpointName(line),
		Location:    p.cleanText(line),
		Latitude:    lat,
		Longitude:   lng,
		Province:    province,
		City:        city,
		District:    district,
		Road:        road,
		Status:      "unknown",
		Type:        "checkpoint",
		Source:      "web",
		Reliability: 60, // 文本解析的可靠性较低
		LastSeen:    &now,
		CreatedAt:   now,
		UpdatedAt:   now,
	}

	return checkpoint
}

// extractText 提取文本内容
func (p *CheckpointParser) extractText(s *goquery.Selection, selector string) string {
	if selector != "" {
		found := s.Find(selector)
		if found.Length() > 0 {
			return strings.TrimSpace(found.First().Text())
		}
	}

	// 如果没有找到特定选择器，返回整个元素的文本
	return strings.TrimSpace(s.Text())
}

// extractStatus 提取状态信息
func (p *CheckpointParser) extractStatus(s *goquery.Selection) string {
	statusText := p.extractText(s, ".status, .state, .condition")
	statusText = strings.ToLower(statusText)

	if strings.Contains(statusText, "活跃") || strings.Contains(statusText, "正常") || strings.Contains(statusText, "active") {
		return "active"
	} else if strings.Contains(statusText, "停用") || strings.Contains(statusText, "关闭") || strings.Contains(statusText, "inactive") {
		return "inactive"
	}

	return "unknown"
}

// extractCoordinates 提取坐标信息
func (p *CheckpointParser) extractCoordinates(s *goquery.Selection) (float64, float64) {
	// 尝试从data属性中提取
	if lat, exists := s.Attr("data-lat"); exists {
		if lng, exists := s.Attr("data-lng"); exists {
			latFloat, _ := strconv.ParseFloat(lat, 64)
			lngFloat, _ := strconv.ParseFloat(lng, 64)
			return latFloat, lngFloat
		}
	}

	// 尝试从文本中提取
	text := s.Text()
	return p.extractCoordinatesFromText(text)
}

// extractCoordinatesFromText 从文本中提取坐标
func (p *CheckpointParser) extractCoordinatesFromText(text string) (float64, float64) {
	matches := p.coordRegex.FindStringSubmatch(text)
	if len(matches) >= 3 {
		lat, err1 := strconv.ParseFloat(matches[1], 64)
		lng, err2 := strconv.ParseFloat(matches[2], 64)
		if err1 == nil && err2 == nil {
			return lat, lng
		}
	}

	// 如果没有找到坐标，返回北京市中心的默认坐标
	return 39.9042, 116.4074
}

// parseLocation 解析地理位置
func (p *CheckpointParser) parseLocation(location string) (province, city, district string) {
	// 北京相关的关键词匹配
	if strings.Contains(location, "北京") || strings.Contains(location, "京") {
		province = "北京"
		city = "北京"

		// 提取区县信息
		districts := []string{"朝阳", "海淀", "丰台", "西城", "东城", "石景山", "门头沟", "房山", "通州", "顺义", "昌平", "大兴", "怀柔", "平谷", "密云", "延庆"}
		for _, d := range districts {
			if strings.Contains(location, d) {
				district = d + "区"
				break
			}
		}
	}

	// 使用正则表达式进一步解析
	matches := p.locationRegex.FindStringSubmatch(location)
	if len(matches) >= 2 && matches[1] != "" {
		province = strings.TrimSuffix(matches[1], "省")
	}
	if len(matches) >= 3 && matches[2] != "" {
		city = strings.TrimSuffix(matches[2], "市")
	}
	if len(matches) >= 4 && matches[3] != "" {
		district = matches[3]
	}

	// 默认值
	if province == "" {
		province = "北京"
	}
	if city == "" {
		city = "北京"
	}

	return province, city, district
}

// extractRoad 提取道路信息
func (p *CheckpointParser) extractRoad(name, location string) string {
	text := name + " " + location

	// 高速公路
	highways := []string{"京藏高速", "京沪高速", "京港澳高速", "京承高速", "京开高速", "京津高速", "京哈高速", "京新高速"}
	for _, highway := range highways {
		if strings.Contains(text, highway) {
			return highway
		}
	}

	// 国道
	if strings.Contains(text, "国道") || strings.Contains(text, "G") {
		roads := regexp.MustCompile(`G\d+|国道\d+`).FindAllString(text, -1)
		if len(roads) > 0 {
			return roads[0]
		}
	}

	// 省道
	if strings.Contains(text, "省道") || strings.Contains(text, "S") {
		roads := regexp.MustCompile(`S\d+|省道\d+`).FindAllString(text, -1)
		if len(roads) > 0 {
			return roads[0]
		}
	}

	return ""
}

// containsCheckpointKeywords 检查是否包含检查站关键词
func (p *CheckpointParser) containsCheckpointKeywords(text string) bool {
	keywords := []string{"检查站", "检查点", "卡口", "收费站", "服务区", "checkpoint", "inspection"}
	text = strings.ToLower(text)

	for _, keyword := range keywords {
		if strings.Contains(text, keyword) {
			return true
		}
	}

	return false
}

// generateCheckpointName 生成检查站名称
func (p *CheckpointParser) generateCheckpointName(text string) string {
	// 如果文本中包含明确的检查站名称，直接使用
	if strings.Contains(text, "检查站") {
		parts := strings.Split(text, "检查站")
		if len(parts) > 0 {
			return strings.TrimSpace(parts[0]) + "检查站"
		}
	}

	// 根据位置信息生成名称
	_, city, district := p.parseLocation(text)
	road := p.extractRoad(text, text)

	name := ""
	if road != "" {
		name = road
	} else if district != "" {
		name = district
	} else if city != "" {
		name = city
	}

	if name != "" {
		return name + "检查站"
	}

	return "未知检查站"
}

// cleanText 清理文本内容
func (p *CheckpointParser) cleanText(text string) string {
	// 移除多余的空白字符
	text = regexp.MustCompile(`\s+`).ReplaceAllString(text, " ")
	text = strings.TrimSpace(text)

	// 移除特殊字符
	text = regexp.MustCompile(`[^\p{L}\p{N}\s\-\(\)（）]`).ReplaceAllString(text, "")

	return text
}
