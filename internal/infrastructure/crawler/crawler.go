package crawler

import (
	"context"
	"fmt"
	"time"

	"github.com/azel-ko/final-ddd/internal/domain/entities"
	"github.com/azel-ko/final-ddd/internal/pkg/config"
	"github.com/azel-ko/final-ddd/internal/pkg/logger"
	"go.uber.org/zap"
)

// CheckpointCrawler 检查站数据爬虫
type CheckpointCrawler struct {
	httpClient *HTTPClient
	parser     *CheckpointParser
	validator  *CheckpointValidator
	config     *config.CheckpointConfig
}

// NewCheckpointCrawler 创建检查站爬虫
func NewCheckpointCrawler(cfg *config.CheckpointConfig) *CheckpointCrawler {
	return &CheckpointCrawler{
		httpClient: NewHTTPClient(cfg),
		parser:     NewCheckpointParser(),
		validator:  NewCheckpointValidator(),
		config:     cfg,
	}
}

// CrawlResult 爬取结果
type CrawlResult struct {
	Checkpoints    []*entities.Checkpoint
	TotalFound     int
	ValidCount     int
	InvalidCount   int
	DuplicateCount int
	Errors         []error
	Duration       time.Duration
	Source         string
}

// CrawlCheckpoints 爬取检查站数据
func (c *CheckpointCrawler) CrawlCheckpoints(ctx context.Context) (*CrawlResult, error) {
	startTime := time.Now()
	
	logger.Info("Starting checkpoint crawling", 
		zap.String("url", c.config.URL))

	result := &CrawlResult{
		Source: c.config.URL,
	}

	// 1. 获取HTML内容
	htmlContent, err := c.httpClient.GetWithRetry(ctx, c.config.URL)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch HTML content: %w", err)
	}

	logger.Info("HTML content fetched", 
		zap.Int("size", len(htmlContent)))

	// 2. 解析检查站数据
	checkpoints, err := c.parser.ParseCheckpoints(htmlContent)
	if err != nil {
		return nil, fmt.Errorf("failed to parse checkpoints: %w", err)
	}

	result.TotalFound = len(checkpoints)
	logger.Info("Checkpoints parsed", 
		zap.Int("count", result.TotalFound))

	// 3. 验证和清洗数据
	validCheckpoints, validationErrors := c.validator.ValidateAndCleanCheckpoints(checkpoints)
	result.ValidCount = len(validCheckpoints)
	result.InvalidCount = len(validationErrors)
	result.Errors = validationErrors

	// 4. 去重
	uniqueCheckpoints := c.validator.DeduplicateCheckpoints(validCheckpoints)
	result.DuplicateCount = result.ValidCount - len(uniqueCheckpoints)
	result.Checkpoints = uniqueCheckpoints

	// 5. 计算耗时
	result.Duration = time.Since(startTime)

	logger.Info("Checkpoint crawling completed", 
		zap.Int("total_found", result.TotalFound),
		zap.Int("valid", result.ValidCount),
		zap.Int("invalid", result.InvalidCount),
		zap.Int("duplicates", result.DuplicateCount),
		zap.Int("final_count", len(result.Checkpoints)),
		zap.Duration("duration", result.Duration))

	return result, nil
}

// CrawlSpecificPages 爬取特定页面
func (c *CheckpointCrawler) CrawlSpecificPages(ctx context.Context, urls []string) (*CrawlResult, error) {
	startTime := time.Now()
	
	logger.Info("Starting multi-page checkpoint crawling", 
		zap.Int("page_count", len(urls)))

	result := &CrawlResult{
		Source: "multiple_pages",
	}

	var allCheckpoints []*entities.Checkpoint
	var allErrors []error

	// 爬取每个页面
	for i, url := range urls {
		logger.Info("Crawling page", 
			zap.Int("page", i+1),
			zap.Int("total", len(urls)),
			zap.String("url", url))

		// 获取HTML内容
		htmlContent, err := c.httpClient.GetWithRetry(ctx, url)
		if err != nil {
			logger.Error("Failed to fetch page", 
				zap.String("url", url),
				zap.Error(err))
			allErrors = append(allErrors, fmt.Errorf("page %s: %w", url, err))
			continue
		}

		// 解析检查站数据
		checkpoints, err := c.parser.ParseCheckpoints(htmlContent)
		if err != nil {
			logger.Error("Failed to parse page", 
				zap.String("url", url),
				zap.Error(err))
			allErrors = append(allErrors, fmt.Errorf("parse %s: %w", url, err))
			continue
		}

		allCheckpoints = append(allCheckpoints, checkpoints...)
		
		// 添加延迟以避免过于频繁的请求
		if i < len(urls)-1 {
			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			case <-time.After(2 * time.Second):
				// 继续下一个页面
			}
		}
	}

	result.TotalFound = len(allCheckpoints)
	result.Errors = allErrors

	// 验证和清洗数据
	validCheckpoints, validationErrors := c.validator.ValidateAndCleanCheckpoints(allCheckpoints)
	result.ValidCount = len(validCheckpoints)
	result.InvalidCount = len(validationErrors)
	result.Errors = append(result.Errors, validationErrors...)

	// 去重
	uniqueCheckpoints := c.validator.DeduplicateCheckpoints(validCheckpoints)
	result.DuplicateCount = result.ValidCount - len(uniqueCheckpoints)
	result.Checkpoints = uniqueCheckpoints

	// 计算耗时
	result.Duration = time.Since(startTime)

	logger.Info("Multi-page checkpoint crawling completed", 
		zap.Int("pages", len(urls)),
		zap.Int("total_found", result.TotalFound),
		zap.Int("valid", result.ValidCount),
		zap.Int("invalid", result.InvalidCount),
		zap.Int("duplicates", result.DuplicateCount),
		zap.Int("final_count", len(result.Checkpoints)),
		zap.Duration("duration", result.Duration))

	return result, nil
}

// GetCheckpointURLs 获取检查站数据的URL列表
func (c *CheckpointCrawler) GetCheckpointURLs() []string {
	// 这里可以配置多个数据源URL
	baseURL := c.config.URL
	
	urls := []string{
		baseURL,
		// 可以添加更多的数据源URL
		// baseURL + "/beijing",
		// baseURL + "/checkpoints",
		// baseURL + "/stations",
	}

	return urls
}

// TestConnection 测试连接
func (c *CheckpointCrawler) TestConnection(ctx context.Context) error {
	logger.Info("Testing connection to data source", 
		zap.String("url", c.config.URL))

	_, err := c.httpClient.Get(ctx, c.config.URL)
	if err != nil {
		return fmt.Errorf("connection test failed: %w", err)
	}

	logger.Info("Connection test successful")
	return nil
}

// GetCrawlerStats 获取爬虫统计信息
func (c *CheckpointCrawler) GetCrawlerStats() map[string]interface{} {
	return map[string]interface{}{
		"source_url":    c.config.URL,
		"timeout":       c.config.Timeout,
		"retry_count":   c.config.RetryCount,
		"retry_delay":   c.config.RetryDelay,
		"user_agent":    c.config.UserAgent,
	}
}