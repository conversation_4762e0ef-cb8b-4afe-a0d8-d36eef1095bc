package handlers

import (
	"net/http"

	"github.com/azel-ko/final-ddd/internal/application/dto"
	"github.com/azel-ko/final-ddd/internal/application/services"
	"github.com/gin-gonic/gin"
)

// MonitoringHandler 监控处理器
type MonitoringHandler struct {
	monitoringService *services.MonitoringService
}

// NewMonitoringHandler 创建监控处理器
func NewMonitoringHandler(monitoringService *services.MonitoringService) *MonitoringHandler {
	return &MonitoringHandler{
		monitoringService: monitoringService,
	}
}

// GetSystemStats 获取系统统计信息
func (h *MonitoringHandler) GetSystemStats(c *gin.Context) {
	stats, err := h.monitoringService.GetSystemStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// GetAPIStats 获取API统计信息
func (h *MonitoringHandler) GetAPIStats(c *gin.Context) {
	var req dto.APIStatsQueryRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	stats, err := h.monitoringService.GetAPIStats(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":  stats,
		"total": len(stats),
	})
}

// GetLogs 获取系统日志
func (h *MonitoringHandler) GetLogs(c *gin.Context) {
	var req dto.LogQueryRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	logs, total, err := h.monitoringService.GetLogs(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":      logs,
		"total":     total,
		"page":      req.Page,
		"page_size": req.PageSize,
	})
}

// GetErrors 获取错误信息
func (h *MonitoringHandler) GetErrors(c *gin.Context) {
	var req dto.ErrorQueryRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	errors, total, err := h.monitoringService.GetErrors(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":      errors,
		"total":     total,
		"page":      req.Page,
		"page_size": req.PageSize,
	})
}

// GetHealthCheck 获取健康检查信息
func (h *MonitoringHandler) GetHealthCheck(c *gin.Context) {
	health, err := h.monitoringService.GetHealthCheck()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 根据健康状态返回相应的HTTP状态码
	statusCode := http.StatusOK
	if health.Status != "healthy" {
		statusCode = http.StatusServiceUnavailable
	}

	c.JSON(statusCode, health)
}

// ExportLogs 导出日志
func (h *MonitoringHandler) ExportLogs(c *gin.Context) {
	var req dto.LogQueryRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 设置大的页面大小以获取所有日志
	req.PageSize = 10000
	req.Page = 1

	logs, _, err := h.monitoringService.GetLogs(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 设置响应头为CSV下载
	c.Header("Content-Type", "text/csv")
	c.Header("Content-Disposition", "attachment; filename=system_logs.csv")

	// 写入CSV头部
	c.String(http.StatusOK, "Timestamp,Level,Source,Message\n")

	// 写入日志数据
	for _, log := range logs {
		c.String(http.StatusOK, "%s,%s,%s,\"%s\"\n",
			log.Timestamp.Format("2006-01-02 15:04:05"),
			log.Level,
			log.Source,
			log.Message,
		)
	}
}

// ExportErrors 导出错误报告
func (h *MonitoringHandler) ExportErrors(c *gin.Context) {
	var req dto.ErrorQueryRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 设置大的页面大小以获取所有错误
	req.PageSize = 10000
	req.Page = 1

	errors, _, err := h.monitoringService.GetErrors(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 设置响应头为CSV下载
	c.Header("Content-Type", "text/csv")
	c.Header("Content-Disposition", "attachment; filename=error_report.csv")

	// 写入CSV头部
	c.String(http.StatusOK, "Timestamp,ErrorType,Message,Count,UserID,RequestID\n")

	// 写入错误数据
	for _, err := range errors {
		c.String(http.StatusOK, "%s,%s,\"%s\",%d,%s,%s\n",
			err.Timestamp.Format("2006-01-02 15:04:05"),
			err.ErrorType,
			err.Message,
			err.Count,
			err.UserID,
			err.RequestID,
		)
	}
}

// ExportAPIReport 导出API统计报告
func (h *MonitoringHandler) ExportAPIReport(c *gin.Context) {
	var req dto.APIStatsQueryRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	stats, err := h.monitoringService.GetAPIStats(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 设置响应头为CSV下载
	c.Header("Content-Type", "text/csv")
	c.Header("Content-Disposition", "attachment; filename=api_stats_report.csv")

	// 写入CSV头部
	c.String(http.StatusOK, "Endpoint,Method,TotalCalls,SuccessRate,AvgResponseTime,ErrorCount,LastCalled\n")

	// 写入API统计数据
	for _, stat := range stats {
		c.String(http.StatusOK, "%s,%s,%d,%.2f,%.2f,%d,%s\n",
			stat.Endpoint,
			stat.Method,
			stat.TotalCalls,
			stat.SuccessRate,
			stat.AvgResponseTime,
			stat.ErrorCount,
			stat.LastCalled.Format("2006-01-02 15:04:05"),
		)
	}
}

// GetMonitoringConfig 获取监控配置
func (h *MonitoringHandler) GetMonitoringConfig(c *gin.Context) {
	config, err := h.monitoringService.GetMonitoringConfig()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, config)
}

// UpdateMonitoringConfig 更新监控配置
func (h *MonitoringHandler) UpdateMonitoringConfig(c *gin.Context) {
	var req dto.MonitoringConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err := h.monitoringService.UpdateMonitoringConfig(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "监控配置更新成功"})
}

// GetAlerts 获取系统告警
func (h *MonitoringHandler) GetAlerts(c *gin.Context) {
	alerts, err := h.monitoringService.GetAlerts()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":  alerts,
		"total": len(alerts),
	})
}

// ResolveAlert 解决告警
func (h *MonitoringHandler) ResolveAlert(c *gin.Context) {
	alertID := c.Param("id")
	if alertID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "告警ID不能为空"})
		return
	}

	// 获取当前用户ID（从JWT中获取）
	userID := "admin" // 简化处理，实际应该从JWT中获取

	err := h.monitoringService.ResolveAlert(alertID, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "告警已解决"})
}
