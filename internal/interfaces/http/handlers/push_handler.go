package handlers

import (
	"net/http"
	"strconv"

	"github.com/azel-ko/final-ddd/internal/application/services"
	"github.com/azel-ko/final-ddd/internal/domain/entities"
	"github.com/azel-ko/final-ddd/internal/pkg/logger"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// PushHandler 推送处理器
type PushHandler struct {
	pushService *services.PushService
}

// NewPushHandler 创建推送处理器
func NewPushHandler(pushService *services.PushService) *PushHandler {
	return &PushHandler{
		pushService: pushService,
	}
}

// RegisterDeviceRequest 注册设备请求
type RegisterDeviceRequest struct {
	DeviceType string   `json:"device_type" binding:"required"` // ios, android, miniprogram
	Token      string   `json:"token" binding:"required"`
	Tags       []string `json:"tags,omitempty"`
}

// UpdateSubscriptionRequest 更新订阅请求
type UpdateSubscriptionRequest struct {
	CheckpointUpdates bool   `json:"checkpoint_updates"`
	RouteChanges      bool   `json:"route_changes"`
	SystemAlerts      bool   `json:"system_alerts"`
	QuietHours        string `json:"quiet_hours,omitempty"`
}

// SendPushRequest 发送推送请求
type SendPushRequest struct {
	Title    string                 `json:"title" binding:"required"`
	Body     string                 `json:"body" binding:"required"`
	Data     map[string]interface{} `json:"data,omitempty"`
	UserIDs  []uint                 `json:"user_ids" binding:"required"`
	Priority string                 `json:"priority,omitempty"`
}

// SendTemplateRequest 发送模板消息请求
type SendTemplateRequest struct {
	TemplateName string                 `json:"template_name" binding:"required"`
	Data         map[string]interface{} `json:"data,omitempty"`
	UserIDs      []uint                 `json:"user_ids" binding:"required"`
}

// RegisterDevice 注册设备token
func (h *PushHandler) RegisterDevice(c *gin.Context) {
	var req RegisterDeviceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 从JWT中获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	uid, ok := userID.(uint)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "用户ID格式错误"})
		return
	}

	// 验证设备类型
	if req.DeviceType != "ios" && req.DeviceType != "android" && req.DeviceType != "miniprogram" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "不支持的设备类型"})
		return
	}

	// 验证token有效性
	if err := h.pushService.ValidateDeviceToken(c.Request.Context(), req.DeviceType, req.Token); err != nil {
		logger.Warn("Invalid device token", zap.String("device_type", req.DeviceType), zap.Error(err))
		// 不阻止注册，只记录警告
	}

	// 注册设备
	if err := h.pushService.RegisterDevice(c.Request.Context(), uid, req.DeviceType, req.Token, req.Tags); err != nil {
		logger.Error("Failed to register device", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "注册设备失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "设备注册成功"})
}

// UnregisterDevice 注销设备token
func (h *PushHandler) UnregisterDevice(c *gin.Context) {
	token := c.Param("token")
	if token == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "设备token不能为空"})
		return
	}

	if err := h.pushService.UnregisterDevice(c.Request.Context(), token); err != nil {
		logger.Error("Failed to unregister device", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "注销设备失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "设备注销成功"})
}

// GetSubscription 获取用户推送订阅设置
func (h *PushHandler) GetSubscription(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	uid, ok := userID.(uint)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "用户ID格式错误"})
		return
	}

	subscription, err := h.pushService.GetPushSubscription(c.Request.Context(), uid)
	if err != nil {
		logger.Error("Failed to get push subscription", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取订阅设置失败"})
		return
	}

	c.JSON(http.StatusOK, subscription)
}

// UpdateSubscription 更新用户推送订阅设置
func (h *PushHandler) UpdateSubscription(c *gin.Context) {
	var req UpdateSubscriptionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	uid, ok := userID.(uint)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "用户ID格式错误"})
		return
	}

	// 获取现有订阅设置
	subscription, err := h.pushService.GetPushSubscription(c.Request.Context(), uid)
	if err != nil {
		logger.Error("Failed to get push subscription", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取订阅设置失败"})
		return
	}

	// 更新订阅设置
	subscription.CheckpointUpdates = req.CheckpointUpdates
	subscription.RouteChanges = req.RouteChanges
	subscription.SystemAlerts = req.SystemAlerts
	if req.QuietHours != "" {
		subscription.QuietHours = req.QuietHours
	}

	if err := h.pushService.UpdatePushSubscription(c.Request.Context(), subscription); err != nil {
		logger.Error("Failed to update push subscription", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新订阅设置失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "订阅设置更新成功"})
}

// GetStatistics 获取推送统计
func (h *PushHandler) GetStatistics(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	uid, ok := userID.(uint)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "用户ID格式错误"})
		return
	}

	// 获取天数参数，默认7天
	daysStr := c.DefaultQuery("days", "7")
	days, err := strconv.Atoi(daysStr)
	if err != nil || days <= 0 {
		days = 7
	}

	stats, err := h.pushService.GetPushStatistics(c.Request.Context(), uid, days)
	if err != nil {
		logger.Error("Failed to get push statistics", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取推送统计失败"})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// GetLogs 获取推送日志
func (h *PushHandler) GetLogs(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	uid, ok := userID.(uint)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "用户ID格式错误"})
		return
	}

	// 获取限制参数，默认50条
	limitStr := c.DefaultQuery("limit", "50")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 50
	}

	logs, err := h.pushService.GetPushLogs(c.Request.Context(), uid, limit)
	if err != nil {
		logger.Error("Failed to get push logs", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取推送日志失败"})
		return
	}

	c.JSON(http.StatusOK, logs)
}

// SendPush 发送推送消息（管理员功能）
func (h *PushHandler) SendPush(c *gin.Context) {
	var req SendPushRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 检查管理员权限
	role, exists := c.Get("role")
	if !exists || role != "admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "权限不足"})
		return
	}

	message := &services.PushMessage{
		Title:    req.Title,
		Body:     req.Body,
		Data:     req.Data,
		Priority: req.Priority,
		Sound:    "default",
	}

	result, err := h.pushService.SendBatchNotification(c.Request.Context(), message, req.UserIDs)
	if err != nil {
		logger.Error("Failed to send push notification", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "发送推送失败"})
		return
	}

	c.JSON(http.StatusOK, result)
}

// SendTemplate 发送模板消息（管理员功能）
func (h *PushHandler) SendTemplate(c *gin.Context) {
	var req SendTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 检查管理员权限
	role, exists := c.Get("role")
	if !exists || role != "admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "权限不足"})
		return
	}

	if err := h.pushService.SendTemplateMessage(c.Request.Context(), req.TemplateName, req.Data, req.UserIDs); err != nil {
		logger.Error("Failed to send template message", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "发送模板消息失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "模板消息发送成功"})
}

// GetTemplates 获取推送模板列表（管理员功能）
func (h *PushHandler) GetTemplates(c *gin.Context) {
	// 检查管理员权限
	role, exists := c.Get("role")
	if !exists || role != "admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "权限不足"})
		return
	}

	templates, err := h.pushService.GetPushTemplates(c.Request.Context())
	if err != nil {
		logger.Error("Failed to get push templates", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取推送模板失败"})
		return
	}

	c.JSON(http.StatusOK, templates)
}

// CreateTemplate 创建推送模板（管理员功能）
func (h *PushHandler) CreateTemplate(c *gin.Context) {
	var template entities.PushTemplate
	if err := c.ShouldBindJSON(&template); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 检查管理员权限
	role, exists := c.Get("role")
	if !exists || role != "admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "权限不足"})
		return
	}

	if err := h.pushService.CreatePushTemplate(c.Request.Context(), &template); err != nil {
		logger.Error("Failed to create push template", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建推送模板失败"})
		return
	}

	c.JSON(http.StatusCreated, template)
}

// UpdateTemplate 更新推送模板（管理员功能）
func (h *PushHandler) UpdateTemplate(c *gin.Context) {
	var template entities.PushTemplate
	if err := c.ShouldBindJSON(&template); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 检查管理员权限
	role, exists := c.Get("role")
	if !exists || role != "admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "权限不足"})
		return
	}

	if err := h.pushService.UpdatePushTemplate(c.Request.Context(), &template); err != nil {
		logger.Error("Failed to update push template", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新推送模板失败"})
		return
	}

	c.JSON(http.StatusOK, template)
}
