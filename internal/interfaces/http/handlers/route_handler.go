package handlers

import (
	"net/http"
	"strconv"

	"github.com/azel-ko/final-ddd/internal/application/dto"
	"github.com/azel-ko/final-ddd/internal/application/services"
	"github.com/azel-ko/final-ddd/internal/pkg/logger"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type RouteHandler struct {
	routeService *services.RouteService
}

func NewRouteHandler(routeService *services.RouteService) *RouteHandler {
	return &RouteHandler{
		routeService: routeService,
	}
}

// PlanRoute handles POST /api/routes/plan
func (h *RouteHandler) PlanRoute(c *gin.Context) {
	userIDAuth, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "user not authenticated"})
		return
	}

	userID, ok := userIDAuth.(uint)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "invalid user ID type in context"})
		return
	}

	var req dto.RouteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Invalid route planning request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Set default values
	if req.AvoidLevel == 0 {
		req.AvoidLevel = 1
	}
	if req.Strategy == "" {
		req.Strategy = "fastest"
	}

	response, err := h.routeService.PlanRoute(c.Request.Context(), userID, &req)
	if err != nil {
		logger.Error("Failed to plan route", 
			zap.Uint("user_id", userID),
			zap.Error(err))

		// Handle specific error types
		switch err {
		case services.ErrNavigationPermissionDenied:
			c.JSON(http.StatusForbidden, gin.H{
				"error": "Navigation permission denied. Please upgrade to premium or check your trial status.",
				"code":  "NAVIGATION_PERMISSION_DENIED",
			})
		default:
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to plan route"})
		}
		return
	}

	logger.Info("Route planned successfully", 
		zap.Uint("user_id", userID),
		zap.Int("distance", response.Distance),
		zap.Int("duration", response.Duration))

	c.JSON(http.StatusOK, response)
}

// OptimizeRoute handles POST /api/routes/optimize (Premium feature)
func (h *RouteHandler) OptimizeRoute(c *gin.Context) {
	userIDAuth, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "user not authenticated"})
		return
	}

	userID, ok := userIDAuth.(uint)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "invalid user ID type in context"})
		return
	}

	var req dto.RouteOptimizeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Invalid route optimization request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	response, err := h.routeService.OptimizeRoute(c.Request.Context(), userID, &req)
	if err != nil {
		logger.Error("Failed to optimize route", 
			zap.Uint("user_id", userID),
			zap.Uint("route_id", req.RouteID),
			zap.Error(err))

		// Handle specific error types
		switch err {
		case services.ErrPremiumFeatureRequired:
			c.JSON(http.StatusPaymentRequired, gin.H{
				"error": "Premium subscription required for route optimization",
				"code":  "PREMIUM_REQUIRED",
			})
		case services.ErrRouteNotFound:
			c.JSON(http.StatusNotFound, gin.H{"error": "Route not found"})
		default:
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to optimize route"})
		}
		return
	}

	logger.Info("Route optimized successfully", 
		zap.Uint("user_id", userID),
		zap.Uint("route_id", req.RouteID))

	c.JSON(http.StatusOK, response)
}

// GetRouteHistory handles GET /api/routes/history
func (h *RouteHandler) GetRouteHistory(c *gin.Context) {
	userIDAuth, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "user not authenticated"})
		return
	}

	userID, ok := userIDAuth.(uint)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "invalid user ID type in context"})
		return
	}

	var req dto.RouteHistoryRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		logger.Error("Invalid route history request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Set defaults
	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 20
	}
	if req.SortBy == "" {
		req.SortBy = "created_at"
	}
	if req.SortOrder == "" {
		req.SortOrder = "desc"
	}

	response, err := h.routeService.GetRouteHistory(c.Request.Context(), userID, &req)
	if err != nil {
		logger.Error("Failed to get route history", 
			zap.Uint("user_id", userID),
			zap.Error(err))

		// Handle specific error types
		switch err {
		case services.ErrViewPermissionDenied:
			c.JSON(http.StatusForbidden, gin.H{
				"error": "View permission denied",
				"code":  "VIEW_PERMISSION_DENIED",
			})
		default:
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve route history"})
		}
		return
	}

	logger.Info("Route history retrieved successfully", 
		zap.Uint("user_id", userID),
		zap.Int("count", len(response.Data)))

	c.JSON(http.StatusOK, response)
}

// AnalyzeRoute handles GET /api/routes/:id/analyze (Premium feature)
func (h *RouteHandler) AnalyzeRoute(c *gin.Context) {
	userIDAuth, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "user not authenticated"})
		return
	}

	userID, ok := userIDAuth.(uint)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "invalid user ID type in context"})
		return
	}

	idStr := c.Param("id")
	routeID, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		logger.Error("Invalid route ID", zap.String("id", idStr), zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid route ID"})
		return
	}

	response, err := h.routeService.AnalyzeRoute(c.Request.Context(), userID, uint(routeID))
	if err != nil {
		logger.Error("Failed to analyze route", 
			zap.Uint("user_id", userID),
			zap.Uint64("route_id", routeID),
			zap.Error(err))

		// Handle specific error types
		switch err {
		case services.ErrPremiumFeatureRequired:
			c.JSON(http.StatusPaymentRequired, gin.H{
				"error": "Premium subscription required for route analysis",
				"code":  "PREMIUM_REQUIRED",
			})
		case services.ErrRouteNotFound:
			c.JSON(http.StatusNotFound, gin.H{"error": "Route not found"})
		default:
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to analyze route"})
		}
		return
	}

	logger.Info("Route analysis completed", 
		zap.Uint("user_id", userID),
		zap.Uint64("route_id", routeID))

	c.JSON(http.StatusOK, response)
}

// GetRouteDetail handles GET /api/routes/:id
func (h *RouteHandler) GetRouteDetail(c *gin.Context) {
	userIDAuth, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "user not authenticated"})
		return
	}

	userID, ok := userIDAuth.(uint)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "invalid user ID type in context"})
		return
	}

	idStr := c.Param("id")
	routeID, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		logger.Error("Invalid route ID", zap.String("id", idStr), zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid route ID"})
		return
	}

	// For now, return a placeholder response
	// This would be implemented with actual route detail retrieval
	response := gin.H{
		"id":      routeID,
		"user_id": userID,
		"message": "Route detail retrieval not fully implemented yet",
	}

	logger.Info("Route detail requested", 
		zap.Uint("user_id", userID),
		zap.Uint64("route_id", routeID))

	c.JSON(http.StatusOK, response)
}

// DeleteRoute handles DELETE /api/routes/:id
func (h *RouteHandler) DeleteRoute(c *gin.Context) {
	userIDAuth, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "user not authenticated"})
		return
	}

	userID, ok := userIDAuth.(uint)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "invalid user ID type in context"})
		return
	}

	idStr := c.Param("id")
	routeID, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		logger.Error("Invalid route ID", zap.String("id", idStr), zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid route ID"})
		return
	}

	// For now, return a placeholder response
	// This would be implemented with actual route deletion
	response := gin.H{
		"id":      routeID,
		"user_id": userID,
		"message": "Route deletion not fully implemented yet",
	}

	logger.Info("Route deletion requested", 
		zap.Uint("user_id", userID),
		zap.Uint64("route_id", routeID))

	c.JSON(http.StatusOK, response)
}