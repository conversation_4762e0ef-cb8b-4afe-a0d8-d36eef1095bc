package handlers

import (
	"net/http"

	"github.com/azel-ko/final-ddd/internal/application/dto"
	"github.com/azel-ko/final-ddd/internal/application/services"
	"github.com/azel-ko/final-ddd/internal/pkg/logger"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type SubscriptionHandler struct {
	subscriptionService *services.SubscriptionService
}

func NewSubscriptionHandler(subscriptionService *services.SubscriptionService) *SubscriptionHandler {
	return &SubscriptionHandler{
		subscriptionService: subscriptionService,
	}
}

// GetAvailablePlans handles GET /api/subscriptions/plans
func (h *SubscriptionHandler) GetAvailablePlans(c *gin.Context) {
	plans, err := h.subscriptionService.GetAvailablePlans(c.Request.Context())
	if err != nil {
		logger.Error("Failed to get available plans", zap.Error(err))
		c.<PERSON>(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve subscription plans"})
		return
	}

	logger.Info("Available subscription plans retrieved", zap.Int("count", len(plans)))
	c.<PERSON>(http.StatusOK, gin.H{"data": plans})
}

// GetSubscriptionStatus handles GET /api/subscriptions/status
func (h *SubscriptionHandler) GetSubscriptionStatus(c *gin.Context) {
	userIDAuth, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "user not authenticated"})
		return
	}

	userID, ok := userIDAuth.(uint)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "invalid user ID type in context"})
		return
	}

	status, err := h.subscriptionService.GetSubscriptionStatus(c.Request.Context(), userID)
	if err != nil {
		logger.Error("Failed to get subscription status", 
			zap.Uint("user_id", userID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve subscription status"})
		return
	}

	logger.Info("Subscription status retrieved", 
		zap.Uint("user_id", userID),
		zap.String("plan", status.CurrentPlan))

	c.JSON(http.StatusOK, status)
}

// PurchaseSubscription handles POST /api/subscriptions/purchase
func (h *SubscriptionHandler) PurchaseSubscription(c *gin.Context) {
	userIDAuth, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "user not authenticated"})
		return
	}

	userID, ok := userIDAuth.(uint)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "invalid user ID type in context"})
		return
	}

	var req dto.SubscriptionPurchaseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Invalid subscription purchase request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	response, err := h.subscriptionService.PurchaseSubscription(c.Request.Context(), userID, &req)
	if err != nil {
		logger.Error("Failed to purchase subscription", 
			zap.Uint("user_id", userID),
			zap.String("plan_id", req.PlanID),
			zap.Error(err))

		// Handle specific error types
		switch err {
		case services.ErrInvalidSubscriptionPlan:
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Invalid subscription plan",
				"code":  "INVALID_PLAN",
			})
		case services.ErrPaymentFailed:
			c.JSON(http.StatusPaymentRequired, gin.H{
				"error": "Payment processing failed",
				"code":  "PAYMENT_FAILED",
			})
		default:
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process subscription purchase"})
		}
		return
	}

	logger.Info("Subscription purchase initiated", 
		zap.Uint("user_id", userID),
		zap.String("plan_id", req.PlanID),
		zap.String("order_id", response.OrderID))

	c.JSON(http.StatusCreated, response)
}

// RenewSubscription handles POST /api/subscriptions/renew
func (h *SubscriptionHandler) RenewSubscription(c *gin.Context) {
	userIDAuth, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "user not authenticated"})
		return
	}

	userID, ok := userIDAuth.(uint)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "invalid user ID type in context"})
		return
	}

	var req dto.SubscriptionRenewalRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Invalid subscription renewal request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	response, err := h.subscriptionService.RenewSubscription(c.Request.Context(), userID, &req)
	if err != nil {
		logger.Error("Failed to renew subscription", 
			zap.Uint("user_id", userID),
			zap.Error(err))

		// Handle specific error types
		switch err {
		case services.ErrNoActiveSubscription:
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "No active subscription to renew",
				"code":  "NO_ACTIVE_SUBSCRIPTION",
			})
		case services.ErrInvalidSubscriptionPlan:
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Invalid subscription plan",
				"code":  "INVALID_PLAN",
			})
		default:
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process subscription renewal"})
		}
		return
	}

	logger.Info("Subscription renewal initiated", 
		zap.Uint("user_id", userID),
		zap.String("order_id", response.OrderID))

	c.JSON(http.StatusOK, response)
}

// CancelSubscription handles DELETE /api/subscriptions/cancel
func (h *SubscriptionHandler) CancelSubscription(c *gin.Context) {
	userIDAuth, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "user not authenticated"})
		return
	}

	userID, ok := userIDAuth.(uint)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "invalid user ID type in context"})
		return
	}

	err := h.subscriptionService.CancelSubscription(c.Request.Context(), userID)
	if err != nil {
		logger.Error("Failed to cancel subscription", 
			zap.Uint("user_id", userID),
			zap.Error(err))

		// Handle specific error types
		switch err {
		case services.ErrNoActiveSubscription:
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "No active subscription to cancel",
				"code":  "NO_ACTIVE_SUBSCRIPTION",
			})
		default:
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to cancel subscription"})
		}
		return
	}

	logger.Info("Subscription cancelled", zap.Uint("user_id", userID))
	c.JSON(http.StatusOK, gin.H{"message": "Subscription cancelled successfully"})
}

// GetUsageStats handles GET /api/subscriptions/usage-stats
func (h *SubscriptionHandler) GetUsageStats(c *gin.Context) {
	userIDAuth, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "user not authenticated"})
		return
	}

	userID, ok := userIDAuth.(uint)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "invalid user ID type in context"})
		return
	}

	var req dto.UsageStatsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		logger.Error("Invalid usage stats request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Set default granularity if not provided
	if req.Granularity == "" {
		req.Granularity = "daily"
	}

	stats, err := h.subscriptionService.GetUsageStats(c.Request.Context(), userID, &req)
	if err != nil {
		logger.Error("Failed to get usage statistics", 
			zap.Uint("user_id", userID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve usage statistics"})
		return
	}

	logger.Info("Usage statistics retrieved", 
		zap.Uint("user_id", userID),
		zap.Int64("total_requests", stats.TotalRequests))

	c.JSON(http.StatusOK, stats)
}