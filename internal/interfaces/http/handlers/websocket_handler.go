package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/azel-ko/final-ddd/internal/application/services"
	"github.com/azel-ko/final-ddd/internal/infrastructure/websocket"
	"github.com/azel-ko/final-ddd/internal/pkg/logger"
	"go.uber.org/zap"
)

// WebSocketHandler WebSocket处理器
type WebSocketHandler struct {
	wsHub               *websocket.Hub
	notificationService *services.NotificationService
}

// NewWebSocketHandler 创建WebSocket处理器
func NewWebSocketHandler(
	wsHub *websocket.Hub,
	notificationService *services.NotificationService,
) *WebSocketHandler {
	return &WebSocketHandler{
		wsHub:               wsHub,
		notificationService: notificationService,
	}
}

// HandleConnection 处理WebSocket连接
func (h *WebSocketHandler) HandleConnection(c *gin.Context) {
	// 从JWT中获取用户ID
	userIDInterface, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "未授权访问"})
		return
	}

	userID, ok := userIDInterface.(uint)
	if !ok {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的用户ID"})
		return
	}

	logger.Info("WebSocket connection request", zap.Uint("user_id", userID))

	// 使用Hub处理WebSocket连接
	h.wsHub.HandleWebSocket(c)
}

// SubscribeNotifications 订阅通知
func (h *WebSocketHandler) SubscribeNotifications(c *gin.Context) {
	userIDInterface, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "未授权访问"})
		return
	}

	userID := strconv.FormatUint(uint64(userIDInterface.(uint)), 10)

	var req struct {
		Routes      []string                              `json:"routes"`
		Locations   []services.Location                   `json:"locations"`
		Checkpoints []string                              `json:"checkpoints"`
		Preferences services.NotificationPrefs            `json:"preferences"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数错误: " + err.Error()})
		return
	}

	subscription := &services.UserSubscription{
		Routes:      req.Routes,
		Locations:   req.Locations,
		Checkpoints: req.Checkpoints,
		Preferences: req.Preferences,
	}

	if err := h.notificationService.SubscribeUser(userID, subscription); err != nil {
		logger.Error("Failed to subscribe user", zap.Error(err), zap.String("user_id", userID))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "订阅失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "订阅成功",
		"subscription": subscription,
	})
}

// UpdateSubscription 更新订阅
func (h *WebSocketHandler) UpdateSubscription(c *gin.Context) {
	userIDInterface, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "未授权访问"})
		return
	}

	userID := strconv.FormatUint(uint64(userIDInterface.(uint)), 10)

	var req struct {
		Routes      []string                              `json:"routes"`
		Locations   []services.Location                   `json:"locations"`
		Checkpoints []string                              `json:"checkpoints"`
		Preferences services.NotificationPrefs            `json:"preferences"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数错误: " + err.Error()})
		return
	}

	subscription := &services.UserSubscription{
		Routes:      req.Routes,
		Locations:   req.Locations,
		Checkpoints: req.Checkpoints,
		Preferences: req.Preferences,
	}

	if err := h.notificationService.UpdateUserSubscription(userID, subscription); err != nil {
		logger.Error("Failed to update subscription", zap.Error(err), zap.String("user_id", userID))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新订阅失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "订阅更新成功",
		"subscription": subscription,
	})
}

// GetSubscription 获取订阅信息
func (h *WebSocketHandler) GetSubscription(c *gin.Context) {
	userIDInterface, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "未授权访问"})
		return
	}

	userID := strconv.FormatUint(uint64(userIDInterface.(uint)), 10)

	subscription, err := h.notificationService.GetUserSubscription(userID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "未找到订阅信息"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"subscription": subscription,
	})
}

// UnsubscribeNotifications 取消订阅
func (h *WebSocketHandler) UnsubscribeNotifications(c *gin.Context) {
	userIDInterface, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "未授权访问"})
		return
	}

	userID := strconv.FormatUint(uint64(userIDInterface.(uint)), 10)

	if err := h.notificationService.UnsubscribeUser(userID); err != nil {
		logger.Error("Failed to unsubscribe user", zap.Error(err), zap.String("user_id", userID))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "取消订阅失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "取消订阅成功",
	})
}

// GetNotificationStats 获取通知统计信息（管理员接口）
func (h *WebSocketHandler) GetNotificationStats(c *gin.Context) {
	// 这里应该检查管理员权限，简化处理
	stats := h.notificationService.GetSubscriptionStats()
	
	c.JSON(http.StatusOK, gin.H{
		"stats": stats,
	})
}

// SendTestNotification 发送测试通知（管理员接口）
func (h *WebSocketHandler) SendTestNotification(c *gin.Context) {
	var req struct {
		Title    string `json:"title" binding:"required"`
		Content  string `json:"content" binding:"required"`
		Priority int    `json:"priority"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数错误: " + err.Error()})
		return
	}

	if req.Priority == 0 {
		req.Priority = 3 // 默认中等优先级
	}

	if err := h.notificationService.NotifySystemAlert(req.Title, req.Content, req.Priority); err != nil {
		logger.Error("Failed to send test notification", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "发送测试通知失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "测试通知发送成功",
	})
}