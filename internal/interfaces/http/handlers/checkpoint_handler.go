package handlers

import (
	"net/http"
	"strconv"

	"github.com/azel-ko/final-ddd/internal/application/dto"
	"github.com/azel-ko/final-ddd/internal/application/services"
	"github.com/azel-ko/final-ddd/internal/pkg/logger"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type CheckpointHandler struct {
	checkpointService *services.CheckpointService
}

func NewCheckpointHandler(checkpointService *services.CheckpointService) *CheckpointHandler {
	return &CheckpointHandler{
		checkpointService: checkpointService,
	}
}

// GetCheckpointList handles GET /api/checkpoints
func (h *CheckpointHandler) GetCheckpointList(c *gin.Context) {
	var req dto.CheckpointListRequest
	if err := c.Should<PERSON>ind<PERSON>uery(&req); err != nil {
		logger.Error("Invalid checkpoint list request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Set defaults if not provided
	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 20
	}
	if req.SortBy == "" {
		req.SortBy = "created_at"
	}
	if req.SortOrder == "" {
		req.SortOrder = "desc"
	}

	response, err := h.checkpointService.GetCheckpointList(c.Request.Context(), &req)
	if err != nil {
		logger.Error("Failed to get checkpoint list", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve checkpoints"})
		return
	}

	logger.Info("Checkpoint list retrieved successfully", 
		zap.Int("count", len(response.Data)),
		zap.Int64("total", response.Total))

	c.JSON(http.StatusOK, response)
}

// GetNearbyCheckpoints handles GET /api/checkpoints/nearby
func (h *CheckpointHandler) GetNearbyCheckpoints(c *gin.Context) {
	var req dto.NearbyCheckpointsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		logger.Error("Invalid nearby checkpoints request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Set defaults if not provided
	if req.Radius == 0 {
		req.Radius = 10
	}
	if req.Limit == 0 {
		req.Limit = 20
	}

	response, err := h.checkpointService.GetNearbyCheckpoints(c.Request.Context(), &req)
	if err != nil {
		logger.Error("Failed to get nearby checkpoints", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve nearby checkpoints"})
		return
	}

	logger.Info("Nearby checkpoints retrieved successfully", 
		zap.Int("count", len(response)),
		zap.Float64("latitude", req.Latitude),
		zap.Float64("longitude", req.Longitude))

	c.JSON(http.StatusOK, gin.H{"data": response})
}

// GetCheckpointDetail handles GET /api/checkpoints/:id
func (h *CheckpointHandler) GetCheckpointDetail(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		logger.Error("Invalid checkpoint ID", zap.String("id", idStr), zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid checkpoint ID"})
		return
	}

	response, err := h.checkpointService.GetCheckpointDetail(c.Request.Context(), uint(id))
	if err != nil {
		logger.Error("Failed to get checkpoint detail", zap.Uint64("id", id), zap.Error(err))
		c.JSON(http.StatusNotFound, gin.H{"error": "Checkpoint not found"})
		return
	}

	logger.Info("Checkpoint detail retrieved successfully", zap.Uint64("id", id))
	c.JSON(http.StatusOK, response)
}

// ReportCheckpoint handles POST /api/checkpoints/report
func (h *CheckpointHandler) ReportCheckpoint(c *gin.Context) {
	userIDAuth, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "user not authenticated"})
		return
	}

	userID, ok := userIDAuth.(uint)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "invalid user ID type in context"})
		return
	}

	var req dto.CheckpointReportRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Invalid checkpoint report request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	response, err := h.checkpointService.ReportCheckpoint(c.Request.Context(), userID, &req)
	if err != nil {
		logger.Error("Failed to create checkpoint report", 
			zap.Uint("user_id", userID),
			zap.Uint("checkpoint_id", req.CheckpointID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create report"})
		return
	}

	logger.Info("Checkpoint report created successfully", 
		zap.Uint("user_id", userID),
		zap.Uint("checkpoint_id", req.CheckpointID),
		zap.String("status", req.Status))

	c.JSON(http.StatusCreated, response)
}

// GetCheckpointReports handles GET /api/checkpoints/:id/reports
func (h *CheckpointHandler) GetCheckpointReports(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		logger.Error("Invalid checkpoint ID", zap.String("id", idStr), zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid checkpoint ID"})
		return
	}

	response, err := h.checkpointService.GetCheckpointReports(c.Request.Context(), uint(id))
	if err != nil {
		logger.Error("Failed to get checkpoint reports", zap.Uint64("id", id), zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve reports"})
		return
	}

	logger.Info("Checkpoint reports retrieved successfully", 
		zap.Uint64("id", id),
		zap.Int("count", len(response)))

	c.JSON(http.StatusOK, gin.H{"data": response})
}

// CreateCheckpoint handles POST /api/checkpoints (Admin only)
func (h *CheckpointHandler) CreateCheckpoint(c *gin.Context) {
	var req dto.CheckpointRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Invalid checkpoint creation request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// This would typically be handled by an admin service
	// For now, return a placeholder response
	logger.Info("Checkpoint creation requested", zap.String("name", req.Name))
	c.JSON(http.StatusCreated, gin.H{"message": "Checkpoint creation not fully implemented yet"})
}

// UpdateCheckpoint handles PUT /api/checkpoints/:id (Admin only)
func (h *CheckpointHandler) UpdateCheckpoint(c *gin.Context) {
	// 检查管理员权限
	role, exists := c.Get("role")
	if !exists || role != "admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "权限不足"})
		return
	}

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		logger.Error("Invalid checkpoint ID", zap.String("id", idStr), zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid checkpoint ID"})
		return
	}

	var req dto.CheckpointRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Invalid checkpoint update request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	response, err := h.checkpointService.UpdateCheckpoint(c.Request.Context(), uint(id), &req)
	if err != nil {
		logger.Error("Failed to update checkpoint", zap.Uint64("id", id), zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update checkpoint"})
		return
	}

	logger.Info("Checkpoint updated successfully", zap.Uint64("id", id), zap.String("name", req.Name))
	c.JSON(http.StatusOK, response)
}

// DeleteCheckpoint handles DELETE /api/checkpoints/:id (Admin only)
func (h *CheckpointHandler) DeleteCheckpoint(c *gin.Context) {
	// 检查管理员权限
	role, exists := c.Get("role")
	if !exists || role != "admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "权限不足"})
		return
	}

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		logger.Error("Invalid checkpoint ID", zap.String("id", idStr), zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid checkpoint ID"})
		return
	}

	err = h.checkpointService.DeleteCheckpoint(c.Request.Context(), uint(id))
	if err != nil {
		logger.Error("Failed to delete checkpoint", zap.Uint64("id", id), zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete checkpoint"})
		return
	}

	logger.Info("Checkpoint deleted successfully", zap.Uint64("id", id))
	c.JSON(http.StatusOK, gin.H{"message": "Checkpoint deleted successfully"})
}

// GetCheckpointStats handles GET /api/v1/checkpoints/stats
func (h *CheckpointHandler) GetCheckpointStats(c *gin.Context) {
	stats, err := h.checkpointService.GetCheckpointStats(c.Request.Context())
	if err != nil {
		logger.Error("Failed to get checkpoint statistics", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve statistics"})
		return
	}

	logger.Info("Checkpoint statistics retrieved successfully")
	c.JSON(http.StatusOK, stats)
}

// BatchUpdateCheckpoints handles PUT /api/v1/checkpoints/batch (Admin only)
func (h *CheckpointHandler) BatchUpdateCheckpoints(c *gin.Context) {
	// 检查管理员权限
	role, exists := c.Get("role")
	if !exists || role != "admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "权限不足"})
		return
	}

	var req dto.BatchUpdateCheckpointsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Invalid batch update request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if len(req.IDs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No checkpoint IDs provided"})
		return
	}

	result, err := h.checkpointService.BatchUpdateCheckpoints(c.Request.Context(), &req)
	if err != nil {
		logger.Error("Failed to batch update checkpoints", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update checkpoints"})
		return
	}

	logger.Info("Batch update completed", 
		zap.Int("total", len(req.IDs)),
		zap.Int("success", result.SuccessCount),
		zap.Int("failed", result.FailedCount))

	c.JSON(http.StatusOK, result)
}