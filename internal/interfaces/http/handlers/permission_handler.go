package handlers

import (
	"net/http"

	"github.com/azel-ko/final-ddd/internal/application/services"
	"github.com/azel-ko/final-ddd/internal/pkg/logger"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type PermissionHandler struct {
	permissionService *services.PermissionService
}

func NewPermissionHandler(permissionService *services.PermissionService) *PermissionHandler {
	return &PermissionHandler{
		permissionService: permissionService,
	}
}

// CheckPermissions handles GET /api/permissions/check
func (h *PermissionHandler) CheckPermissions(c *gin.Context) {
	userIDAuth, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "user not authenticated"})
		return
	}

	userID, ok := userIDAuth.(uint)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "invalid user ID type in context"})
		return
	}

	permissions, err := h.permissionService.CheckUserPermissions(c.Request.Context(), userID)
	if err != nil {
		logger.Error("Failed to check user permissions", 
			zap.Uint("user_id", userID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check permissions"})
		return
	}

	logger.Info("User permissions checked", 
		zap.Uint("user_id", userID),
		zap.Int("level", int(permissions.Level)))

	c.JSON(http.StatusOK, permissions)
}

// ValidateFeature handles POST /api/permissions/validate
func (h *PermissionHandler) ValidateFeature(c *gin.Context) {
	userIDAuth, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "user not authenticated"})
		return
	}

	userID, ok := userIDAuth.(uint)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "invalid user ID type in context"})
		return
	}

	var req struct {
		Feature string `json:"feature" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Invalid feature validation request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err := h.permissionService.ValidateFeatureAccess(c.Request.Context(), userID, req.Feature)
	if err != nil {
		logger.Warn("Feature access denied", 
			zap.Uint("user_id", userID),
			zap.String("feature", req.Feature),
			zap.Error(err))

		// Handle specific error types
		switch err {
		case services.ErrNavigationAccessDenied:
			c.JSON(http.StatusForbidden, gin.H{
				"error": "Navigation access denied - upgrade required",
				"code":  "NAVIGATION_ACCESS_DENIED",
				"feature": req.Feature,
			})
		case services.ErrPremiumFeatureRequired:
			c.JSON(http.StatusPaymentRequired, gin.H{
				"error": "Premium subscription required for this feature",
				"code":  "PREMIUM_REQUIRED",
				"feature": req.Feature,
			})
		case services.ErrBasicAccessDenied:
			c.JSON(http.StatusForbidden, gin.H{
				"error": "Basic access denied",
				"code":  "BASIC_ACCESS_DENIED",
				"feature": req.Feature,
			})
		case services.ErrTrialExpired:
			c.JSON(http.StatusForbidden, gin.H{
				"error": "Trial period has expired",
				"code":  "TRIAL_EXPIRED",
				"feature": req.Feature,
			})
		default:
			c.JSON(http.StatusForbidden, gin.H{
				"error": "Access denied",
				"code":  "ACCESS_DENIED",
				"feature": req.Feature,
			})
		}
		return
	}

	logger.Info("Feature access validated", 
		zap.Uint("user_id", userID),
		zap.String("feature", req.Feature))

	c.JSON(http.StatusOK, gin.H{
		"message": "Access granted",
		"feature": req.Feature,
	})
}

// ValidateDevice handles POST /api/permissions/validate-device
func (h *PermissionHandler) ValidateDevice(c *gin.Context) {
	userIDAuth, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "user not authenticated"})
		return
	}

	userID, ok := userIDAuth.(uint)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "invalid user ID type in context"})
		return
	}

	var req struct {
		DeviceID string `json:"device_id" binding:"required"`
		Platform string `json:"platform" binding:"required,oneof=web mobile car miniprogram"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Invalid device validation request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err := h.permissionService.ValidateDeviceAccess(c.Request.Context(), userID, req.DeviceID, req.Platform)
	if err != nil {
		logger.Warn("Device access denied", 
			zap.Uint("user_id", userID),
			zap.String("device_id", req.DeviceID),
			zap.String("platform", req.Platform),
			zap.Error(err))

		// Handle specific error types
		switch err {
		case services.ErrDeviceLimitExceeded:
			c.JSON(http.StatusForbidden, gin.H{
				"error": "Device limit exceeded",
				"code":  "DEVICE_LIMIT_EXCEEDED",
			})
		case services.ErrCarPlatformRequiresPremium:
			c.JSON(http.StatusPaymentRequired, gin.H{
				"error": "Car platform requires premium subscription",
				"code":  "CAR_PLATFORM_PREMIUM_REQUIRED",
			})
		case services.ErrMiniProgramAccessDenied:
			c.JSON(http.StatusForbidden, gin.H{
				"error": "Mini-program access denied",
				"code":  "MINIPROGRAM_ACCESS_DENIED",
			})
		default:
			c.JSON(http.StatusForbidden, gin.H{
				"error": "Device access denied",
				"code":  "DEVICE_ACCESS_DENIED",
			})
		}
		return
	}

	logger.Info("Device access validated", 
		zap.Uint("user_id", userID),
		zap.String("device_id", req.DeviceID),
		zap.String("platform", req.Platform))

	c.JSON(http.StatusOK, gin.H{
		"message": "Device access granted",
		"device_id": req.DeviceID,
		"platform": req.Platform,
	})
}

// EnforceTrial handles POST /api/permissions/enforce-trial
func (h *PermissionHandler) EnforceTrial(c *gin.Context) {
	userIDAuth, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "user not authenticated"})
		return
	}

	userID, ok := userIDAuth.(uint)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "invalid user ID type in context"})
		return
	}

	err := h.permissionService.EnforceTrialLimitations(c.Request.Context(), userID)
	if err != nil {
		logger.Warn("Trial enforcement failed", 
			zap.Uint("user_id", userID),
			zap.Error(err))

		if err == services.ErrTrialExpired {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "Trial period has expired - account downgraded to free",
				"code":  "TRIAL_EXPIRED_DOWNGRADED",
			})
			return
		}

		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to enforce trial limitations"})
		return
	}

	logger.Info("Trial limitations enforced", zap.Uint("user_id", userID))
	c.JSON(http.StatusOK, gin.H{"message": "Trial limitations enforced"})
}