package handlers

import (
	"net/http"

	"github.com/azel-ko/final-ddd/internal/application/dto"
	"github.com/azel-ko/final-ddd/internal/application/services"
	"github.com/azel-ko/final-ddd/internal/pkg/logger"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type AuthHandler struct {
	authService *services.AuthService
}

func NewAuthHandler(authService *services.AuthService) *AuthHandler {
	return &AuthHandler{authService: authService}
}



func (h *AuthHandler) Login(c *gin.Context) {
	var req dto.LoginRequest
	if err := c.ShouldBind<PERSON>(&req); err != nil {
		logger.Error("Invalid login request", zap.Error(err))
		c.JSO<PERSON>(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Extract client IP address
	ipAddress := c.<PERSON>()
	
	// Set User-Agent if not provided in request
	if req.UserAgent == "" {
		req.UserAgent = c.<PERSON>("User-Agent")
	}

	response, err := h.authService.Login(c.Request.Context(), &req, ipAddress)
	if err != nil {
		logger.Error("Login failed",
			zap.String("email", req.Email),
			zap.String("ip_address", ipAddress),
			zap.String("platform", req.Platform),
			zap.Error(err),
		)
		c.JSON(http.StatusUnauthorized, gin.H{"error": "invalid credentials"})
		return
	}

	logger.Info("User logged in successfully",
		zap.String("email", req.Email),
		zap.Uint("user_id", response.User.ID),
		zap.String("platform", req.Platform),
		zap.String("session_id", response.SessionID),
	)
	c.JSON(http.StatusOK, response)
}

// Register godoc
func (h *AuthHandler) Register(c *gin.Context) {
	var req dto.UserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Invalid registration request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	user, err := h.authService.Register(&req)
	if err != nil {
		logger.Error("Registration failed",
			zap.String("email", req.Email),
			zap.Error(err),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	logger.Info("User registered successfully",
		zap.String("email", req.Email),
		zap.Uint("user_id", user.ID),
	)
	c.JSON(http.StatusCreated, user)
}
