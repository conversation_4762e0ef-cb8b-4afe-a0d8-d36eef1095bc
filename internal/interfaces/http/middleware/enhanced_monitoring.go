package middleware

import (
	"bytes"
	"io"
	"strconv"
	"strings"
	"time"

	"github.com/azel-ko/final-ddd/internal/infrastructure/monitoring"
	"github.com/azel-ko/final-ddd/internal/pkg/logger"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// EnhancedAPIStatsMiddleware 增强的API统计中间件，集成StatsCollector
func EnhancedAPIStatsMiddleware(statsCollector *monitoring.StatsCollector) gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		method := c.Request.Method

		// 记录请求体大小
		var requestSize int
		if c.Request.Body != nil {
			requestBody, _ := io.ReadAll(c.Request.Body)
			requestSize = len(requestBody)
			c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))
		}

		// 处理请求
		c.Next()

		// 计算响应时间
		duration := time.Since(start)
		statusCode := c.Writer.Status()
		responseSize := c.Writer.Size()

		// 获取用户ID（如果存在）
		userID := ""
		if userIDValue, exists := c.Get("user_id"); exists {
			if uid, ok := userIDValue.(string); ok {
				userID = uid
			}
		}

		// 创建API调用统计
		stats := monitoring.APICallStats{
			Endpoint:     path,
			Method:       method,
			StatusCode:   statusCode,
			ResponseTime: duration.Milliseconds(),
			Timestamp:    start,
			ClientIP:     c.ClientIP(),
			UserAgent:    c.Request.UserAgent(),
			UserID:       userID,
			RequestSize:  requestSize,
			ResponseSize: responseSize,
		}

		// 如果是错误状态码，记录错误信息
		if statusCode >= 400 {
			if len(c.Errors) > 0 {
				stats.ErrorMessage = c.Errors.String()
			} else {
				stats.ErrorMessage = "HTTP " + strconv.Itoa(statusCode)
			}
		}

		// 记录到统计收集器
		if statsCollector != nil {
			statsCollector.RecordAPICall(stats)
		}

		// 记录到日志
		logger.Info("API call",
			zap.String("method", method),
			zap.String("path", path),
			zap.Int("status", statusCode),
			zap.Duration("duration", duration),
			zap.String("client_ip", c.ClientIP()),
			zap.Int("request_size", requestSize),
			zap.Int("response_size", responseSize),
		)

		// 如果是错误状态码，记录错误
		if statusCode >= 400 {
			logger.Error("API error",
				zap.String("method", method),
				zap.String("path", path),
				zap.Int("status", statusCode),
				zap.Duration("duration", duration),
				zap.String("client_ip", c.ClientIP()),
				zap.String("error", stats.ErrorMessage),
			)
		}
	}
}

// EnhancedErrorTrackingMiddleware 增强的错误追踪中间件
func EnhancedErrorTrackingMiddleware(statsCollector *monitoring.StatsCollector) gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				// 创建错误条目
				errorEntry := monitoring.ErrorEntry{
					Timestamp:  time.Now(),
					ErrorType:  "PanicError",
					Message:    "Panic recovered: " + toString(err),
					StackTrace: getStackTrace(),
					Endpoint:   c.Request.URL.Path,
					Method:     c.Request.Method,
					ClientIP:   c.ClientIP(),
					UserAgent:  c.Request.UserAgent(),
				}

				// 获取用户ID（如果存在）
				if userIDValue, exists := c.Get("user_id"); exists {
					if uid, ok := userIDValue.(string); ok {
						errorEntry.UserID = uid
					}
				}

				// 获取请求ID（如果存在）
				if requestID := c.GetHeader("X-Request-ID"); requestID != "" {
					errorEntry.RequestID = requestID
				}

				// 记录到统计收集器
				if statsCollector != nil {
					statsCollector.RecordError(errorEntry)
				}

				// 记录到日志
				logger.Error("Panic recovered",
					zap.Any("error", err),
					zap.String("method", c.Request.Method),
					zap.String("path", c.Request.URL.Path),
					zap.String("client_ip", c.ClientIP()),
				)

				// 返回500错误
				c.JSON(500, gin.H{
					"error": "Internal server error",
					"code":  "INTERNAL_ERROR",
				})
				c.Abort()
			}
		}()

		c.Next()

		// 检查是否有错误
		if len(c.Errors) > 0 {
			for _, err := range c.Errors {
				// 创建错误条目
				errorEntry := monitoring.ErrorEntry{
					Timestamp: time.Now(),
					ErrorType: "RequestError",
					Message:   err.Error(),
					Endpoint:  c.Request.URL.Path,
					Method:    c.Request.Method,
					ClientIP:  c.ClientIP(),
					UserAgent: c.Request.UserAgent(),
				}

				// 获取用户ID（如果存在）
				if userIDValue, exists := c.Get("user_id"); exists {
					if uid, ok := userIDValue.(string); ok {
						errorEntry.UserID = uid
					}
				}

				// 记录到统计收集器
				if statsCollector != nil {
					statsCollector.RecordError(errorEntry)
				}

				// 记录到日志
				logger.Error("Request error",
					zap.Error(err.Err),
					zap.String("method", c.Request.Method),
					zap.String("path", c.Request.URL.Path),
					zap.String("client_ip", c.ClientIP()),
				)
			}
		}
	}
}

// EnhancedPerformanceMonitoringMiddleware 增强的性能监控中间件
func EnhancedPerformanceMonitoringMiddleware(statsCollector *monitoring.StatsCollector) gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()

		c.Next()

		duration := time.Since(start)
		responseSize := c.Writer.Size()

		// 性能阈值配置
		slowRequestThreshold := 2 * time.Second
		largeResponseThreshold := 1024 * 1024 // 1MB

		// 检查慢请求
		if duration > slowRequestThreshold {
			// 创建日志条目
			logEntry := monitoring.LogEntry{
				Timestamp: time.Now(),
				Level:     "WARN",
				Message:   "Slow request detected",
				Source:    "performance_monitor",
				Details: map[string]interface{}{
					"method":        c.Request.Method,
					"path":          c.Request.URL.Path,
					"duration_ms":   duration.Milliseconds(),
					"threshold_ms":  slowRequestThreshold.Milliseconds(),
					"status":        c.Writer.Status(),
					"client_ip":     c.ClientIP(),
					"response_size": responseSize,
				},
			}

			// 记录到统计收集器
			if statsCollector != nil {
				statsCollector.RecordLog(logEntry)
			}

			logger.Warn("Slow request detected",
				zap.String("method", c.Request.Method),
				zap.String("path", c.Request.URL.Path),
				zap.Duration("duration", duration),
				zap.Int("status", c.Writer.Status()),
				zap.String("client_ip", c.ClientIP()),
			)
		}

		// 检查大响应
		if responseSize > largeResponseThreshold {
			// 创建日志条目
			logEntry := monitoring.LogEntry{
				Timestamp: time.Now(),
				Level:     "WARN",
				Message:   "Large response detected",
				Source:    "performance_monitor",
				Details: map[string]interface{}{
					"method":        c.Request.Method,
					"path":          c.Request.URL.Path,
					"response_size": responseSize,
					"threshold":     largeResponseThreshold,
					"duration_ms":   duration.Milliseconds(),
				},
			}

			// 记录到统计收集器
			if statsCollector != nil {
				statsCollector.RecordLog(logEntry)
			}

			logger.Warn("Large response detected",
				zap.String("method", c.Request.Method),
				zap.String("path", c.Request.URL.Path),
				zap.String("response_size", strconv.Itoa(responseSize)),
			)
		}

		// 记录性能指标到日志条目
		if duration > 100*time.Millisecond { // 只记录超过100ms的请求
			logEntry := monitoring.LogEntry{
				Timestamp: time.Now(),
				Level:     "INFO",
				Message:   "API performance metrics",
				Source:    "performance_monitor",
				Details: map[string]interface{}{
					"method":        c.Request.Method,
					"path":          c.Request.URL.Path,
					"duration_ms":   duration.Milliseconds(),
					"status":        c.Writer.Status(),
					"response_size": responseSize,
					"client_ip":     c.ClientIP(),
				},
			}

			// 记录到统计收集器
			if statsCollector != nil {
				statsCollector.RecordLog(logEntry)
			}
		}
	}
}

// DatabaseQueryMonitoringMiddleware 数据库查询监控中间件
func DatabaseQueryMonitoringMiddleware(statsCollector *monitoring.StatsCollector) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 在请求开始时设置查询计数器
		c.Set("db_query_start_time", time.Now())
		c.Set("db_query_count", 0)

		c.Next()

		// 检查数据库查询性能
		if startTime, exists := c.Get("db_query_start_time"); exists {
			if start, ok := startTime.(time.Time); ok {
				queryDuration := time.Since(start)
				queryCount := 0
				if count, exists := c.Get("db_query_count"); exists {
					if c, ok := count.(int); ok {
						queryCount = c
					}
				}

				// 如果查询时间过长或查询次数过多，记录警告
				if queryDuration > 1*time.Second || queryCount > 10 {
					logEntry := monitoring.LogEntry{
						Timestamp: time.Now(),
						Level:     "WARN",
						Message:   "Database performance issue detected",
						Source:    "db_monitor",
						Details: map[string]interface{}{
							"method":            c.Request.Method,
							"path":              c.Request.URL.Path,
							"query_duration":    queryDuration.Milliseconds(),
							"query_count":       queryCount,
							"total_duration":    time.Since(start).Milliseconds(),
							"performance_issue": queryDuration > 1*time.Second,
							"query_issue":       queryCount > 10,
						},
					}

					// 记录到统计收集器
					if statsCollector != nil {
						statsCollector.RecordLog(logEntry)
					}

					logger.Warn("Database performance issue",
						zap.String("method", c.Request.Method),
						zap.String("path", c.Request.URL.Path),
						zap.Duration("query_duration", queryDuration),
						zap.Int("query_count", queryCount),
					)
				}
			}
		}
	}
}

// 辅助函数

// toString 将任意类型转换为字符串
func toString(v interface{}) string {
	if v == nil {
		return "nil"
	}
	if s, ok := v.(string); ok {
		return s
	}
	return "unknown error"
}

// getStackTrace 获取堆栈跟踪（简化版）
func getStackTrace() string {
	// 在实际项目中，这里应该使用runtime包获取真实的堆栈跟踪
	return "stack trace not implemented"
}

// 性能优化相关的中间件

// CacheControlMiddleware 缓存控制中间件
func CacheControlMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		path := c.Request.URL.Path
		method := c.Request.Method

		// 对于静态资源设置缓存头
		if method == "GET" && (strings.HasPrefix(path, "/assets/") || strings.HasPrefix(path, "/static/")) {
			c.Header("Cache-Control", "public, max-age=31536000") // 1年
			c.Header("Expires", time.Now().Add(365*24*time.Hour).Format(time.RFC1123))
		} else if method == "GET" && (strings.HasPrefix(path, "/api/checkpoints") || strings.HasPrefix(path, "/api/routes")) {
			// 对于检查站和路线数据设置短期缓存
			c.Header("Cache-Control", "public, max-age=300") // 5分钟
		} else {
			// 对于其他API设置不缓存
			c.Header("Cache-Control", "no-cache, no-store, must-revalidate")
			c.Header("Pragma", "no-cache")
			c.Header("Expires", "0")
		}

		c.Next()
	}
}

// CompressionMiddleware 响应压缩中间件（简化版）
func CompressionMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查客户端是否支持gzip
		if strings.Contains(c.GetHeader("Accept-Encoding"), "gzip") {
			c.Header("Content-Encoding", "gzip")
		}

		c.Next()
	}
}
