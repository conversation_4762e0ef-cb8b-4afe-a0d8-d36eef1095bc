package middleware

import (
	"context"
	"net/http"
	"strings"
	"time"

	"github.com/azel-ko/final-ddd/internal/domain/entities"
	"github.com/azel-ko/final-ddd/internal/pkg/logger"
	"github.com/azel-ko/final-ddd/pkg/auth"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func AuthMiddleware(jwtManager *auth.JWTManager) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "authorization header is required"})
			return
		}

		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "invalid authorization header format"})
			return
		}

		claims, err := jwtManager.ValidateToken(tokenParts[1])
		if err != nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "invalid token"})
			return
		}

		// Set user context information
		c.Set("userID", claims.UserID)
		c.Set("userRole", claims.Role)
		c.Set("deviceID", claims.DeviceID)
		c.Set("platform", claims.Platform)
		c.Set("sessionID", claims.SessionID)
		
		c.Next()
	}
}

func RequireRole(roles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userRole, exists := c.Get("userRole")
		if !exists {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
			return
		}

		for _, role := range roles {
			if userRole == role {
				c.Next()
				return
			}
		}

		c.AbortWithStatusJSON(http.StatusForbidden, gin.H{"error": "insufficient permissions"})
	}
}

// RequireAdmin middleware that only allows admin users
func RequireAdmin() gin.HandlerFunc {
	return RequireRole("admin")
}

// RequirePlatform middleware that restricts access to specific platforms
func RequirePlatform(platforms ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userPlatform, exists := c.Get("platform")
		if !exists {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "platform information missing"})
			return
		}

		platform, ok := userPlatform.(string)
		if !ok {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "invalid platform information"})
			return
		}

		for _, allowedPlatform := range platforms {
			if platform == allowedPlatform {
				c.Next()
				return
			}
		}

		c.AbortWithStatusJSON(http.StatusForbidden, gin.H{"error": "platform not allowed"})
	}
}

// RequireSubscription middleware that checks user subscription level
func RequireSubscription(subscriptions ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// This would need to be enhanced to check user subscription from database
		// For now, we'll implement a basic version
		c.Next()
	}
}

// SessionValidationMiddleware validates the session is still active
func SessionValidationMiddleware(sessionService interface{}) gin.HandlerFunc {
	return func(c *gin.Context) {
		sessionID, exists := c.Get("sessionID")
		if !exists || sessionID == "" {
			c.Next() // Allow requests without session (backward compatibility)
			return
		}

		// 类型断言获取 SessionService
		if service, ok := sessionService.(interface {
			GetSession(ctx context.Context, sessionID string) (*entities.UserSession, error)
			RefreshSession(ctx context.Context, sessionID string) error
		}); ok {
			ctx := c.Request.Context()
			session, err := service.GetSession(ctx, sessionID.(string))
			if err != nil {
				c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "invalid session"})
				return
			}

			// 检查会话是否过期
			if session.ExpiresAt.Before(time.Now()) {
				c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "session expired"})
				return
			}

			// 检查会话是否激活
			if !session.IsActive {
				c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "session inactive"})
				return
			}

			// 刷新会话最后使用时间
			if err := service.RefreshSession(ctx, sessionID.(string)); err != nil {
				// 记录错误但不阻止请求
				logger.Error("failed to refresh session", zap.Error(err))
			}

			// 将会话信息添加到上下文
			c.Set("session", session)
		}

		c.Next()
	}
}

// RequireFeatureAccess middleware that validates access to specific features
func RequireFeatureAccess(feature string, permissionService interface{}) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := c.Get("userID")
		if !exists {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "user not authenticated"})
			return
		}

		uid, ok := userID.(uint)
		if !ok {
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": "invalid user ID"})
			return
		}

		// 类型断言获取权限服务
		if service, ok := permissionService.(interface {
			CheckUserPermission(userID uint, feature string) (interface{}, error)
		}); ok {
			permission, err := service.CheckUserPermission(uid, feature)
			if err != nil {
				logger.Error("failed to check user permission", 
					zap.Uint("user_id", uid), 
					zap.String("feature", feature), 
					zap.Error(err))
				c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": "permission check failed"})
				return
			}

			// 检查权限结果
			if perm, ok := permission.(interface{ 
				CanNavigate() bool
				CanViewCheckpoints() bool 
			}); ok {
				switch feature {
				case "navigation":
					if !perm.CanNavigate() {
						c.AbortWithStatusJSON(http.StatusForbidden, gin.H{"error": "navigation access denied"})
						return
					}
				case "checkpoints":
					if !perm.CanViewCheckpoints() {
						c.AbortWithStatusJSON(http.StatusForbidden, gin.H{"error": "checkpoint access denied"})
						return
					}
				case "premium":
					// 检查用户角色或订阅状态
					userRole, _ := c.Get("userRole")
					if userRole != "admin" && !perm.CanNavigate() {
						c.AbortWithStatusJSON(http.StatusForbidden, gin.H{"error": "premium access denied"})
						return
					}
				default:
					logger.Warn("unknown feature requested", zap.String("feature", feature))
				}
			}

			// 将权限信息添加到上下文
			c.Set("permissions", permission)
		} else {
			// 如果没有权限服务，进行基本的角色检查
			userRole, exists := c.Get("userRole")
			if !exists {
				c.AbortWithStatusJSON(http.StatusForbidden, gin.H{"error": "user role not found"})
				return
			}

			// 基本权限检查
			switch feature {
			case "admin":
				if userRole != "admin" {
					c.AbortWithStatusJSON(http.StatusForbidden, gin.H{"error": "admin access required"})
					return
				}
			case "premium":
				if userRole != "admin" && userRole != "premium" {
					c.AbortWithStatusJSON(http.StatusForbidden, gin.H{"error": "premium access required"})
					return
				}
			}
		}

		c.Next()
	}
}

// RequireOnlineVerification middleware that enforces online verification
func RequireOnlineVerification() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Add headers to prevent caching and ensure fresh requests
		c.Header("Cache-Control", "no-cache, no-store, must-revalidate")
		c.Header("Pragma", "no-cache")
		c.Header("Expires", "0")
		
		// In a real implementation, this would:
		// 1. Verify server connectivity
		// 2. Check license validity
		// 3. Perform anti-tampering checks
		// 4. Validate user session freshness
		
		c.Next()
	}
}

// DeviceLimitMiddleware checks device access limits
func DeviceLimitMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		_, exists := c.Get("userID")
		if !exists {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "user not authenticated"})
			return
		}

		deviceID, _ := c.Get("deviceID")
		platform, _ := c.Get("platform")

		// In a real implementation, this would check device limits
		// based on user subscription and current active devices
		
		// Log device access for monitoring
		if deviceID != nil && platform != nil {
			// This would be logged to a monitoring system
		}

		c.Next()
	}
}

// TrialEnforcementMiddleware enforces trial limitations
func TrialEnforcementMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		_, exists := c.Get("userID")
		if !exists {
			c.Next()
			return
		}

		// In a real implementation, this would:
		// 1. Check if user is on trial
		// 2. Verify trial hasn't expired
		// 3. Automatically downgrade expired trials
		// 4. Apply usage limitations

		c.Next()
	}
}
