package middleware

import (
	"bytes"
	"io"
	"strconv"
	"time"

	"github.com/azel-ko/final-ddd/internal/pkg/logger"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// APIStatsMiddleware API统计中间件
func APIStatsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		method := c.Request.Method

		// 记录请求体（如果需要）
		var requestBody []byte
		if c.Request.Body != nil {
			requestBody, _ = io.ReadAll(c.Request.Body)
			c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))
		}

		// 处理请求
		c.Next()

		// 计算响应时间
		duration := time.Since(start)
		statusCode := c.Writer.Status()

		// 记录API调用统计
		logger.Info("API call",
			zap.String("method", method),
			zap.String("path", path),
			zap.Int("status", statusCode),
			zap.Duration("duration", duration),
			zap.String("client_ip", c.ClientIP()),
			zap.String("user_agent", c.Request.UserAgent()),
		)

		// 如果是错误状态码，记录更多信息
		if statusCode >= 400 {
			logger.Error("API error",
				zap.String("method", method),
				zap.String("path", path),
				zap.Int("status", statusCode),
				zap.Duration("duration", duration),
				zap.String("client_ip", c.ClientIP()),
				zap.String("request_body", string(requestBody)),
			)
		}

		// 在实际项目中，这里应该将统计数据存储到数据库或缓存中
		// 例如：Redis中维护API调用计数、响应时间等统计信息
	}
}

// ErrorTrackingMiddleware 错误追踪中间件
func ErrorTrackingMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				// 记录panic错误
				logger.Error("Panic recovered",
					zap.Any("error", err),
					zap.String("method", c.Request.Method),
					zap.String("path", c.Request.URL.Path),
					zap.String("client_ip", c.ClientIP()),
				)

				// 返回500错误
				c.JSON(500, gin.H{
					"error": "Internal server error",
					"code":  "INTERNAL_ERROR",
				})
				c.Abort()
			}
		}()

		c.Next()

		// 检查是否有错误
		if len(c.Errors) > 0 {
			for _, err := range c.Errors {
				logger.Error("Request error",
					zap.Error(err.Err),
					zap.Uint("type", uint(err.Type)),
					zap.String("method", c.Request.Method),
					zap.String("path", c.Request.URL.Path),
					zap.String("client_ip", c.ClientIP()),
				)
			}
		}
	}
}

// PerformanceMonitoringMiddleware 性能监控中间件
func PerformanceMonitoringMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()

		c.Next()

		duration := time.Since(start)

		// 如果响应时间超过阈值，记录慢请求
		if duration > 2*time.Second {
			logger.Warn("Slow request detected",
				zap.String("method", c.Request.Method),
				zap.String("path", c.Request.URL.Path),
				zap.Duration("duration", duration),
				zap.Int("status", c.Writer.Status()),
				zap.String("client_ip", c.ClientIP()),
			)
		}

		// 记录响应大小
		responseSize := c.Writer.Size()
		if responseSize > 1024*1024 { // 1MB
			logger.Warn("Large response detected",
				zap.String("method", c.Request.Method),
				zap.String("path", c.Request.URL.Path),
				zap.String("response_size", strconv.Itoa(responseSize)),
			)
		}
	}
}

// SecurityMonitoringMiddleware 安全监控中间件
func SecurityMonitoringMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查可疑的请求模式
		userAgent := c.Request.UserAgent()
		clientIP := c.ClientIP()

		// 检查常见的攻击模式
		suspiciousPatterns := []string{
			"sqlmap", "nmap", "nikto", "dirb", "gobuster",
			"<script", "javascript:", "onload=", "onerror=",
			"../", "..\\", "/etc/passwd", "/proc/",
		}

		requestURI := c.Request.RequestURI
		for _, pattern := range suspiciousPatterns {
			if contains(requestURI, pattern) || contains(userAgent, pattern) {
				logger.Warn("Suspicious request detected",
					zap.String("pattern", pattern),
					zap.String("method", c.Request.Method),
					zap.String("path", c.Request.URL.Path),
					zap.String("client_ip", clientIP),
					zap.String("user_agent", userAgent),
				)
				break
			}
		}

		c.Next()
	}
}

// contains 检查字符串是否包含子字符串（忽略大小写）
func contains(s, substr string) bool {
	s = toLower(s)
	substr = toLower(substr)
	return len(s) >= len(substr) && indexIgnoreCase(s, substr) >= 0
}

// indexIgnoreCase 忽略大小写查找子字符串
func indexIgnoreCase(s, substr string) int {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return i
		}
	}
	return -1
}

// toLower 转换为小写
func toLower(s string) string {
	result := make([]byte, len(s))
	for i := 0; i < len(s); i++ {
		if s[i] >= 'A' && s[i] <= 'Z' {
			result[i] = s[i] + 32
		} else {
			result[i] = s[i]
		}
	}
	return string(result)
}
