package router

import (
	"net/http"

	"github.com/azel-ko/final-ddd/internal/application/services"
	"github.com/azel-ko/final-ddd/internal/domain/repository"
	"github.com/azel-ko/final-ddd/internal/infrastructure/amap"
	"github.com/azel-ko/final-ddd/internal/infrastructure/cache"
	"github.com/azel-ko/final-ddd/internal/infrastructure/monitoring"
	"github.com/azel-ko/final-ddd/internal/infrastructure/websocket"
	"github.com/azel-ko/final-ddd/internal/interfaces/http/handlers"
	"github.com/azel-ko/final-ddd/internal/interfaces/http/middleware"
	"github.com/azel-ko/final-ddd/internal/pkg/config"
	"github.com/azel-ko/final-ddd/pkg/auth"
	"github.com/gin-gonic/gin"
)

func Setup(cfg *config.Config, repo repository.Repository, redisCache *cache.RedisCache) *gin.Engine {
	jwtManager := auth.NewJWTManager(cfg.JWT.Key)

	// 映射应用环境到 Gin 模式
	var ginMode string
	switch cfg.App.Env {
	case "development", "dev":
		ginMode = gin.DebugMode
	case "production", "prod":
		ginMode = gin.ReleaseMode
	case "test":
		ginMode = gin.TestMode
	default:
		ginMode = gin.DebugMode // 默认使用调试模式
	}
	gin.SetMode(ginMode)
	r := gin.Default()
	r.Use(
		middleware.RateLimitMiddleware(),
		middleware.LoggerMiddleware(),
		middleware.ErrorHandler(),
		middleware.CORSMiddleware(),
		middleware.CacheControlMiddleware(),
		middleware.CompressionMiddleware(),
		middleware.SourceMiddleware(),
	)

	cacheService := services.NewCacheService(redisCache)
	sessionService := services.NewSessionService(repo, cacheService)
	authService := services.NewAuthService(repo, jwtManager, redisCache, sessionService)
	userService := services.NewUserService(repo, cacheService)

	// Create WebSocket hub for notifications
	wsHub := websocket.NewHub(&cfg.WebSocket)
	notificationService := services.NewNotificationService(wsHub, repo)

	// 启动 WebSocket Hub 和通知服务
	go wsHub.Run()
	notificationService.Start()

	// Create stats collector for monitoring
	statsCollector := monitoring.NewStatsCollector(redisCache)

	// Add enhanced monitoring middleware
	r.Use(
		middleware.EnhancedAPIStatsMiddleware(statsCollector),
		middleware.EnhancedErrorTrackingMiddleware(statsCollector),
		middleware.EnhancedPerformanceMonitoringMiddleware(statsCollector),
		middleware.DatabaseQueryMonitoringMiddleware(statsCollector),
	)

	checkpointService := services.NewCheckpointService(repo, cacheService, notificationService)
	subscriptionService := services.NewSubscriptionService(repo, cacheService, userService)
	permissionService := services.NewPermissionService(repo, cacheService, sessionService)
	monitoringService := services.NewMonitoringService(repo, cacheService, statsCollector)

	// Initialize Amap client with proper configuration
	amapClient := amap.NewClient(&cfg.Amap)
	routeService := services.NewRouteService(repo, cacheService, userService, checkpointService, amapClient)

	healthHandler := handlers.NewHealthHandler()

	authHandler := handlers.NewAuthHandler(authService)
	userHandler := handlers.NewUserHandler(userService)
	checkpointHandler := handlers.NewCheckpointHandler(checkpointService)
	routeHandler := handlers.NewRouteHandler(routeService)
	subscriptionHandler := handlers.NewSubscriptionHandler(subscriptionService)
	permissionHandler := handlers.NewPermissionHandler(permissionService)
	monitoringHandler := handlers.NewMonitoringHandler(monitoringService)

	// 创建 WebSocket 处理器
	wsHandler := handlers.NewWebSocketHandler(wsHub, notificationService)

	// 创建推送服务（暂时使用nil，后续可以实现）
	var pushService *services.PushService = nil
	adminHandler := handlers.NewAdminHandler(checkpointService, userService, pushService)

	// 健康检查路由
	r.GET("/api/health", healthHandler.Check)

	r.POST("/api/auth/login", authHandler.Login)
	r.POST("/api/auth/register", authHandler.Register)

	api := r.Group("/api")
	api.Use(middleware.AuthMiddleware(jwtManager))
	{
		users := api.Group("/users")
		{
			users.GET("/me", userHandler.GetSelf)                               // Get self profile
			users.PUT("/me", userHandler.UpdateSelf)                            // Update self profile
			users.GET("/subscription", userHandler.GetSubscription)             // Get subscription status
			users.POST("/subscribe", userHandler.Subscribe)                     // Upgrade subscription
			users.GET("/permissions", userHandler.GetPermissions)               // Get user permissions
			users.GET("/preferences", userHandler.GetPreferences)               // Get user preferences
			users.PUT("/preferences", userHandler.UpdatePreferences)            // Update user preferences
			users.GET("/trial-status", userHandler.GetTrialStatus)              // Get trial status
			users.GET("/sessions", userHandler.GetSessions)                     // Get user sessions
			users.DELETE("/sessions/:sessionId", userHandler.InvalidateSession) // Invalidate specific session
			users.DELETE("/sessions", userHandler.InvalidateAllSessions)        // Invalidate all sessions
			users.POST("/", userHandler.Create)                                 // Admin/System task, or initial user creation if not via /register
			users.GET("/:id", userHandler.Get)                                  // Admin/System task
			users.PUT("/:id", userHandler.Update)                               // Admin/System task
			users.DELETE("/:id", userHandler.Delete)                            // Admin/System task
		}

		checkpoints := api.Group("/checkpoints")
		{
			checkpoints.GET("/", checkpointHandler.GetCheckpointList)               // Get checkpoint list with filtering
			checkpoints.GET("/nearby", checkpointHandler.GetNearbyCheckpoints)      // Get nearby checkpoints
			checkpoints.GET("/:id", checkpointHandler.GetCheckpointDetail)          // Get checkpoint detail
			checkpoints.POST("/report", checkpointHandler.ReportCheckpoint)         // Report checkpoint status
			checkpoints.GET("/:id/reports", checkpointHandler.GetCheckpointReports) // Get checkpoint reports

			// Admin only endpoints
			checkpoints.POST("/", middleware.RequireAdmin(), checkpointHandler.CreateCheckpoint)      // Create checkpoint (Admin)
			checkpoints.PUT("/:id", middleware.RequireAdmin(), checkpointHandler.UpdateCheckpoint)    // Update checkpoint (Admin)
			checkpoints.DELETE("/:id", middleware.RequireAdmin(), checkpointHandler.DeleteCheckpoint) // Delete checkpoint (Admin)
		}

		routes := api.Group("/routes")
		{
			routes.POST("/plan", routeHandler.PlanRoute)          // Plan a route (requires navigation permission)
			routes.POST("/optimize", routeHandler.OptimizeRoute)  // Optimize route (Premium feature)
			routes.GET("/history", routeHandler.GetRouteHistory)  // Get route history (permission-based filtering)
			routes.GET("/:id", routeHandler.GetRouteDetail)       // Get route detail
			routes.GET("/:id/analyze", routeHandler.AnalyzeRoute) // Analyze route (Premium feature)
			routes.DELETE("/:id", routeHandler.DeleteRoute)       // Delete route from history
		}

		subscriptions := api.Group("/subscriptions")
		{
			subscriptions.GET("/plans", subscriptionHandler.GetAvailablePlans)        // Get available subscription plans (public)
			subscriptions.GET("/status", subscriptionHandler.GetSubscriptionStatus)   // Get user's subscription status
			subscriptions.POST("/purchase", subscriptionHandler.PurchaseSubscription) // Purchase subscription
			subscriptions.POST("/renew", subscriptionHandler.RenewSubscription)       // Renew subscription
			subscriptions.DELETE("/cancel", subscriptionHandler.CancelSubscription)   // Cancel subscription
			subscriptions.GET("/usage-stats", subscriptionHandler.GetUsageStats)      // Get usage statistics
		}

		permissions := api.Group("/permissions")
		{
			permissions.GET("/check", permissionHandler.CheckPermissions)          // Check user permissions
			permissions.POST("/validate", permissionHandler.ValidateFeature)       // Validate feature access
			permissions.POST("/validate-device", permissionHandler.ValidateDevice) // Validate device access
			permissions.POST("/enforce-trial", permissionHandler.EnforceTrial)     // Enforce trial limitations
		}

		// 管理员接口
		admin := api.Group("/admin")
		admin.Use(middleware.RequireAdmin()) // 需要管理员权限
		{
			admin.GET("/stats", adminHandler.GetSystemStats)                            // 获取系统统计
			admin.GET("/logs", adminHandler.GetSystemLogs)                              // 获取系统日志
			admin.GET("/api-stats", adminHandler.GetAPIStats)                           // 获取API统计
			admin.GET("/data-sources/status", adminHandler.GetDataSourceStatus)         // 获取数据源状态
			admin.POST("/data-sources/:source/refresh", adminHandler.RefreshDataSource) // 刷新数据源
		}

		monitoring := api.Group("/monitoring")
		{
			monitoring.GET("/system/stats", monitoringHandler.GetSystemStats)      // Get system statistics
			monitoring.GET("/api/stats", monitoringHandler.GetAPIStats)            // Get API statistics
			monitoring.GET("/logs", monitoringHandler.GetLogs)                     // Get system logs
			monitoring.GET("/errors", monitoringHandler.GetErrors)                 // Get error information
			monitoring.GET("/health", monitoringHandler.GetHealthCheck)            // Get health check
			monitoring.GET("/logs/export", monitoringHandler.ExportLogs)           // Export logs as CSV
			monitoring.GET("/errors/export", monitoringHandler.ExportErrors)       // Export errors as CSV
			monitoring.GET("/api/export", monitoringHandler.ExportAPIReport)       // Export API stats as CSV
			monitoring.GET("/config", monitoringHandler.GetMonitoringConfig)       // Get monitoring configuration
			monitoring.PUT("/config", monitoringHandler.UpdateMonitoringConfig)    // Update monitoring configuration
			monitoring.GET("/alerts", monitoringHandler.GetAlerts)                 // Get system alerts
			monitoring.POST("/alerts/:id/resolve", monitoringHandler.ResolveAlert) // Resolve alert
		}

		// WebSocket 和通知路由
		ws := api.Group("/ws")
		{
			ws.GET("/connect", wsHandler.HandleConnection)                              // WebSocket 连接
			ws.POST("/notifications/subscribe", wsHandler.SubscribeNotifications)       // 订阅通知
			ws.DELETE("/notifications/unsubscribe", wsHandler.UnsubscribeNotifications) // 取消订阅通知
			ws.GET("/notifications/subscription", wsHandler.GetSubscription)            // 获取订阅信息
		}
	}

	// 处理未找到的路由
	r.NoRoute(func(c *gin.Context) {
		c.JSON(http.StatusNotFound, gin.H{"code": "PAGE_NOT_FOUND", "message": "Page not found"})
	})
	return r
}
