package repository

import "github.com/azel-ko/final-ddd/internal/domain/entities"

type Repository interface {
	// User operations
	CreateUser(user *entities.User) error
	GetUser(id int) (*entities.User, error)
	UpdateUser(user *entities.User) error
	DeleteUser(id int) error
	GetUserByEmail(email string) (*entities.User, error)
	UpdateUserProfile(user *entities.User) error

	// Checkpoint operations
	CreateCheckpoint(checkpoint *entities.Checkpoint) error
	GetCheckpoint(id uint) (*entities.Checkpoint, error)
	UpdateCheckpoint(checkpoint *entities.Checkpoint) error
	DeleteCheckpoint(id uint) error
	ListCheckpoints(params CheckpointQueryParams) ([]*entities.Checkpoint, int64, error)
	GetNearbyCheckpoints(lat, lng float64, radius int) ([]*entities.Checkpoint, error)
	BatchCreateCheckpoints(checkpoints []*entities.Checkpoint) error
	BatchUpdateCheckpoints(checkpoints []*entities.Checkpoint) error

	// Checkpoint Report operations
	CreateCheckpointReport(report *entities.CheckpointReport) error
	GetCheckpointReports(checkpointID uint) ([]*entities.CheckpointReport, error)
	UpdateCheckpointReport(report *entities.CheckpointReport) error

	// Route operations
	CreateRoute(route *entities.Route) error
	GetUserRoutes(userID uint, limit int) ([]*entities.Route, error)
	DeleteRoute(id uint) error

	// User Preference operations
	GetUserPreference(userID uint) (*entities.UserPreference, error)
	CreateOrUpdateUserPreference(preference *entities.UserPreference) error

	// Data Update Log operations
	CreateDataUpdateLog(log *entities.DataUpdateLog) error
	GetLatestDataUpdateLog(source string) (*entities.DataUpdateLog, error)

	// Session operations
	CreateSession(session *entities.UserSession) error
	GetSession(sessionID string) (*entities.UserSession, error)
	UpdateSession(session *entities.UserSession) error
	DeleteSession(sessionID string) error
	GetUserSessions(userID uint) ([]*entities.UserSession, error)
	DeleteUserSessions(userID uint) error
	DeleteExpiredSessions() error

	// Push Device operations
	CreatePushDevice(device *entities.PushDevice) error
	GetPushDeviceByToken(token string) (*entities.PushDevice, error)
	GetUserPushDevices(userID uint) ([]*entities.PushDevice, error)
	UpdatePushDevice(device *entities.PushDevice) error
	DeletePushDevice(id uint) error
	DeleteInactivePushDevices(days int) error

	// Push Log operations
	CreatePushLog(log *entities.PushLog) error
	GetPushLogs(userID uint, limit int) ([]*entities.PushLog, error)
	GetPushStatistics(userID uint, days int) (*PushStatisticsData, error)
	DeleteOldPushLogs(days int) error

	// Push Template operations
	CreatePushTemplate(template *entities.PushTemplate) error
	GetPushTemplate(name string) (*entities.PushTemplate, error)
	GetPushTemplates() ([]*entities.PushTemplate, error)
	UpdatePushTemplate(template *entities.PushTemplate) error
	DeletePushTemplate(id uint) error

	// Push Subscription operations
	GetPushSubscription(userID uint) (*entities.PushSubscription, error)
	CreateOrUpdatePushSubscription(subscription *entities.PushSubscription) error
}

// CheckpointQueryParams 检查站查询参数
type CheckpointQueryParams struct {
	Province  string
	City      string
	District  string
	Status    string
	Type      string
	Page      int
	PageSize  int
	SortBy    string
	SortOrder string
}

// PushStatisticsData 推送统计数据
type PushStatisticsData struct {
	TotalSent      int `json:"total_sent"`
	TotalDelivered int `json:"total_delivered"`
	TotalFailed    int `json:"total_failed"`
	DeviceCount    int `json:"device_count"`
}
