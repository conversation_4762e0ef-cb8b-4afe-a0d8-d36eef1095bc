package entities

import (
	"time"
)

// UserSession represents a user session across different devices
type UserSession struct {
	ID        string    `gorm:"primarykey;size:255"`
	UserID    uint      `gorm:"not null;index"`
	DeviceID  string    `gorm:"size:255;not null"`
	Platform  string    `gorm:"size:50;not null"` // web, mobile, car, miniprogram
	IPAddress string    `gorm:"size:45"`          // IPv4 or IPv6
	UserAgent string    `gorm:"type:text"`
	IsActive  bool      `gorm:"default:true"`
	ExpiresAt time.Time `gorm:"not null"`
	CreatedAt time.Time `gorm:"autoCreateTime"`
	UpdatedAt time.Time `gorm:"autoUpdateTime"`
	LastUsedAt time.Time `gorm:"not null"`

	// Relationships
	User User `gorm:"foreignKey:UserID"`
}

// IsExpired checks if the session has expired
func (s *UserSession) IsExpired() bool {
	return time.Now().After(s.ExpiresAt)
}

// Refresh extends the session expiry time
func (s *UserSession) Refresh(duration time.Duration) {
	s.ExpiresAt = time.Now().Add(duration)
	s.LastUsedAt = time.Now()
}