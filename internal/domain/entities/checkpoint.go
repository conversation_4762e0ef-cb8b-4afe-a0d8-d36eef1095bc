package entities

import "time"

// Checkpoint 检查站实体
type Checkpoint struct {
	ID          uint      `gorm:"primarykey"`
	Name        string    `gorm:"size:255;not null;index"`                    // 检查站名称
	Location    string    `gorm:"size:500;not null"`                          // 详细位置描述
	Latitude    float64   `gorm:"type:decimal(10,8);not null;index"`          // 纬度
	Longitude   float64   `gorm:"type:decimal(11,8);not null;index"`          // 经度
	Province    string    `gorm:"size:50;not null;index"`                     // 省份
	City        string    `gorm:"size:50;not null;index"`                     // 城市
	District    string    `gorm:"size:50;index"`                              // 区县
	Road        string    `gorm:"size:255;index"`                             // 所在道路
	Direction   string    `gorm:"size:100"`                                   // 方向描述
	Status      string    `gorm:"size:20;not null;default:'unknown';index"`  // 状态: active, inactive, unknown
	Type        string    `gorm:"size:50;not null;default:'checkpoint'"`      // 类型: checkpoint, inspection, etc.
	Source      string    `gorm:"size:100;not null;default:'web'"`            // 数据来源
	Reliability int       `gorm:"default:50"`                                 // 可靠性评分 0-100
	LastSeen    *time.Time `gorm:"type:timestamp"`                           // 最后确认时间
	CreatedAt   time.Time `gorm:"autoCreateTime"`
	UpdatedAt   time.Time `gorm:"autoUpdateTime"`
}

// CheckpointReport 检查站举报记录
type CheckpointReport struct {
	ID           uint      `gorm:"primarykey"`
	CheckpointID uint      `gorm:"not null;index"`                    // 检查站ID
	UserID       uint      `gorm:"not null;index"`                    // 举报用户ID
	Status       string    `gorm:"size:20;not null"`                  // 举报状态: active, inactive, moved
	Description  string    `gorm:"type:text"`                         // 描述信息
	Latitude     float64   `gorm:"type:decimal(10,8)"`                // 举报位置纬度
	Longitude    float64   `gorm:"type:decimal(11,8)"`                // 举报位置经度
	Verified     bool      `gorm:"default:false"`                     // 是否已验证
	CreatedAt    time.Time `gorm:"autoCreateTime"`
	
	// 关联关系
	Checkpoint Checkpoint `gorm:"foreignKey:CheckpointID"`
	User       User       `gorm:"foreignKey:UserID"`
}

// Route 路线记录
type Route struct {
	ID            uint      `gorm:"primarykey"`
	UserID        uint      `gorm:"not null;index"`                    // 用户ID
	StartLat      float64   `gorm:"type:decimal(10,8);not null"`       // 起点纬度
	StartLng      float64   `gorm:"type:decimal(11,8);not null"`       // 起点经度
	EndLat        float64   `gorm:"type:decimal(10,8);not null"`       // 终点纬度
	EndLng        float64   `gorm:"type:decimal(11,8);not null"`       // 终点经度
	StartAddress  string    `gorm:"size:500"`                          // 起点地址
	EndAddress    string    `gorm:"size:500"`                          // 终点地址
	Distance      int       `gorm:"not null"`                          // 距离(米)
	Duration      int       `gorm:"not null"`                          // 预计时间(秒)
	RouteData     string    `gorm:"type:text"`                         // 路线详细数据(JSON)
	AvoidLevel    int       `gorm:"default:1"`                         // 避让级别 1-3
	CheckpointIDs string    `gorm:"type:text"`                         // 涉及的检查站ID列表(JSON)
	CreatedAt     time.Time `gorm:"autoCreateTime"`
	
	// 关联关系
	User User `gorm:"foreignKey:UserID"`
}

// UserPreference 用户偏好设置
type UserPreference struct {
	ID                  uint      `gorm:"primarykey"`
	UserID              uint      `gorm:"not null;unique;index"`         // 用户ID
	DefaultAvoidLevel   int       `gorm:"default:1"`                     // 默认避让级别
	NotificationTypes   string    `gorm:"type:text"`                     // 通知类型(JSON数组)
	AutoUpdate          bool      `gorm:"default:true"`                  // 自动更新检查站信息
	VoiceNavigation     bool      `gorm:"default:true"`                  // 语音导航
	NightMode           bool      `gorm:"default:false"`                 // 夜间模式
	MapStyle            string    `gorm:"size:50;default:'standard'"`    // 地图样式
	UpdatedAt           time.Time `gorm:"autoUpdateTime"`
	
	// 关联关系
	User User `gorm:"foreignKey:UserID"`
}

// DataUpdateLog 数据更新日志
type DataUpdateLog struct {
	ID          uint      `gorm:"primarykey"`
	Source      string    `gorm:"size:100;not null;index"`           // 数据源
	Type        string    `gorm:"size:50;not null"`                  // 更新类型
	Status      string    `gorm:"size:20;not null"`                  // 状态: success, failed, partial
	RecordsAdded int      `gorm:"default:0"`                         // 新增记录数
	RecordsUpdated int    `gorm:"default:0"`                         // 更新记录数
	RecordsDeleted int    `gorm:"default:0"`                         // 删除记录数
	ErrorMessage string   `gorm:"type:text"`                         // 错误信息
	Duration     int      `gorm:"default:0"`                         // 执行时间(毫秒)
	CreatedAt    time.Time `gorm:"autoCreateTime"`
}