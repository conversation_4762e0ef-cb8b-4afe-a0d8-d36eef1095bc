package entities

import (
	"time"
)

// PushDevice 推送设备信息
type PushDevice struct {
	ID         uint      `json:"id" gorm:"primaryKey;autoIncrement"`
	UserID     uint      `json:"user_id" gorm:"not null;index"`
	DeviceType string    `json:"device_type" gorm:"not null"` // ios, android, miniprogram
	Token      string    `json:"token" gorm:"not null;unique;index"`
	Tags       string    `json:"tags" gorm:"type:text"`       // JSON格式的标签数组
	IsActive   bool      `json:"is_active" gorm:"default:true"`
	LastUsed   time.Time `json:"last_used"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
	
	// 关联关系
	User *User `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// PushLog 推送日志记录
type PushLog struct {
	ID          uint      `json:"id" gorm:"primaryKey;autoIncrement"`
	UserID      uint      `json:"user_id" gorm:"not null;index"`
	DeviceToken string    `json:"device_token" gorm:"not null;index"`
	Title       string    `json:"title" gorm:"not null"`
	Body        string    `json:"body" gorm:"not null"`
	Data        string    `json:"data" gorm:"type:text"`        // JSON格式的数据
	Success     bool      `json:"success" gorm:"default:false"`
	Error       string    `json:"error" gorm:"type:text"`
	MessageID   string    `json:"message_id"`
	Platform    string    `json:"platform"`                     // firebase, apns
	CreatedAt   time.Time `json:"created_at"`
	
	// 关联关系
	User *User `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// PushTemplate 推送模板
type PushTemplate struct {
	ID          uint      `json:"id" gorm:"primaryKey;autoIncrement"`
	Name        string    `json:"name" gorm:"not null;unique"`
	Type        string    `json:"type" gorm:"not null"`         // checkpoint_update, route_change, system_alert
	Title       string    `json:"title" gorm:"not null"`
	Body        string    `json:"body" gorm:"not null"`
	Data        string    `json:"data" gorm:"type:text"`        // JSON格式的默认数据
	IsActive    bool      `json:"is_active" gorm:"default:true"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// PushSubscription 推送订阅设置
type PushSubscription struct {
	ID                uint      `json:"id" gorm:"primaryKey;autoIncrement"`
	UserID            uint      `json:"user_id" gorm:"not null;unique;index"`
	CheckpointUpdates bool      `json:"checkpoint_updates" gorm:"default:true"`
	RouteChanges      bool      `json:"route_changes" gorm:"default:true"`
	SystemAlerts      bool      `json:"system_alerts" gorm:"default:true"`
	QuietHours        string    `json:"quiet_hours" gorm:"type:text"`     // JSON格式的静默时间设置
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
	
	// 关联关系
	User *User `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// TableName 指定表名
func (PushDevice) TableName() string {
	return "push_devices"
}

func (PushLog) TableName() string {
	return "push_logs"
}

func (PushTemplate) TableName() string {
	return "push_templates"
}

func (PushSubscription) TableName() string {
	return "push_subscriptions"
}