package entities

import "time"

type User struct {
	ID            uint      `gorm:"primarykey"`
	Name          string    `gorm:"size:255;not null"`
	Email         string    `gorm:"size:255;not null;unique"`
	Password      string    `gorm:"size:255;not null"`
	Role          string    `gorm:"size:255;not null;default:'user'"`
	CarPlate      string    `gorm:"size:20"`
	PlateRegion   string    `gorm:"size:10"`
	Subscription  string    `gorm:"size:20;not null;default:'trial'"` // trial, free, premium
	TrialExpiry   *time.Time `gorm:"type:timestamp"`
	Preferences   string    `gorm:"type:text"` // JSON string for user preferences
	CreatedAt     time.Time `gorm:"autoCreateTime"`
	UpdatedAt     time.Time `gorm:"autoUpdateTime"`
	LastLoginAt   *time.Time `gorm:"type:timestamp"`
}
