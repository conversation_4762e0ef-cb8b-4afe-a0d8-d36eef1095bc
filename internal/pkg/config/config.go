package config

import (
	"fmt"
	"os"
	"regexp"
	"strconv"
	"strings"

	"github.com/spf13/viper"
)

// Config 总配置结构
type Config struct {
	App          App                `mapstructure:"app"`
	Database     DatabaseConfig     `mapstructure:"database"`
	JWT          JWTConfig          `mapstructure:"jwt"`
	Redis        RedisConfig        `mapstructure:"redis"`
	Log          LogConfig          `mapstructure:"log"`
	Amap         AmapConfig         `mapstructure:"amap"`
	DataSources  DataSourcesConfig  `mapstructure:"data_sources"`
	Scheduler    SchedulerConfig    `mapstructure:"scheduler"`
	WebSocket    WebSocketConfig    `mapstructure:"websocket"`
	Push         PushConfig         `mapstructure:"push"`
	Subscription SubscriptionConfig `mapstructure:"subscription"`
	Monitoring   MonitoringConfig   `mapstructure:"monitoring"`
}

// App 应用配置
type App struct {
	Name string `mapstructure:"name"`
	Port int    `mapstructure:"port"`
	Env  string `mapstructure:"env"`
}

// DatabaseConfig 数据库配置 - PostgreSQL 优先
type DatabaseConfig struct {
	// PostgreSQL 主配置（默认和推荐）
	URL      string             `mapstructure:"url"`      // PostgreSQL 连接字符串
	Host     string             `mapstructure:"host"`     // PostgreSQL 主机
	Port     int                `mapstructure:"port"`     // PostgreSQL 端口
	Name     string             `mapstructure:"name"`     // 数据库名
	User     string             `mapstructure:"user"`     // 用户名
	Password string             `mapstructure:"password"` // 密码
	SSLMode  string             `mapstructure:"ssl_mode"` // SSL 模式
	Pool     DatabasePoolConfig `mapstructure:"pool"`     // 连接池配置

	// 向后兼容的类型字段（已弃用，但保留以支持旧配置）
	Type string `mapstructure:"type"` // 已弃用：使用 URL 或具体字段
	Path string `mapstructure:"path"` // 已弃用：仅用于 SQLite 兼容

	// 备用数据库配置（可选，用于测试或特殊场景）
	Fallback map[string]string `mapstructure:"fallback,omitempty"`
}

// DatabasePoolConfig 数据库连接池配置
type DatabasePoolConfig struct {
	MaxOpen     int    `mapstructure:"max_open"`     // 最大打开连接数
	MaxIdle     int    `mapstructure:"max_idle"`     // 最大空闲连接数
	MaxLifetime string `mapstructure:"max_lifetime"` // 连接最大生存时间
}

// JWTConfig JWT配置
type JWTConfig struct {
	Key string `mapstructure:"key"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Password string `mapstructure:"password"`
	DB       int    `mapstructure:"db"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level  string `mapstructure:"level"`
	Format string `mapstructure:"format"`
}

var AppConfig Config

func Load() (*Config, error) {
	return LoadWithEnv("")
}

// LoadWithEnv 加载指定环境的配置
func LoadWithEnv(env string) (*Config, error) {
	// 设置基础配置
	viper.SetConfigName("config") // 使用统一的配置文件
	viper.SetConfigType("yaml")
	viper.AddConfigPath("configs")
	viper.AutomaticEnv()

	// 启用环境变量替换
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	// 手动设置一些默认值以避免解析错误
	viper.SetDefault("database.pool.max_open", 25)
	viper.SetDefault("database.pool.max_idle", 5)
	viper.SetDefault("database.port", 5432)
	viper.SetDefault("monitoring.metrics.enabled", true)
	viper.SetDefault("monitoring.metrics.port", 9090)
	viper.SetDefault("monitoring.loki.enabled", false)
	viper.SetDefault("monitoring.tracing.enabled", false)

	// 设置环境变量前缀
	viper.SetEnvPrefix("") // 不使用前缀

	// 读取并处理配置文件
	configPath := "configs/config.yml"

	// 读取配置文件内容
	content, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	// 处理环境变量占位符
	processedContent := expandEnvVars(string(content))

	// 创建临时文件
	tmpFile, err := os.CreateTemp("", "config-*.yml")
	if err != nil {
		return nil, fmt.Errorf("failed to create temp config file: %w", err)
	}
	defer os.Remove(tmpFile.Name())
	defer tmpFile.Close()

	// 写入处理后的内容
	if _, err := tmpFile.WriteString(processedContent); err != nil {
		return nil, fmt.Errorf("failed to write temp config file: %w", err)
	}
	tmpFile.Close()

	// 使用处理后的配置文件
	viper.SetConfigFile(tmpFile.Name())
	if err := viper.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("failed to read processed config: %w", err)
	}

	// 调试信息：显示实际加载的配置文件（仅在调试模式下）
	if os.Getenv("LOG_LEVEL") == "debug" {
		fmt.Printf("Loaded config file: %s\n", configPath)
	}

	// 解析配置到结构体
	if err := viper.Unmarshal(&AppConfig); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// 应用环境变量覆盖
	applyEnvironmentOverrides(&AppConfig)

	// 验证数据库配置
	if err := AppConfig.Database.ValidateConfig(); err != nil {
		return nil, fmt.Errorf("invalid database config: %w", err)
	}

	return &AppConfig, nil
}

// applyEnvironmentOverrides 应用环境变量覆盖
func applyEnvironmentOverrides(cfg *Config) {
	// 数据库配置覆盖
	if host := os.Getenv("DB_HOST"); host != "" {
		cfg.Database.Host = host
	}
	if port := os.Getenv("DB_PORT"); port != "" {
		if p, err := strconv.Atoi(port); err == nil {
			cfg.Database.Port = p
		}
	}
	if name := os.Getenv("DB_NAME"); name != "" {
		cfg.Database.Name = name
	}
	if user := os.Getenv("DB_USER"); user != "" {
		cfg.Database.User = user
	}
	if password := os.Getenv("DB_PASSWORD"); password != "" {
		cfg.Database.Password = password
	}
	if sslMode := os.Getenv("DB_SSL_MODE"); sslMode != "" {
		cfg.Database.SSLMode = sslMode
	}
	if dbURL := os.Getenv("DATABASE_URL"); dbURL != "" {
		cfg.Database.URL = dbURL
	}

	// JWT配置覆盖
	if jwtSecret := os.Getenv("JWT_SECRET"); jwtSecret != "" {
		cfg.JWT.Key = jwtSecret
	}

	// Redis配置覆盖
	if redisHost := os.Getenv("REDIS_HOST"); redisHost != "" {
		cfg.Redis.Host = redisHost
	}
	if redisPort := os.Getenv("REDIS_PORT"); redisPort != "" {
		if p, err := strconv.Atoi(redisPort); err == nil {
			cfg.Redis.Port = p
		}
	}
	if redisPassword := os.Getenv("REDIS_PASSWORD"); redisPassword != "" {
		cfg.Redis.Password = redisPassword
	}

	// 应用配置覆盖
	if appEnv := os.Getenv("APP_ENV"); appEnv != "" {
		cfg.App.Env = appEnv
	}
	if appPort := os.Getenv("APP_PORT"); appPort != "" {
		if p, err := strconv.Atoi(appPort); err == nil {
			cfg.App.Port = p
		}
	}

	// 日志配置覆盖
	if logLevel := os.Getenv("LOG_LEVEL"); logLevel != "" {
		cfg.Log.Level = logLevel
	}
	if logFormat := os.Getenv("LOG_FORMAT"); logFormat != "" {
		cfg.Log.Format = logFormat
	}
}

// mergeConfigFile 合并环境特定的配置文件
func mergeConfigFile(configPath string) error {
	// 读取配置文件内容
	content, err := os.ReadFile(configPath)
	if err != nil {
		return err
	}

	// 处理环境变量占位符
	processedContent := expandEnvVars(string(content))

	// 创建临时文件
	tmpFile, err := os.CreateTemp("", "config-*.yml")
	if err != nil {
		return err
	}
	defer os.Remove(tmpFile.Name())
	defer tmpFile.Close()

	// 写入处理后的内容
	if _, err := tmpFile.WriteString(processedContent); err != nil {
		return err
	}
	tmpFile.Close()

	// 使用处理后的配置文件
	envViper := viper.New()
	envViper.SetConfigFile(tmpFile.Name())
	envViper.AutomaticEnv()

	if err := envViper.ReadInConfig(); err != nil {
		return err
	}

	// 合并配置
	return viper.MergeConfigMap(envViper.AllSettings())
}

// expandEnvVars 处理 ${VAR:default} 格式的环境变量占位符
func expandEnvVars(content string) string {
	// 使用正则表达式匹配 ${VAR:default} 格式
	re := regexp.MustCompile(`\$\{([^}:]+):([^}]*)\}`)

	return re.ReplaceAllStringFunc(content, func(match string) string {
		// 提取变量名和默认值
		parts := re.FindStringSubmatch(match)
		if len(parts) != 3 {
			return match
		}

		varName := parts[1]
		defaultValue := parts[2]

		// 获取环境变量值，如果不存在则使用默认值
		if value := os.Getenv(varName); value != "" {
			return value
		}
		return defaultValue
	})
}

func (cfg *Config) GetServerAddress() string { return fmt.Sprintf(":%d", cfg.App.Port) }

// GetDatabaseURL 获取数据库连接字符串 - PostgreSQL 优先
func (cfg *DatabaseConfig) GetDatabaseURL() string {
	// 优先使用直接配置的 URL
	if cfg.URL != "" {
		return cfg.URL
	}

	// 如果没有 URL，构建 PostgreSQL 连接字符串（默认行为）
	if cfg.Host != "" && cfg.Name != "" {
		sslMode := cfg.SSLMode
		if sslMode == "" {
			sslMode = "disable" // 开发环境默认
		}

		return fmt.Sprintf("postgresql://%s:%s@%s:%d/%s?sslmode=%s",
			cfg.User, cfg.Password, cfg.Host, cfg.Port, cfg.Name, sslMode)
	}

	// 向后兼容：检查旧的 type 字段
	if cfg.Type != "" {
		switch cfg.Type {
		case "postgres", "postgresql":
			return fmt.Sprintf("postgresql://%s:%s@%s:%d/%s?sslmode=disable",
				cfg.User, cfg.Password, cfg.Host, cfg.Port, cfg.Name)
		case "mysql":
			return fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
				cfg.User, cfg.Password, cfg.Host, cfg.Port, cfg.Name)
		case "sqlite":
			if cfg.Path != "" {
				return cfg.Path
			}
			return "./data/app.db"
		}
	}

	// 默认返回 PostgreSQL 本地连接
	return "postgresql://postgres:password@localhost:5432/final_ddd?sslmode=disable"
}

// IsPostgreSQL 检查是否使用 PostgreSQL
func (cfg *DatabaseConfig) IsPostgreSQL() bool {
	if cfg.URL != "" {
		return strings.HasPrefix(cfg.URL, "postgresql://") || strings.HasPrefix(cfg.URL, "postgres://")
	}

	// 默认假设是 PostgreSQL
	return cfg.Type == "" || cfg.Type == "postgres" || cfg.Type == "postgresql"
}

// GetDriverName 获取数据库驱动名称
func (cfg *DatabaseConfig) GetDriverName() string {
	if cfg.IsPostgreSQL() {
		return "postgres"
	}

	if cfg.Type == "mysql" {
		return "mysql"
	}

	if cfg.Type == "sqlite" {
		return "sqlite3"
	}

	// 默认返回 PostgreSQL
	return "postgres"
}

// ValidateConfig 验证配置
func (cfg *DatabaseConfig) ValidateConfig() error {
	// 如果有 URL，优先验证 URL
	if cfg.URL != "" {
		if !strings.Contains(cfg.URL, "://") {
			return fmt.Errorf("invalid database URL format")
		}
		return nil
	}

	// 验证基本字段
	if cfg.Host == "" {
		return fmt.Errorf("database host is required")
	}

	if cfg.Name == "" {
		return fmt.Errorf("database name is required")
	}

	if cfg.User == "" {
		return fmt.Errorf("database user is required")
	}

	return nil
}

// AmapConfig 高德地图API配置
type AmapConfig struct {
	APIKey        string          `mapstructure:"api_key"`
	WebServiceKey string          `mapstructure:"web_service_key"`
	BaseURL       string          `mapstructure:"base_url"`
	Timeout       string          `mapstructure:"timeout"`
	RateLimit     RateLimitConfig `mapstructure:"rate_limit"`
}

// RateLimitConfig 限流配置
type RateLimitConfig struct {
	RequestsPerSecond int `mapstructure:"requests_per_second"`
	Burst             int `mapstructure:"burst"`
}

// DataSourcesConfig 数据源配置
type DataSourcesConfig struct {
	Checkpoint     CheckpointConfig `mapstructure:"checkpoint"`
	UpdateInterval string           `mapstructure:"update_interval"`
	Cache          CacheConfig      `mapstructure:"cache"`
}

// CheckpointConfig 检查站数据源配置
type CheckpointConfig struct {
	URL        string `mapstructure:"url"`
	Timeout    string `mapstructure:"timeout"`
	RetryCount int    `mapstructure:"retry_count"`
	RetryDelay string `mapstructure:"retry_delay"`
	UserAgent  string `mapstructure:"user_agent"`
}

// CacheConfig 缓存配置
type CacheConfig struct {
	TTL     string `mapstructure:"ttl"`
	MaxSize int    `mapstructure:"max_size"`
}

// SchedulerConfig 定时任务配置
type SchedulerConfig struct {
	Enabled  bool                 `mapstructure:"enabled"`
	Timezone string               `mapstructure:"timezone"`
	Jobs     map[string]JobConfig `mapstructure:"jobs"`
}

// JobConfig 任务配置
type JobConfig struct {
	Enabled bool   `mapstructure:"enabled"`
	Cron    string `mapstructure:"cron"`
	Timeout string `mapstructure:"timeout"`
}

// WebSocketConfig WebSocket配置
type WebSocketConfig struct {
	Enabled         bool   `mapstructure:"enabled"`
	Port            int    `mapstructure:"port"`
	Path            string `mapstructure:"path"`
	MaxConnections  int    `mapstructure:"max_connections"`
	ReadBufferSize  int    `mapstructure:"read_buffer_size"`
	WriteBufferSize int    `mapstructure:"write_buffer_size"`
	PingPeriod      string `mapstructure:"ping_period"`
	PongWait        string `mapstructure:"pong_wait"`
	WriteWait       string `mapstructure:"write_wait"`
	MaxMessageSize  int    `mapstructure:"max_message_size"`
}

// PushConfig 推送通知配置
type PushConfig struct {
	Firebase    FirebaseConfig    `mapstructure:"firebase"`
	APNS        APNSConfig        `mapstructure:"apns"`
	Miniprogram MiniprogramConfig `mapstructure:"miniprogram"`
}

// MiniprogramConfig 小程序推送配置
type MiniprogramConfig struct {
	Enabled   bool   `mapstructure:"enabled"`
	AppID     string `mapstructure:"app_id"`
	AppSecret string `mapstructure:"app_secret"`
}

// FirebaseConfig Firebase配置
type FirebaseConfig struct {
	Enabled         bool   `mapstructure:"enabled"`
	CredentialsFile string `mapstructure:"credentials_file"`
}

// APNSConfig APNs配置
type APNSConfig struct {
	Enabled    bool   `mapstructure:"enabled"`
	KeyFile    string `mapstructure:"key_file"`
	KeyID      string `mapstructure:"key_id"`
	TeamID     string `mapstructure:"team_id"`
	BundleID   string `mapstructure:"bundle_id"`
	Production bool   `mapstructure:"production"`
}

// SubscriptionConfig 订阅和权限配置
type SubscriptionConfig struct {
	Trial    TrialConfig         `mapstructure:"trial"`
	Features map[string][]string `mapstructure:"features"`
}

// TrialConfig 试用期配置
type TrialConfig struct {
	Duration string `mapstructure:"duration"`
}

// MonitoringConfig 监控配置
type MonitoringConfig struct {
	Metrics MetricsConfig `mapstructure:"metrics"`
	Loki    LokiConfig    `mapstructure:"loki"`
	Tracing TracingConfig `mapstructure:"tracing"`
}

// MetricsConfig Prometheus指标配置
type MetricsConfig struct {
	Enabled bool   `mapstructure:"enabled"`
	Port    int    `mapstructure:"port"`
	Path    string `mapstructure:"path"`
}

// LokiConfig Loki日志配置
type LokiConfig struct {
	Enabled  bool   `mapstructure:"enabled"`
	Endpoint string `mapstructure:"endpoint"`
}

// TracingConfig 链路追踪配置
type TracingConfig struct {
	Enabled        bool   `mapstructure:"enabled"`
	JaegerEndpoint string `mapstructure:"jaeger_endpoint"`
}
