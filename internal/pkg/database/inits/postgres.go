package inits

import (
	"database/sql"
	"fmt"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/azel-ko/final-ddd/internal/pkg/config"
	_ "github.com/jackc/pgx/v5/stdlib" // PostgreSQL driver
)

// PostgreSQLInitializer 实现了 DatabaseInitializer 接口，用于 PostgreSQL 初始化
type PostgreSQLInitializer struct {
	config *config.Config
}

func (p *PostgreSQLInitializer) Initialize() error {
	// 解析数据库连接参数
	dbParams, err := p.parseDatabaseParams()
	if err != nil {
		return fmt.Errorf("解析数据库连接参数失败: %v", err)
	}

	// 构建包含数据库名称的DSN用于初始连接
	initialDSN := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		dbParams.Host, dbParams.Port, dbParams.User, dbParams.Password, dbParams.Name, dbParams.SSLMode)

	// 尝试连接到 PostgreSQL 服务器（包含特定数据库）
	db, err := sql.Open("pgx", initialDSN)
	if err != nil {
		return fmt.Errorf("无法打开 PostgreSQL 数据库连接: %v", err)
	}
	defer db.Close()

	// 设置连接超时
	db.SetConnMaxLifetime(30 * time.Second)
	db.SetMaxOpenConns(5)
	db.SetMaxIdleConns(2)

	// 测试连接是否成功，增加重试机制
	var pingErr error
	for i := 0; i < 5; i++ {
		pingErr = db.Ping()
		if pingErr == nil {
			break
		}
		fmt.Printf("数据库连接尝试 %d/5 失败: %v\n", i+1, pingErr)
		time.Sleep(2 * time.Second)
	}

	if pingErr != nil {
		return fmt.Errorf("无法连接到 PostgreSQL 数据库服务器: %v", pingErr)
	}

	// 检测数据库是否存在
	var exists bool
	err = db.QueryRow("SELECT EXISTS(SELECT 1 FROM pg_catalog.pg_database WHERE datname=$1)",
		dbParams.Name).Scan(&exists)
	if err != nil {
		return fmt.Errorf("查询 PostgreSQL 数据库存在性失败: %v", err)
	}

	if !exists {
		fmt.Printf("PostgreSQL 数据库 %s 不存在，正在创建...\n", dbParams.Name)
		_, err = db.Exec(fmt.Sprintf("CREATE DATABASE %s", dbParams.Name))
		if err != nil {
			return fmt.Errorf("创建 PostgreSQL 数据库失败: %v", err)
		}
		fmt.Printf("PostgreSQL 数据库 %s 创建成功\n", dbParams.Name)
	} else {
		fmt.Printf("PostgreSQL 数据库 %s 已存在\n", dbParams.Name)
	}

	return nil
}

// DatabaseParams 数据库连接参数
type DatabaseParams struct {
	Host     string
	Port     int
	User     string
	Password string
	Name     string
	SSLMode  string
}

// parseDatabaseParams 解析数据库连接参数，支持 DATABASE_URL 和分离字段配置
func (p *PostgreSQLInitializer) parseDatabaseParams() (*DatabaseParams, error) {
	params := &DatabaseParams{}

	// 如果配置了 DATABASE_URL，优先解析 URL
	if p.config.Database.URL != "" {
		parsedURL, err := url.Parse(p.config.Database.URL)
		if err != nil {
			return nil, fmt.Errorf("解析 DATABASE_URL 失败: %v", err)
		}

		// 提取主机和端口
		params.Host = parsedURL.Hostname()
		if parsedURL.Port() != "" {
			port, err := strconv.Atoi(parsedURL.Port())
			if err != nil {
				return nil, fmt.Errorf("解析端口失败: %v", err)
			}
			params.Port = port
		} else {
			params.Port = 5432 // PostgreSQL 默认端口
		}

		// 提取用户名和密码
		if parsedURL.User != nil {
			params.User = parsedURL.User.Username()
			if password, ok := parsedURL.User.Password(); ok {
				params.Password = password
			}
		}

		// 提取数据库名（去掉前导斜杠）
		params.Name = strings.TrimPrefix(parsedURL.Path, "/")

		// 提取 SSL 模式
		queryParams := parsedURL.Query()
		if sslMode := queryParams.Get("sslmode"); sslMode != "" {
			params.SSLMode = sslMode
		} else {
			params.SSLMode = "disable" // 默认值
		}
	} else {
		// 使用分离字段配置
		params.Host = p.config.Database.Host
		params.Port = p.config.Database.Port
		params.User = p.config.Database.User
		params.Password = p.config.Database.Password
		params.Name = p.config.Database.Name
		params.SSLMode = p.config.Database.SSLMode
		if params.SSLMode == "" {
			params.SSLMode = "disable"
		}
	}

	// 验证必需参数
	if params.Host == "" {
		return nil, fmt.Errorf("数据库主机地址不能为空")
	}
	if params.Port == 0 {
		params.Port = 5432 // PostgreSQL 默认端口
	}
	if params.User == "" {
		return nil, fmt.Errorf("数据库用户名不能为空")
	}
	if params.Name == "" {
		return nil, fmt.Errorf("数据库名称不能为空")
	}

	return params, nil
}
