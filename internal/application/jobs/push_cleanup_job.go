package jobs

import (
	"context"
	"time"

	"github.com/azel-ko/final-ddd/internal/application/services"
	"github.com/azel-ko/final-ddd/internal/pkg/logger"
	"go.uber.org/zap"
)

// PushCleanupJob 推送数据清理任务
type PushCleanupJob struct {
	pushService *services.PushService
}

// NewPushCleanupJob 创建推送数据清理任务
func NewPushCleanupJob(pushService *services.PushService) *PushCleanupJob {
	return &PushCleanupJob{
		pushService: pushService,
	}
}

// Execute 执行推送数据清理任务
func (j *PushCleanupJob) Execute(ctx context.Context) error {
	logger.Info("Starting push data cleanup job")

	// 清理30天前的推送日志
	if err := j.pushService.CleanupOldPushLogs(ctx, 30); err != nil {
		logger.Error("Failed to cleanup old push logs", zap.Error(err))
		return err
	}
	logger.Info("Cleaned up old push logs (30+ days)")

	// 清理7天未使用的不活跃设备
	if err := j.pushService.CleanupInactiveDevices(ctx, 7); err != nil {
		logger.Error("Failed to cleanup inactive devices", zap.Error(err))
		return err
	}
	logger.Info("Cleaned up inactive push devices (7+ days)")

	logger.Info("Push data cleanup job completed successfully")
	return nil
}

// Name 返回任务名称（实现scheduler.Job接口）
func (j *PushCleanupJob) Name() string {
	return "PushCleanupJob"
}

// GetName 获取任务名称
func (j *PushCleanupJob) GetName() string {
	return "push_cleanup"
}

// GetDescription 获取任务描述
func (j *PushCleanupJob) GetDescription() string {
	return "清理过期的推送日志和不活跃的设备token"
}

// GetTimeout 获取任务超时时间
func (j *PushCleanupJob) GetTimeout() time.Duration {
	return 5 * time.Minute
}
