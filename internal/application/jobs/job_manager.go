package jobs

import (
	"context"
	"fmt"

	"github.com/azel-ko/final-ddd/internal/application/services"
	"github.com/azel-ko/final-ddd/internal/domain/repository"
	"github.com/azel-ko/final-ddd/internal/infrastructure/crawler"
	"github.com/azel-ko/final-ddd/internal/infrastructure/scheduler"
	"github.com/azel-ko/final-ddd/internal/pkg/config"
	"github.com/azel-ko/final-ddd/internal/pkg/logger"
)

// JobManager 任务管理器
type JobManager struct {
	scheduler    *scheduler.Scheduler
	repo         repository.Repository
	cacheService *services.CacheService
	pushService  *services.PushService
	config       *config.Config
}

// NewJobManager 创建任务管理器
func NewJobManager(
	scheduler *scheduler.Scheduler,
	repo repository.Repository,
	cacheService *services.CacheService,
	pushService *services.PushService,
	config *config.Config,
) *JobManager {
	return &JobManager{
		scheduler:    scheduler,
		repo:         repo,
		cacheService: cacheService,
		pushService:  pushService,
		config:       config,
	}
}

// RegisterJobs 注册所有任务
func (jm *JobManager) RegisterJobs() error {
	logger.Info("Registering scheduled jobs")

	// 1. 注册检查站数据更新任务
	if err := jm.registerCheckpointUpdateJob(); err != nil {
		return fmt.Errorf("failed to register checkpoint update job: %w", err)
	}

	// 2. 注册数据清理任务
	if err := jm.registerDataCleanupJob(); err != nil {
		return fmt.Errorf("failed to register data cleanup job: %w", err)
	}

	// 3. 注册试用期检查任务
	if err := jm.registerTrialCheckJob(); err != nil {
		return fmt.Errorf("failed to register trial check job: %w", err)
	}

	// 4. 注册推送数据清理任务
	if err := jm.registerPushCleanupJob(); err != nil {
		return fmt.Errorf("failed to register push cleanup job: %w", err)
	}

	logger.Info("All jobs registered successfully")
	return nil
}

// registerCheckpointUpdateJob 注册检查站数据更新任务
func (jm *JobManager) registerCheckpointUpdateJob() error {
	jobConfig, exists := jm.config.Scheduler.Jobs["checkpoint_update"]
	if !exists || !jobConfig.Enabled {
		logger.Info("Checkpoint update job is disabled")
		return nil
	}

	// 创建爬虫
	crawler := crawler.NewCheckpointCrawler(&jm.config.DataSources.Checkpoint)

	// 创建检查站服务
	checkpointService := services.NewCheckpointService(jm.repo, jm.cacheService, nil)

	// 创建任务
	job := NewCheckpointUpdateJob(
		crawler,
		jm.repo,
		jm.cacheService,
		checkpointService,
		&jm.config.DataSources,
	)

	// 注册到调度器
	return jm.scheduler.AddJob("checkpoint_update", job, jobConfig.Cron, jobConfig)
}

// registerDataCleanupJob 注册数据清理任务
func (jm *JobManager) registerDataCleanupJob() error {
	jobConfig, exists := jm.config.Scheduler.Jobs["data_cleanup"]
	if !exists || !jobConfig.Enabled {
		logger.Info("Data cleanup job is disabled")
		return nil
	}

	// 创建任务
	job := NewDataCleanupJob(jm.repo)

	// 注册到调度器
	return jm.scheduler.AddJob("data_cleanup", job, jobConfig.Cron, jobConfig)
}

// registerTrialCheckJob 注册试用期检查任务
func (jm *JobManager) registerTrialCheckJob() error {
	jobConfig, exists := jm.config.Scheduler.Jobs["trial_check"]
	if !exists || !jobConfig.Enabled {
		logger.Info("Trial check job is disabled")
		return nil
	}

	// 创建任务
	job := NewTrialCheckJob(jm.repo)

	// 注册到调度器
	return jm.scheduler.AddJob("trial_check", job, jobConfig.Cron, jobConfig)
}

// registerPushCleanupJob 注册推送数据清理任务
func (jm *JobManager) registerPushCleanupJob() error {
	jobConfig, exists := jm.config.Scheduler.Jobs["push_cleanup"]
	if !exists || !jobConfig.Enabled {
		logger.Info("Push cleanup job is disabled")
		return nil
	}

	// 创建任务
	job := NewPushCleanupJob(jm.pushService)

	// 注册到调度器
	return jm.scheduler.AddJob("push_cleanup", job, jobConfig.Cron, jobConfig)
}

// Start 启动任务管理器
func (jm *JobManager) Start() error {
	logger.Info("Starting job manager")
	return jm.scheduler.Start()
}

// Stop 停止任务管理器
func (jm *JobManager) Stop() error {
	logger.Info("Stopping job manager")
	return jm.scheduler.Stop()
}

// GetJobStatus 获取任务状态
func (jm *JobManager) GetJobStatus(jobID string) (*scheduler.JobWrapper, error) {
	return jm.scheduler.GetJobStatus(jobID)
}

// GetAllJobsStatus 获取所有任务状态
func (jm *JobManager) GetAllJobsStatus() map[string]*scheduler.JobWrapper {
	return jm.scheduler.GetAllJobs()
}

// DataCleanupJob 数据清理任务
type DataCleanupJob struct {
	repo repository.Repository
}

// NewDataCleanupJob 创建数据清理任务
func NewDataCleanupJob(repo repository.Repository) *DataCleanupJob {
	return &DataCleanupJob{repo: repo}
}

// Execute 执行数据清理任务
func (j *DataCleanupJob) Execute(ctx context.Context) error {
	logger.Info("Starting data cleanup job")

	// 这里可以实现数据清理逻辑
	// 例如：删除过期的数据更新日志、清理无效的路线记录等

	logger.Info("Data cleanup job completed")
	return nil
}

// Name 返回任务名称
func (j *DataCleanupJob) Name() string {
	return "DataCleanupJob"
}

// TrialCheckJob 试用期检查任务
type TrialCheckJob struct {
	repo repository.Repository
}

// NewTrialCheckJob 创建试用期检查任务
func NewTrialCheckJob(repo repository.Repository) *TrialCheckJob {
	return &TrialCheckJob{repo: repo}
}

// Execute 执行试用期检查任务
func (j *TrialCheckJob) Execute(ctx context.Context) error {
	logger.Info("Starting trial check job")

	// 这里可以实现试用期检查逻辑
	// 例如：检查试用期到期的用户，发送通知等

	logger.Info("Trial check job completed")
	return nil
}

// Name 返回任务名称
func (j *TrialCheckJob) Name() string {
	return "TrialCheckJob"
}
