package jobs

import (
	"context"
	"fmt"
	"time"

	"github.com/azel-ko/final-ddd/internal/application/services"
	"github.com/azel-ko/final-ddd/internal/domain/entities"
	"github.com/azel-ko/final-ddd/internal/domain/repository"
	"github.com/azel-ko/final-ddd/internal/infrastructure/crawler"
	"github.com/azel-ko/final-ddd/internal/pkg/config"
	"github.com/azel-ko/final-ddd/internal/pkg/logger"
	"go.uber.org/zap"
)

// CheckpointUpdateJob 检查站数据更新任务
type CheckpointUpdateJob struct {
	crawler           *crawler.CheckpointCrawler
	repo              repository.Repository
	cacheService      *services.CacheService
	checkpointService *services.CheckpointService
	config            *config.DataSourcesConfig
}

// NewCheckpointUpdateJob 创建检查站数据更新任务
func NewCheckpointUpdateJob(
	crawler *crawler.CheckpointCrawler,
	repo repository.Repository,
	cacheService *services.CacheService,
	checkpointService *services.CheckpointService,
	config *config.DataSourcesConfig,
) *CheckpointUpdateJob {
	return &CheckpointUpdateJob{
		crawler:           crawler,
		repo:              repo,
		cacheService:      cacheService,
		checkpointService: checkpointService,
		config:            config,
	}
}

// Execute 执行任务
func (j *CheckpointUpdateJob) Execute(ctx context.Context) error {
	startTime := time.Now()
	logger.Info("Starting checkpoint update job")

	// 创建数据更新日志
	updateLog := &entities.DataUpdateLog{
		Source:    "jinjing365.com",
		Type:      "checkpoint_update",
		Status:    "running",
		CreatedAt: startTime,
	}

	// 1. 爬取最新数据
	result, err := j.crawler.CrawlCheckpoints(ctx)
	if err != nil {
		updateLog.Status = "failed"
		updateLog.ErrorMessage = err.Error()
		updateLog.Duration = int(time.Since(startTime).Milliseconds())
		
		if logErr := j.repo.CreateDataUpdateLog(updateLog); logErr != nil {
			logger.Error("Failed to create update log", zap.Error(logErr))
		}
		
		return fmt.Errorf("failed to crawl checkpoints: %w", err)
	}

	logger.Info("Checkpoint crawling completed", 
		zap.Int("total_found", result.TotalFound),
		zap.Int("valid_count", result.ValidCount),
		zap.Int("final_count", len(result.Checkpoints)))

	// 2. 获取现有数据进行对比
	existingCheckpoints, err := j.getExistingCheckpoints(ctx)
	if err != nil {
		logger.Error("Failed to get existing checkpoints", zap.Error(err))
		// 继续执行，不中断任务
	}

	// 3. 执行增量更新
	updateStats, err := j.performIncrementalUpdate(ctx, result.Checkpoints, existingCheckpoints)
	if err != nil {
		updateLog.Status = "failed"
		updateLog.ErrorMessage = err.Error()
		updateLog.Duration = int(time.Since(startTime).Milliseconds())
		
		if logErr := j.repo.CreateDataUpdateLog(updateLog); logErr != nil {
			logger.Error("Failed to create update log", zap.Error(logErr))
		}
		
		return fmt.Errorf("failed to perform incremental update: %w", err)
	}

	// 4. 更新缓存
	if err := j.updateCache(ctx, result.Checkpoints); err != nil {
		logger.Error("Failed to update cache", zap.Error(err))
		// 缓存更新失败不中断任务
	}

	// 5. 记录更新日志
	updateLog.Status = "success"
	updateLog.RecordsAdded = updateStats.Added
	updateLog.RecordsUpdated = updateStats.Updated
	updateLog.RecordsDeleted = updateStats.Deleted
	updateLog.Duration = int(time.Since(startTime).Milliseconds())

	if err := j.repo.CreateDataUpdateLog(updateLog); err != nil {
		logger.Error("Failed to create update log", zap.Error(err))
	}

	logger.Info("Checkpoint update job completed successfully", 
		zap.Int("added", updateStats.Added),
		zap.Int("updated", updateStats.Updated),
		zap.Int("deleted", updateStats.Deleted),
		zap.Duration("duration", time.Since(startTime)))

	return nil
}

// Name 返回任务名称
func (j *CheckpointUpdateJob) Name() string {
	return "CheckpointUpdateJob"
}

// UpdateStats 更新统计信息
type UpdateStats struct {
	Added   int
	Updated int
	Deleted int
}

// getExistingCheckpoints 获取现有检查站数据
func (j *CheckpointUpdateJob) getExistingCheckpoints(ctx context.Context) (map[string]*entities.Checkpoint, error) {
	params := repository.CheckpointQueryParams{
		Page:     1,
		PageSize: 10000, // 获取所有数据
	}

	checkpoints, _, err := j.repo.ListCheckpoints(params)
	if err != nil {
		return nil, err
	}

	// 创建映射表，用于快速查找
	existingMap := make(map[string]*entities.Checkpoint)
	for _, checkpoint := range checkpoints {
		// 使用名称和坐标作为唯一标识
		key := fmt.Sprintf("%s_%.6f_%.6f", 
			checkpoint.Name, 
			checkpoint.Latitude, 
			checkpoint.Longitude)
		existingMap[key] = checkpoint
	}

	return existingMap, nil
}

// performIncrementalUpdate 执行增量更新
func (j *CheckpointUpdateJob) performIncrementalUpdate(
	ctx context.Context, 
	newCheckpoints []*entities.Checkpoint, 
	existingCheckpoints map[string]*entities.Checkpoint,
) (*UpdateStats, error) {
	stats := &UpdateStats{}

	// 创建新数据的映射表
	newMap := make(map[string]*entities.Checkpoint)
	for _, checkpoint := range newCheckpoints {
		key := fmt.Sprintf("%s_%.6f_%.6f", 
			checkpoint.Name, 
			checkpoint.Latitude, 
			checkpoint.Longitude)
		newMap[key] = checkpoint
	}

	// 1. 处理新增和更新
	var toCreate []*entities.Checkpoint
	var toUpdate []*entities.Checkpoint

	for key, newCheckpoint := range newMap {
		if existing, exists := existingCheckpoints[key]; exists {
			// 检查是否需要更新
			if j.needsUpdate(existing, newCheckpoint) {
				newCheckpoint.ID = existing.ID // 保持原有ID
				newCheckpoint.CreatedAt = existing.CreatedAt // 保持创建时间
				toUpdate = append(toUpdate, newCheckpoint)
			}
		} else {
			// 新增数据
			toCreate = append(toCreate, newCheckpoint)
		}
	}

	// 2. 批量创建新数据
	if len(toCreate) > 0 {
		if err := j.repo.BatchCreateCheckpoints(toCreate); err != nil {
			return nil, fmt.Errorf("failed to batch create checkpoints: %w", err)
		}
		stats.Added = len(toCreate)
		logger.Info("Created new checkpoints", zap.Int("count", stats.Added))
	}

	// 3. 批量更新数据（使用checkpoint service以触发通知）
	if len(toUpdate) > 0 {
		// 对于状态变化的检查站，使用checkpoint service单独更新以触发通知
		var statusChangedCount int
		var regularUpdates []*entities.Checkpoint
		
		for _, newCheckpoint := range toUpdate {
			if existing, exists := existingCheckpoints[fmt.Sprintf("%s_%.6f_%.6f", 
				newCheckpoint.Name, newCheckpoint.Latitude, newCheckpoint.Longitude)]; exists {
				
				// 如果状态发生变化，使用checkpoint service更新
				if existing.Status != newCheckpoint.Status {
					if err := j.checkpointService.UpdateCheckpointStatus(ctx, existing.ID, newCheckpoint.Status); err != nil {
						logger.Error("Failed to update checkpoint status via service", 
							zap.Uint("id", existing.ID),
							zap.String("old_status", existing.Status),
							zap.String("new_status", newCheckpoint.Status),
							zap.Error(err))
					} else {
						statusChangedCount++
					}
				} else {
					// 非状态变化的更新，批量处理
					regularUpdates = append(regularUpdates, newCheckpoint)
				}
			}
		}
		
		// 批量更新非状态变化的检查站
		if len(regularUpdates) > 0 {
			if err := j.repo.BatchUpdateCheckpoints(regularUpdates); err != nil {
				return nil, fmt.Errorf("failed to batch update checkpoints: %w", err)
			}
		}
		
		stats.Updated = len(toUpdate)
		logger.Info("Updated checkpoints", 
			zap.Int("total_count", stats.Updated),
			zap.Int("status_changed", statusChangedCount),
			zap.Int("regular_updates", len(regularUpdates)))
	}

	// 4. 处理删除（标记为不活跃而不是物理删除）
	for key, existing := range existingCheckpoints {
		if _, exists := newMap[key]; !exists {
			// 数据源中不存在，标记为不活跃
			if existing.Status != "inactive" {
				// 使用checkpoint service更新状态以触发通知
				if err := j.checkpointService.UpdateCheckpointStatus(ctx, existing.ID, "inactive"); err != nil {
					logger.Error("Failed to mark checkpoint as inactive via service", 
						zap.Uint("id", existing.ID),
						zap.Error(err))
				} else {
					stats.Deleted++
				}
			}
		}
	}

	if stats.Deleted > 0 {
		logger.Info("Marked checkpoints as inactive", zap.Int("count", stats.Deleted))
	}

	return stats, nil
}

// needsUpdate 检查是否需要更新
func (j *CheckpointUpdateJob) needsUpdate(existing, new *entities.Checkpoint) bool {
	// 比较关键字段是否有变化
	return existing.Status != new.Status ||
		existing.Location != new.Location ||
		existing.Road != new.Road ||
		existing.Direction != new.Direction ||
		existing.Type != new.Type ||
		abs(existing.Latitude-new.Latitude) > 0.000001 ||
		abs(existing.Longitude-new.Longitude) > 0.000001
}

// abs 计算浮点数绝对值
func abs(x float64) float64 {
	if x < 0 {
		return -x
	}
	return x
}

// updateCache 更新缓存
func (j *CheckpointUpdateJob) updateCache(ctx context.Context, checkpoints []*entities.Checkpoint) error {
	// 清除相关缓存
	if err := j.cacheService.InvalidateCheckpointCache(ctx); err != nil {
		logger.Error("Failed to invalidate checkpoint cache", zap.Error(err))
	}

	// 预热缓存
	if err := j.cacheService.WarmupCache(ctx, checkpoints); err != nil {
		logger.Error("Failed to warmup cache", zap.Error(err))
		return err
	}

	logger.Info("Cache updated successfully")
	return nil
}