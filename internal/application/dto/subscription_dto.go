package dto

import (
	"time"
	"github.com/azel-ko/final-ddd/internal/domain/entities"
)

// SubscriptionPlan represents available subscription plans
type SubscriptionPlan struct {
	ID          string  `json:"id"`
	Name        string  `json:"name"`
	Description string  `json:"description"`
	Price       float64 `json:"price"`
	Currency    string  `json:"currency"`
	Duration    int     `json:"duration"` // Duration in days
	Features    []string `json:"features"`
	IsPopular   bool    `json:"is_popular"`
}

// SubscriptionPurchaseRequest represents a subscription purchase request
type SubscriptionPurchaseRequest struct {
	PlanID        string `json:"plan_id" binding:"required"`
	PaymentMethod string `json:"payment_method" binding:"required,oneof=alipay wechat_pay credit_card"`
	AutoRenew     bool   `json:"auto_renew"`
}

// SubscriptionPurchaseResponse represents subscription purchase response
type SubscriptionPurchaseResponse struct {
	OrderID       string    `json:"order_id"`
	PlanID        string    `json:"plan_id"`
	Status        string    `json:"status"`
	PaymentURL    string    `json:"payment_url,omitempty"`
	ExpiresAt     time.Time `json:"expires_at"`
	CreatedAt     time.Time `json:"created_at"`
}

// SubscriptionStatusResponse represents user's subscription status
type SubscriptionStatusResponse struct {
	UserID           uint       `json:"user_id"`
	CurrentPlan      string     `json:"current_plan"`
	Status           string     `json:"status"`
	ExpiresAt        *time.Time `json:"expires_at"`
	TrialExpiry      *time.Time `json:"trial_expiry"`
	IsTrialExpired   bool       `json:"is_trial_expired"`
	RemainingDays    int        `json:"remaining_days"`
	AutoRenew        bool       `json:"auto_renew"`
	NextBillingDate  *time.Time `json:"next_billing_date"`
	AvailableUpgrades []SubscriptionPlan `json:"available_upgrades"`
}

// SubscriptionRenewalRequest represents subscription renewal request
type SubscriptionRenewalRequest struct {
	PlanID        string `json:"plan_id"`
	PaymentMethod string `json:"payment_method" binding:"required,oneof=alipay wechat_pay credit_card"`
	Duration      int    `json:"duration,default=30"` // Duration in days
}

// UsageStatsRequest represents usage statistics query parameters
type UsageStatsRequest struct {
	StartDate time.Time `form:"start_date"`
	EndDate   time.Time `form:"end_date"`
	Granularity string  `form:"granularity,default=daily" binding:"oneof=hourly daily weekly monthly"`
}

// UsageStatsResponse represents user usage statistics
type UsageStatsResponse struct {
	UserID           uint                 `json:"user_id"`
	Period           string               `json:"period"`
	TotalRequests    int64                `json:"total_requests"`
	NavigationCount  int64                `json:"navigation_count"`
	CheckpointViews  int64                `json:"checkpoint_views"`
	RouteOptimizations int64              `json:"route_optimizations"`
	DailyStats       []DailyUsageStats    `json:"daily_stats"`
	FeatureUsage     map[string]int64     `json:"feature_usage"`
	TopRoutes        []RouteUsageStats    `json:"top_routes"`
}

// DailyUsageStats represents daily usage statistics
type DailyUsageStats struct {
	Date            string `json:"date"`
	NavigationCount int64  `json:"navigation_count"`
	CheckpointViews int64  `json:"checkpoint_views"`
	TotalRequests   int64  `json:"total_requests"`
}

// RouteUsageStats represents route usage statistics
type RouteUsageStats struct {
	StartAddress string `json:"start_address"`
	EndAddress   string `json:"end_address"`
	UsageCount   int64  `json:"usage_count"`
	LastUsed     time.Time `json:"last_used"`
}

// UserActivityLog represents user activity logging
type UserActivityLog struct {
	ID        uint      `json:"id"`
	UserID    uint      `json:"user_id"`
	Action    string    `json:"action"`
	Resource  string    `json:"resource"`
	Details   string    `json:"details"`
	IPAddress string    `json:"ip_address"`
	UserAgent string    `json:"user_agent"`
	CreatedAt time.Time `json:"created_at"`
}

// GetAvailablePlans returns available subscription plans
func GetAvailablePlans() []SubscriptionPlan {
	return []SubscriptionPlan{
		{
			ID:          "free",
			Name:        "免费版",
			Description: "基础功能，仅可查看检查站信息",
			Price:       0,
			Currency:    "CNY",
			Duration:    0, // Permanent
			Features: []string{
				"查看检查站信息",
				"基础地图功能",
				"有限历史记录（10条）",
			},
			IsPopular: false,
		},
		{
			ID:          "monthly",
			Name:        "月度会员",
			Description: "完整导航功能，月度订阅",
			Price:       19.9,
			Currency:    "CNY",
			Duration:    30,
			Features: []string{
				"完整导航功能",
				"路线优化",
				"实时避让检查站",
				"历史记录（50条）",
				"语音导航",
				"夜间模式",
			},
			IsPopular: true,
		},
		{
			ID:          "yearly",
			Name:        "年度会员",
			Description: "完整导航功能，年度订阅享受优惠",
			Price:       199,
			Currency:    "CNY",
			Duration:    365,
			Features: []string{
				"完整导航功能",
				"路线优化",
				"实时避让检查站",
				"无限历史记录",
				"语音导航",
				"夜间模式",
				"路线分析报告",
				"优先客服支持",
			},
			IsPopular: false,
		},
		{
			ID:          "lifetime",
			Name:        "终身会员",
			Description: "一次购买，终身使用",
			Price:       999,
			Currency:    "CNY",
			Duration:    0, // Lifetime
			Features: []string{
				"完整导航功能",
				"路线优化",
				"实时避让检查站",
				"无限历史记录",
				"语音导航",
				"夜间模式",
				"路线分析报告",
				"优先客服支持",
				"新功能抢先体验",
			},
			IsPopular: false,
		},
	}
}

// ToSubscriptionStatusResponse converts user entity to subscription status response
func ToSubscriptionStatusResponse(user *entities.User, isTrialExpired bool, remainingDays int) *SubscriptionStatusResponse {
	availablePlans := GetAvailablePlans()
	
	// Filter available upgrades based on current subscription
	var availableUpgrades []SubscriptionPlan
	for _, plan := range availablePlans {
		if plan.ID != user.Subscription && plan.Price > 0 {
			availableUpgrades = append(availableUpgrades, plan)
		}
	}

	return &SubscriptionStatusResponse{
		UserID:            user.ID,
		CurrentPlan:       user.Subscription,
		Status:            getSubscriptionStatus(user, isTrialExpired),
		ExpiresAt:         user.TrialExpiry,
		TrialExpiry:       user.TrialExpiry,
		IsTrialExpired:    isTrialExpired,
		RemainingDays:     remainingDays,
		AutoRenew:         false, // This would come from subscription settings
		NextBillingDate:   user.TrialExpiry,
		AvailableUpgrades: availableUpgrades,
	}
}

// Helper function to determine subscription status
func getSubscriptionStatus(user *entities.User, isTrialExpired bool) string {
	switch user.Subscription {
	case "trial":
		if isTrialExpired {
			return "expired"
		}
		return "active"
	case "free":
		return "active"
	case "premium", "monthly", "yearly", "lifetime":
		return "active"
	default:
		return "inactive"
	}
}