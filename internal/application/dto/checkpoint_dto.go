package dto

import (
	"time"

	"github.com/azel-ko/final-ddd/internal/domain/entities"
)

// CheckpointRequest represents a request to create or update a checkpoint
type CheckpointRequest struct {
	Name      string  `json:"name" binding:"required"`
	Address   string  `json:"address" binding:"required"`
	Latitude  float64 `json:"latitude,omitempty"`
	Longitude float64 `json:"longitude,omitempty"`
	Province  string  `json:"province" binding:"required"`
	City      string  `json:"city" binding:"required"`
	District  string  `json:"district"`
	Status    string  `json:"status" binding:"oneof=active inactive unknown"`
	Severity  int     `json:"severity" binding:"min=1,max=5"`
	Type      string  `json:"type" binding:"required"`
}

// CheckpointResponse represents checkpoint data in API responses
type CheckpointResponse struct {
	ID          uint       `json:"id"`
	Name        string     `json:"name"`
	Location    string     `json:"location"`
	Latitude    float64    `json:"latitude"`
	Longitude   float64    `json:"longitude"`
	Province    string     `json:"province"`
	City        string     `json:"city"`
	District    string     `json:"district"`
	Road        string     `json:"road"`
	Direction   string     `json:"direction"`
	Status      string     `json:"status"`
	Type        string     `json:"type"`
	Source      string     `json:"source"`
	Reliability int        `json:"reliability"`
	LastSeen    *time.Time `json:"last_seen"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	Distance    *float64   `json:"distance,omitempty"` // Distance in km (for nearby searches)
}

// CheckpointListRequest represents query parameters for checkpoint list
type CheckpointListRequest struct {
	Province  string `form:"province"`
	City      string `form:"city"`
	District  string `form:"district"`
	Status    string `form:"status" binding:"omitempty,oneof=active inactive unknown"`
	Type      string `form:"type"`
	Page      int    `form:"page,default=1" binding:"min=1"`
	PageSize  int    `form:"page_size,default=20" binding:"min=1,max=100"`
	SortBy    string `form:"sort_by,default=created_at" binding:"omitempty,oneof=created_at updated_at name reliability"`
	SortOrder string `form:"sort_order,default=desc" binding:"omitempty,oneof=asc desc"`
}

// NearbyCheckpointsRequest represents query parameters for nearby checkpoint search
type NearbyCheckpointsRequest struct {
	Latitude  float64 `form:"latitude" binding:"required"`
	Longitude float64 `form:"longitude" binding:"required"`
	Radius    int     `form:"radius,default=10" binding:"min=1,max=50"` // Radius in km
	Status    string  `form:"status" binding:"omitempty,oneof=active inactive unknown"`
	Type      string  `form:"type"`
	Limit     int     `form:"limit,default=20" binding:"min=1,max=100"`
}

// CheckpointListResponse represents paginated checkpoint list response
type CheckpointListResponse struct {
	Data       []*CheckpointResponse `json:"data"`
	Total      int64                 `json:"total"`
	Page       int                   `json:"page"`
	PageSize   int                   `json:"page_size"`
	TotalPages int                   `json:"total_pages"`
}

// CheckpointReportRequest represents a request to report checkpoint status
type CheckpointReportRequest struct {
	CheckpointID uint    `json:"checkpoint_id" binding:"required"`
	Status       string  `json:"status" binding:"required,oneof=active inactive moved"`
	Description  string  `json:"description"`
	Latitude     float64 `json:"latitude"`
	Longitude    float64 `json:"longitude"`
}

// CheckpointReportResponse represents checkpoint report data
type CheckpointReportResponse struct {
	ID           uint      `json:"id"`
	CheckpointID uint      `json:"checkpoint_id"`
	UserID       uint      `json:"user_id"`
	Status       string    `json:"status"`
	Description  string    `json:"description"`
	Latitude     float64   `json:"latitude"`
	Longitude    float64   `json:"longitude"`
	Verified     bool      `json:"verified"`
	CreatedAt    time.Time `json:"created_at"`
}

// ToCheckpointEntity converts DTO to entity
func ToCheckpointEntity(req *CheckpointRequest) *entities.Checkpoint {
	return &entities.Checkpoint{
		Name:        req.Name,
		Location:    req.Address, // Map Address to Location field
		Latitude:    req.Latitude,
		Longitude:   req.Longitude,
		Province:    req.Province,
		City:        req.City,
		District:    req.District,
		Status:      req.Status,
		Type:        req.Type,
		Reliability: req.Severity * 20, // Map Severity (1-5) to Reliability (20-100)
	}
}

// ToCheckpointResponse converts entity to response DTO
func ToCheckpointResponse(checkpoint *entities.Checkpoint) *CheckpointResponse {
	return &CheckpointResponse{
		ID:          checkpoint.ID,
		Name:        checkpoint.Name,
		Location:    checkpoint.Location,
		Latitude:    checkpoint.Latitude,
		Longitude:   checkpoint.Longitude,
		Province:    checkpoint.Province,
		City:        checkpoint.City,
		District:    checkpoint.District,
		Road:        checkpoint.Road,
		Direction:   checkpoint.Direction,
		Status:      checkpoint.Status,
		Type:        checkpoint.Type,
		Source:      checkpoint.Source,
		Reliability: checkpoint.Reliability,
		LastSeen:    checkpoint.LastSeen,
		CreatedAt:   checkpoint.CreatedAt,
		UpdatedAt:   checkpoint.UpdatedAt,
	}
}

// ToCheckpointResponseWithDistance converts entity to response DTO with distance
func ToCheckpointResponseWithDistance(checkpoint *entities.Checkpoint, distance float64) *CheckpointResponse {
	response := ToCheckpointResponse(checkpoint)
	response.Distance = &distance
	return response
}

// ToCheckpointReportEntity converts DTO to entity
func ToCheckpointReportEntity(userID uint, req *CheckpointReportRequest) *entities.CheckpointReport {
	return &entities.CheckpointReport{
		CheckpointID: req.CheckpointID,
		UserID:       userID,
		Status:       req.Status,
		Description:  req.Description,
		Latitude:     req.Latitude,
		Longitude:    req.Longitude,
		Verified:     false, // Reports start as unverified
	}
}

// ToCheckpointReportResponse converts entity to response DTO
func ToCheckpointReportResponse(report *entities.CheckpointReport) *CheckpointReportResponse {
	return &CheckpointReportResponse{
		ID:           report.ID,
		CheckpointID: report.CheckpointID,
		UserID:       report.UserID,
		Status:       report.Status,
		Description:  report.Description,
		Latitude:     report.Latitude,
		Longitude:    report.Longitude,
		Verified:     report.Verified,
		CreatedAt:    report.CreatedAt,
	}
}

// CheckpointStatsResponse represents checkpoint statistics
type CheckpointStatsResponse struct {
	Total         int            `json:"total"`
	Active        int            `json:"active"`
	Inactive      int            `json:"inactive"`
	Unknown       int            `json:"unknown"`
	ByProvince    map[string]int `json:"byProvince"`
	ByType        map[string]int `json:"byType"`
	RecentUpdates int            `json:"recentUpdates"`
}

// BatchUpdateCheckpointsRequest represents a batch update request
type BatchUpdateCheckpointsRequest struct {
	IDs  []uint               `json:"ids" binding:"required"`
	Data CheckpointUpdateData `json:"data" binding:"required"`
}

// CheckpointUpdateData represents the data to update in batch operations
type CheckpointUpdateData struct {
	Status   string `json:"status,omitempty" binding:"omitempty,oneof=active inactive unknown"`
	Severity int    `json:"severity,omitempty" binding:"omitempty,min=1,max=5"`
	Type     string `json:"type,omitempty"`
}

// BatchUpdateResult represents the result of a batch update operation
type BatchUpdateResult struct {
	TotalCount   int               `json:"totalCount"`
	SuccessCount int               `json:"successCount"`
	FailedCount  int               `json:"failedCount"`
	Errors       map[string]string `json:"errors,omitempty"`
}
