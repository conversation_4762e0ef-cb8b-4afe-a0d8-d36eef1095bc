package dto

import "github.com/azel-ko/final-ddd/internal/domain/entities"

type LoginRequest struct {
	Email     string `json:"email" binding:"required,email"`
	Password  string `json:"password" binding:"required,min=6"`
	DeviceID  string `json:"device_id"`
	Platform  string `json:"platform" binding:"oneof=web mobile car miniprogram"`
	UserAgent string `json:"user_agent"`
}

type LoginResponse struct {
	Token     string `json:"token"`
	SessionID string `json:"session_id"`
	ExpiresAt int64  `json:"expires_at"`
	User      UserResponse
}

func ToLoginResponse(token, sessionID string, expiresAt int64, user *entities.User) *LoginResponse {
	return &LoginResponse{
		Token:     token,
		SessionID: sessionID,
		ExpiresAt: expiresAt,
		User: UserResponse{
			ID:           user.ID,
			Email:        user.Email,
			Name:         user.Name,
			CarPlate:     user.CarPlate,
			PlateRegion:  user.PlateRegion,
			Subscription: user.Subscription,
		},
	}
}
