package dto

import "time"

// SystemStatsResponse 系统统计信息响应
type SystemStatsResponse struct {
	Uptime            int64   `json:"uptime"`             // 系统运行时间（秒）
	CPUUsage          float64 `json:"cpu_usage"`          // CPU使用率（百分比）
	MemoryUsage       float64 `json:"memory_usage"`       // 内存使用率（百分比）
	DiskUsage         float64 `json:"disk_usage"`         // 磁盘使用率（百分比）
	ActiveConnections int     `json:"active_connections"` // 活跃连接数
	TotalRequests     int64   `json:"total_requests"`     // 总请求数
	ErrorRate         float64 `json:"error_rate"`         // 错误率（百分比）
	ResponseTime      float64 `json:"response_time"`      // 平均响应时间（毫秒）
}

// APIStatsResponse API统计信息响应
type APIStatsResponse struct {
	Endpoint        string    `json:"endpoint"`          // API端点
	Method          string    `json:"method"`            // HTTP方法
	TotalCalls      int64     `json:"total_calls"`       // 总调用次数
	SuccessRate     float64   `json:"success_rate"`      // 成功率（百分比）
	AvgResponseTime float64   `json:"avg_response_time"` // 平均响应时间（毫秒）
	ErrorCount      int64     `json:"error_count"`       // 错误次数
	LastCalled      time.Time `json:"last_called"`       // 最后调用时间
}

// LogEntryResponse 日志条目响应
type LogEntryResponse struct {
	ID        string                 `json:"id"`        // 日志ID
	Timestamp time.Time              `json:"timestamp"` // 时间戳
	Level     string                 `json:"level"`     // 日志级别
	Message   string                 `json:"message"`   // 日志消息
	Source    string                 `json:"source"`    // 日志来源
	Details   map[string]interface{} `json:"details"`   // 详细信息
}

// ErrorEntryResponse 错误条目响应
type ErrorEntryResponse struct {
	ID         string    `json:"id"`          // 错误ID
	Timestamp  time.Time `json:"timestamp"`   // 时间戳
	ErrorType  string    `json:"error_type"`  // 错误类型
	Message    string    `json:"message"`     // 错误消息
	StackTrace string    `json:"stack_trace"` // 堆栈跟踪
	UserID     string    `json:"user_id"`     // 用户ID
	RequestID  string    `json:"request_id"`  // 请求ID
	Count      int       `json:"count"`       // 错误次数
}

// LogQueryRequest 日志查询请求
type LogQueryRequest struct {
	Level     string    `form:"level"`      // 日志级别过滤
	Source    string    `form:"source"`     // 来源过滤
	Search    string    `form:"search"`     // 搜索关键词
	StartTime time.Time `form:"start_time"` // 开始时间
	EndTime   time.Time `form:"end_time"`   // 结束时间
	Page      int       `form:"page,default=1" binding:"min=1"`
	PageSize  int       `form:"page_size,default=50" binding:"min=1,max=1000"`
}

// ErrorQueryRequest 错误查询请求
type ErrorQueryRequest struct {
	ErrorType string    `form:"error_type"` // 错误类型过滤
	Search    string    `form:"search"`     // 搜索关键词
	StartTime time.Time `form:"start_time"` // 开始时间
	EndTime   time.Time `form:"end_time"`   // 结束时间
	Page      int       `form:"page,default=1" binding:"min=1"`
	PageSize  int       `form:"page_size,default=50" binding:"min=1,max=1000"`
}

// APIStatsQueryRequest API统计查询请求
type APIStatsQueryRequest struct {
	Endpoint  string    `form:"endpoint"`   // 端点过滤
	Method    string    `form:"method"`     // 方法过滤
	StartTime time.Time `form:"start_time"` // 开始时间
	EndTime   time.Time `form:"end_time"`   // 结束时间
	SortBy    string    `form:"sort_by,default=total_calls" binding:"omitempty,oneof=total_calls success_rate avg_response_time error_count"`
	SortOrder string    `form:"sort_order,default=desc" binding:"omitempty,oneof=asc desc"`
}

// MonitoringConfigRequest 监控配置请求
type MonitoringConfigRequest struct {
	CPUThreshold    float64 `json:"cpu_threshold" binding:"min=0,max=100"`    // CPU使用率阈值
	MemoryThreshold float64 `json:"memory_threshold" binding:"min=0,max=100"` // 内存使用率阈值
	DiskThreshold   float64 `json:"disk_threshold" binding:"min=0,max=100"`   // 磁盘使用率阈值
	ErrorThreshold  float64 `json:"error_threshold" binding:"min=0,max=100"`  // 错误率阈值
	AlertEnabled    bool    `json:"alert_enabled"`                            // 是否启用告警
	AlertEmail      string  `json:"alert_email"`                              // 告警邮箱
}

// AlertResponse 告警响应
type AlertResponse struct {
	ID         string    `json:"id"`          // 告警ID
	Type       string    `json:"type"`        // 告警类型
	Level      string    `json:"level"`       // 告警级别
	Title      string    `json:"title"`       // 告警标题
	Message    string    `json:"message"`     // 告警消息
	Timestamp  time.Time `json:"timestamp"`   // 告警时间
	Resolved   bool      `json:"resolved"`    // 是否已解决
	ResolvedAt time.Time `json:"resolved_at"` // 解决时间
	ResolvedBy string    `json:"resolved_by"` // 解决人
}

// HealthCheckResponse 健康检查响应
type HealthCheckResponse struct {
	Status     string                 `json:"status"`     // 整体状态
	Timestamp  time.Time              `json:"timestamp"`  // 检查时间
	Version    string                 `json:"version"`    // 版本信息
	Components map[string]interface{} `json:"components"` // 各组件状态
}
