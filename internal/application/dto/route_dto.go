package dto

import (
	"time"
	"github.com/azel-ko/final-ddd/internal/domain/entities"
)

// RouteRequest represents a request to plan a route
type RouteRequest struct {
	StartLat     float64 `json:"start_lat" binding:"required"`
	StartLng     float64 `json:"start_lng" binding:"required"`
	EndLat       float64 `json:"end_lat" binding:"required"`
	EndLng       float64 `json:"end_lng" binding:"required"`
	StartAddress string  `json:"start_address"`
	EndAddress   string  `json:"end_address"`
	AvoidLevel   int     `json:"avoid_level,default=1" binding:"min=1,max=3"` // 1=low, 2=medium, 3=high
	Strategy     string  `json:"strategy,default=fastest" binding:"oneof=fastest shortest avoid_checkpoints"`
}

// RouteResponse represents route planning result
type RouteResponse struct {
	ID            uint                   `json:"id,omitempty"`
	StartLat      float64                `json:"start_lat"`
	StartLng      float64                `json:"start_lng"`
	EndLat        float64                `json:"end_lat"`
	EndLng        float64                `json:"end_lng"`
	StartAddress  string                 `json:"start_address"`
	EndAddress    string                 `json:"end_address"`
	Distance      int                    `json:"distance"`      // Distance in meters
	Duration      int                    `json:"duration"`      // Duration in seconds
	RouteData     interface{}            `json:"route_data"`    // Detailed route information
	AvoidLevel    int                    `json:"avoid_level"`
	CheckpointIDs []uint                 `json:"checkpoint_ids"` // Checkpoints along the route
	Checkpoints   []*CheckpointResponse  `json:"checkpoints,omitempty"` // Detailed checkpoint info
	RiskScore     int                    `json:"risk_score"`    // Risk assessment score (0-100)
	CreatedAt     *time.Time             `json:"created_at,omitempty"`
	IsOptimized   bool                   `json:"is_optimized"`  // Whether route was optimized to avoid checkpoints
}

// RouteOptimizeRequest represents a request to optimize an existing route
type RouteOptimizeRequest struct {
	RouteID    uint `json:"route_id" binding:"required"`
	AvoidLevel int  `json:"avoid_level" binding:"min=1,max=3"`
}

// RouteHistoryRequest represents query parameters for route history
type RouteHistoryRequest struct {
	Page     int    `form:"page,default=1" binding:"min=1"`
	PageSize int    `form:"page_size,default=20" binding:"min=1,max=100"`
	SortBy   string `form:"sort_by,default=created_at" binding:"omitempty,oneof=created_at distance duration"`
	SortOrder string `form:"sort_order,default=desc" binding:"omitempty,oneof=asc desc"`
}

// RouteHistoryResponse represents paginated route history response
type RouteHistoryResponse struct {
	Data       []*RouteResponse `json:"data"`
	Total      int64            `json:"total"`
	Page       int              `json:"page"`
	PageSize   int              `json:"page_size"`
	TotalPages int              `json:"total_pages"`
}

// RouteAnalysisResponse represents route analysis for premium users
type RouteAnalysisResponse struct {
	Route              *RouteResponse           `json:"route"`
	CheckpointAnalysis []*CheckpointAnalysis    `json:"checkpoint_analysis"`
	AlternativeRoutes  []*RouteResponse         `json:"alternative_routes,omitempty"`
	RiskAssessment     *RiskAssessment          `json:"risk_assessment"`
	Recommendations    []string                 `json:"recommendations"`
}

// CheckpointAnalysis represents analysis of checkpoints along a route
type CheckpointAnalysis struct {
	Checkpoint   *CheckpointResponse `json:"checkpoint"`
	Distance     float64             `json:"distance"`     // Distance from route start in km
	RiskLevel    string              `json:"risk_level"`   // low, medium, high
	Probability  float64             `json:"probability"`  // Probability of being active (0-1)
	LastReported *time.Time          `json:"last_reported"`
}

// RiskAssessment represents overall route risk assessment
type RiskAssessment struct {
	OverallRisk     string  `json:"overall_risk"`      // low, medium, high
	RiskScore       int     `json:"risk_score"`        // 0-100
	CheckpointCount int     `json:"checkpoint_count"`
	HighRiskAreas   []string `json:"high_risk_areas"`
	BestTimeToTravel string `json:"best_time_to_travel"`
}

// ToRouteEntity converts DTO to entity
func ToRouteEntity(userID uint, req *RouteRequest, routeData interface{}, distance, duration int, checkpointIDs []uint) *entities.Route {
	return &entities.Route{
		UserID:        userID,
		StartLat:      req.StartLat,
		StartLng:      req.StartLng,
		EndLat:        req.EndLat,
		EndLng:        req.EndLng,
		StartAddress:  req.StartAddress,
		EndAddress:    req.EndAddress,
		Distance:      distance,
		Duration:      duration,
		RouteData:     routeDataToString(routeData),
		AvoidLevel:    req.AvoidLevel,
		CheckpointIDs: checkpointIDsToString(checkpointIDs),
	}
}

// ToRouteResponse converts entity to response DTO
func ToRouteResponse(route *entities.Route) *RouteResponse {
	return &RouteResponse{
		ID:            route.ID,
		StartLat:      route.StartLat,
		StartLng:      route.StartLng,
		EndLat:        route.EndLat,
		EndLng:        route.EndLng,
		StartAddress:  route.StartAddress,
		EndAddress:    route.EndAddress,
		Distance:      route.Distance,
		Duration:      route.Duration,
		RouteData:     stringToRouteData(route.RouteData),
		AvoidLevel:    route.AvoidLevel,
		CheckpointIDs: stringToCheckpointIDs(route.CheckpointIDs),
		CreatedAt:     &route.CreatedAt,
	}
}

// ToRouteResponseWithDetails converts entity to response DTO with additional details
func ToRouteResponseWithDetails(route *entities.Route, checkpoints []*CheckpointResponse, riskScore int, isOptimized bool) *RouteResponse {
	response := ToRouteResponse(route)
	response.Checkpoints = checkpoints
	response.RiskScore = riskScore
	response.IsOptimized = isOptimized
	return response
}

// Helper functions for data conversion
func routeDataToString(data interface{}) string {
	// In a real implementation, this would serialize the route data to JSON
	return "{}"
}

func stringToRouteData(data string) interface{} {
	// In a real implementation, this would deserialize JSON to route data
	return map[string]interface{}{}
}

func checkpointIDsToString(ids []uint) string {
	// In a real implementation, this would serialize the IDs to JSON
	return "[]"
}

func stringToCheckpointIDs(data string) []uint {
	// In a real implementation, this would deserialize JSON to ID slice
	return []uint{}
}