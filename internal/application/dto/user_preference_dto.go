package dto

import (
	"encoding/json"
	"time"
	"github.com/azel-ko/final-ddd/internal/domain/entities"
)

// UserPreferenceRequest represents a request to update user preferences
type UserPreferenceRequest struct {
	DefaultAvoidLevel int      `json:"default_avoid_level" binding:"min=1,max=3"`
	NotificationTypes []string `json:"notification_types"`
	AutoUpdate        *bool    `json:"auto_update"`
	VoiceNavigation   *bool    `json:"voice_navigation"`
	NightMode         *bool    `json:"night_mode"`
	MapStyle          string   `json:"map_style" binding:"oneof=standard satellite hybrid"`
}

// UserPreferenceResponse represents user preference data
type UserPreferenceResponse struct {
	ID                uint      `json:"id"`
	UserID            uint      `json:"user_id"`
	DefaultAvoidLevel int       `json:"default_avoid_level"`
	NotificationTypes []string  `json:"notification_types"`
	AutoUpdate        bool      `json:"auto_update"`
	VoiceNavigation   bool      `json:"voice_navigation"`
	NightMode         bool      `json:"night_mode"`
	MapStyle          string    `json:"map_style"`
	UpdatedAt         time.Time `json:"updated_at"`
}

// ToUserPreferenceEntity converts DTO to entity
func ToUserPreferenceEntity(userID uint, req *UserPreferenceRequest) (*entities.UserPreference, error) {
	notificationTypesJSON, err := json.Marshal(req.NotificationTypes)
	if err != nil {
		return nil, err
	}

	preference := &entities.UserPreference{
		UserID:            userID,
		DefaultAvoidLevel: req.DefaultAvoidLevel,
		NotificationTypes: string(notificationTypesJSON),
		MapStyle:          req.MapStyle,
	}

	if req.AutoUpdate != nil {
		preference.AutoUpdate = *req.AutoUpdate
	} else {
		preference.AutoUpdate = true // default value
	}

	if req.VoiceNavigation != nil {
		preference.VoiceNavigation = *req.VoiceNavigation
	} else {
		preference.VoiceNavigation = true // default value
	}

	if req.NightMode != nil {
		preference.NightMode = *req.NightMode
	} else {
		preference.NightMode = false // default value
	}

	return preference, nil
}

// ToUserPreferenceResponse converts entity to response DTO
func ToUserPreferenceResponse(preference *entities.UserPreference) (*UserPreferenceResponse, error) {
	var notificationTypes []string
	if preference.NotificationTypes != "" {
		if err := json.Unmarshal([]byte(preference.NotificationTypes), &notificationTypes); err != nil {
			return nil, err
		}
	}

	return &UserPreferenceResponse{
		ID:                preference.ID,
		UserID:            preference.UserID,
		DefaultAvoidLevel: preference.DefaultAvoidLevel,
		NotificationTypes: notificationTypes,
		AutoUpdate:        preference.AutoUpdate,
		VoiceNavigation:   preference.VoiceNavigation,
		NightMode:         preference.NightMode,
		MapStyle:          preference.MapStyle,
		UpdatedAt:         preference.UpdatedAt,
	}, nil
}

// GetDefaultUserPreference returns default user preferences for new users
func GetDefaultUserPreference(userID uint) *entities.UserPreference {
	defaultNotificationTypes := []string{"checkpoint_update", "route_change", "system_alert"}
	notificationTypesJSON, _ := json.Marshal(defaultNotificationTypes)

	return &entities.UserPreference{
		UserID:            userID,
		DefaultAvoidLevel: 2, // Medium avoidance level
		NotificationTypes: string(notificationTypesJSON),
		AutoUpdate:        true,
		VoiceNavigation:   true,
		NightMode:         false,
		MapStyle:          "standard",
	}
}