package dto

import (
	"time"
	"github.com/azel-ko/final-ddd/internal/domain/entities"
	"github.com/azel-ko/final-ddd/pkg/auth"
)

type UserRequest struct {
	Name        string `json:"username" binding:"required"`
	Email       string `json:"email" binding:"required,email"`
	Password    string `json:"password" binding:"required,min=6"`
	CarPlate    string `json:"car_plate"`
	PlateRegion string `json:"plate_region"`
}

type UserResponse struct {
	ID           uint   `json:"id"`
	Name         string `json:"username"`
	Email        string `json:"email"`
	CarPlate     string `json:"car_plate"`
	PlateRegion  string `json:"plate_region"`
	Subscription string `json:"subscription"`
}

type UserProfileResponse struct {
	ID           uint       `json:"id"`
	Name         string     `json:"username"`
	Email        string     `json:"email"`
	Role         string     `json:"role"`
	CarPlate     string     `json:"car_plate"`
	PlateRegion  string     `json:"plate_region"`
	Subscription string     `json:"subscription"`
	TrialExpiry  *time.Time `json:"trial_expiry"`
	Preferences  string     `json:"preferences"`
	CreatedAt    string     `json:"created_at"`
	LastLoginAt  *time.Time `json:"last_login_at"`
}

type UpdateUserProfileRequest struct {
	Name        *string `json:"username"`
	Email       *string `json:"email"`
	CarPlate    *string `json:"car_plate"`
	PlateRegion *string `json:"plate_region"`
	Preferences *string `json:"preferences"`
}

func ToUserEntity(req *UserRequest) (*entities.User, error) {
	password, err := auth.HashPassword(req.Password)
	if err != nil {
		return nil, err
	}
	
	// Set trial expiry to 3 days from now for new users
	trialExpiry := time.Now().Add(72 * time.Hour)
	
	return &entities.User{
		Name:         req.Name,
		Email:        req.Email,
		Password:     password,
		CarPlate:     req.CarPlate,
		PlateRegion:  req.PlateRegion,
		Subscription: "trial",
		TrialExpiry:  &trialExpiry,
		Role:         "user",
	}, nil
}

func ToUserResponse(user *entities.User) *UserResponse {
	return &UserResponse{
		ID:           user.ID,
		Name:         user.Name,
		Email:        user.Email,
		CarPlate:     user.CarPlate,
		PlateRegion:  user.PlateRegion,
		Subscription: user.Subscription,
	}
}

func ToUserProfileResponse(user *entities.User) *UserProfileResponse {
	return &UserProfileResponse{
		ID:           user.ID,
		Name:         user.Name,
		Email:        user.Email,
		Role:         user.Role,
		CarPlate:     user.CarPlate,
		PlateRegion:  user.PlateRegion,
		Subscription: user.Subscription,
		TrialExpiry:  user.TrialExpiry,
		Preferences:  user.Preferences,
		CreatedAt:    user.CreatedAt.Format("2006-01-02 15:04:05"),
		LastLoginAt:  user.LastLoginAt,
	}
}
// SubscriptionRequest for upgrading user subscription
type SubscriptionRequest struct {
	SubscriptionType string `json:"subscription_type" binding:"required,oneof=free premium"`
}

// PermissionResponse for checking user permissions
type PermissionResponse struct {
	CanNavigate        bool `json:"can_navigate"`
	CanViewCheckpoints bool `json:"can_view_checkpoints"`
}