package services

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"errors"
	"time"

	"github.com/azel-ko/final-ddd/internal/domain/entities"
	"github.com/azel-ko/final-ddd/internal/domain/repository"
	"github.com/azel-ko/final-ddd/internal/pkg/logger"
	"go.uber.org/zap"
)

type SessionService struct {
	repo  repository.Repository
	cache *CacheService
}

func NewSessionService(repo repository.Repository, cache *CacheService) *SessionService {
	return &SessionService{
		repo:  repo,
		cache: cache,
	}
}

// CreateSession creates a new user session
func (s *SessionService) CreateSession(ctx context.Context, userID uint, deviceID, platform, ipAddress, userAgent string) (*entities.UserSession, error) {
	sessionID, err := s.generateSessionID()
	if err != nil {
		return nil, err
	}

	session := &entities.UserSession{
		ID:         sessionID,
		UserID:     userID,
		DeviceID:   deviceID,
		Platform:   platform,
		IPAddress:  ipAddress,
		UserAgent:  userAgent,
		IsActive:   true,
		ExpiresAt:  time.Now().Add(8 * time.Hour), // 8 hours session
		LastUsedAt: time.Now(),
	}

	if err := s.repo.CreateSession(session); err != nil {
		return nil, err
	}

	logger.Info("Session created", 
		zap.String("session_id", sessionID),
		zap.Uint("user_id", userID),
		zap.String("platform", platform),
		zap.String("device_id", deviceID))

	return session, nil
}

// GetSession retrieves a session by ID
func (s *SessionService) GetSession(ctx context.Context, sessionID string) (*entities.UserSession, error) {
	session, err := s.repo.GetSession(sessionID)
	if err != nil {
		return nil, err
	}

	// Check if session is expired
	if session.IsExpired() {
		s.InvalidateSession(ctx, sessionID)
		return nil, err
	}

	return session, nil
}

// RefreshSession extends the session expiry time
func (s *SessionService) RefreshSession(ctx context.Context, sessionID string) error {
	session, err := s.repo.GetSession(sessionID)
	if err != nil {
		return err
	}

	if session.IsExpired() {
		return s.InvalidateSession(ctx, sessionID)
	}

	session.Refresh(8 * time.Hour)
	return s.repo.UpdateSession(session)
}

// InvalidateSession marks a session as inactive
func (s *SessionService) InvalidateSession(ctx context.Context, sessionID string) error {
	session, err := s.repo.GetSession(sessionID)
	if err != nil {
		return err
	}

	session.IsActive = false
	if err := s.repo.UpdateSession(session); err != nil {
		return err
	}

	logger.Info("Session invalidated", zap.String("session_id", sessionID))
	return nil
}

// GetUserSessions retrieves all active sessions for a user
func (s *SessionService) GetUserSessions(ctx context.Context, userID uint) ([]*entities.UserSession, error) {
	return s.repo.GetUserSessions(userID)
}

// InvalidateUserSessions invalidates all sessions for a user
func (s *SessionService) InvalidateUserSessions(ctx context.Context, userID uint) error {
	sessions, err := s.repo.GetUserSessions(userID)
	if err != nil {
		return err
	}

	for _, session := range sessions {
		session.IsActive = false
		if err := s.repo.UpdateSession(session); err != nil {
			logger.Error("Failed to invalidate session", 
				zap.String("session_id", session.ID),
				zap.Error(err))
		}
	}

	logger.Info("All user sessions invalidated", zap.Uint("user_id", userID))
	return nil
}

// InvalidateOtherSessions invalidates all sessions except the current one
func (s *SessionService) InvalidateOtherSessions(ctx context.Context, userID uint, currentSessionID string) error {
	sessions, err := s.repo.GetUserSessions(userID)
	if err != nil {
		return err
	}

	for _, session := range sessions {
		if session.ID != currentSessionID {
			session.IsActive = false
			if err := s.repo.UpdateSession(session); err != nil {
				logger.Error("Failed to invalidate session", 
					zap.String("session_id", session.ID),
					zap.Error(err))
			}
		}
	}

	logger.Info("Other user sessions invalidated", 
		zap.Uint("user_id", userID),
		zap.String("current_session", currentSessionID))
	return nil
}

// CleanupExpiredSessions removes expired sessions from the database
func (s *SessionService) CleanupExpiredSessions(ctx context.Context) error {
	if err := s.repo.DeleteExpiredSessions(); err != nil {
		logger.Error("Failed to cleanup expired sessions", zap.Error(err))
		return err
	}

	logger.Info("Expired sessions cleaned up")
	return nil
}

// ValidateDeviceAccess checks if a device is allowed to access the system
func (s *SessionService) ValidateDeviceAccess(ctx context.Context, userID uint, deviceID, platform string) error {
	// Get user's active sessions
	sessions, err := s.repo.GetUserSessions(userID)
	if err != nil {
		return err
	}

	// Check device limits based on subscription
	// This can be enhanced based on user subscription level
	maxDevices := s.getMaxDevicesForUser(userID)
	
	// Count unique devices
	deviceMap := make(map[string]bool)
	for _, session := range sessions {
		if session.IsActive && !session.IsExpired() {
			deviceMap[session.DeviceID] = true
		}
	}

	// If device already exists, allow access
	if deviceMap[deviceID] {
		return nil
	}

	// Check if adding new device exceeds limit
	if len(deviceMap) >= maxDevices {
		return ErrDeviceLimitExceeded
	}

	return nil
}

// getMaxDevicesForUser returns the maximum number of devices allowed for a user
func (s *SessionService) getMaxDevicesForUser(userID uint) int {
	// This can be enhanced to check user subscription level
	// For now, return a default limit
	return 5 // Allow 5 devices per user
}

// generateSessionID generates a unique session ID
func (s *SessionService) generateSessionID() (string, error) {
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// Custom errors
var (
	ErrSessionExpired      = errors.New("session expired")
	ErrSessionNotFound     = errors.New("session not found")
)