package services

import (
	"context"
	"time"

	"github.com/azel-ko/final-ddd/internal/domain/repository"
	"github.com/azel-ko/final-ddd/internal/infrastructure/persistence"
	"github.com/azel-ko/final-ddd/internal/pkg/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// DatabaseOptimizationService 数据库优化服务
type DatabaseOptimizationService struct {
	repo                    repository.Repository
	db                      *gorm.DB
	indexOptimizer          *persistence.DatabaseIndexOptimizer
	connectionPoolOptimizer *persistence.ConnectionPoolOptimizer
	performanceMonitor      *persistence.PerformanceMonitor
	queryCacheManager       *persistence.QueryCacheManager
}

// NewDatabaseOptimizationService 创建数据库优化服务
func NewDatabaseOptimizationService(repo repository.Repository, db *gorm.DB) *DatabaseOptimizationService {
	return &DatabaseOptimizationService{
		repo:                    repo,
		db:                      db,
		indexOptimizer:          persistence.NewDatabaseIndexOptimizer(db),
		connectionPoolOptimizer: persistence.NewConnectionPoolOptimizer(db),
		performanceMonitor:      persistence.NewPerformanceMonitor(),
		queryCacheManager:       persistence.NewQueryCacheManager(),
	}
}

// InitializeOptimizations 初始化数据库优化
func (dos *DatabaseOptimizationService) InitializeOptimizations(ctx context.Context) error {
	logger.Info("Initializing database optimizations...")

	// 1. 创建优化索引
	if err := dos.indexOptimizer.CreateOptimalIndexes(ctx); err != nil {
		logger.Error("Failed to create optimal indexes", zap.Error(err))
		return err
	}

	// 2. 优化连接池
	if err := dos.connectionPoolOptimizer.OptimizeConnectionPool(); err != nil {
		logger.Error("Failed to optimize connection pool", zap.Error(err))
		return err
	}

	// 3. 优化表统计信息
	if err := dos.indexOptimizer.OptimizeTableStatistics(ctx); err != nil {
		logger.Error("Failed to optimize table statistics", zap.Error(err))
		return err
	}

	logger.Info("Database optimizations initialized successfully")
	return nil
}

// AnalyzePerformance 分析数据库性能
func (dos *DatabaseOptimizationService) AnalyzePerformance(ctx context.Context) (*persistence.QueryPerformanceStats, error) {
	logger.Info("Analyzing database performance...")

	stats, err := dos.indexOptimizer.AnalyzeQueryPerformance(ctx)
	if err != nil {
		logger.Error("Failed to analyze query performance", zap.Error(err))
		return nil, err
	}

	logger.Info("Database performance analysis completed",
		zap.Int64("total_queries", stats.TotalQueries),
		zap.Int64("slow_queries", stats.SlowQueries),
		zap.Float64("index_usage_rate", stats.IndexUsageRate),
		zap.Duration("average_time", stats.AverageTime))

	return stats, nil
}

// GetOptimizationSuggestions 获取优化建议
func (dos *DatabaseOptimizationService) GetOptimizationSuggestions(ctx context.Context) (*persistence.QueryOptimizationSuggestions, error) {
	logger.Info("Getting optimization suggestions...")

	suggestions, err := dos.indexOptimizer.GetOptimizationSuggestions(ctx)
	if err != nil {
		logger.Error("Failed to get optimization suggestions", zap.Error(err))
		return nil, err
	}

	logger.Info("Optimization suggestions retrieved",
		zap.Int("missing_indexes", len(suggestions.MissingIndexes)),
		zap.Int("slow_queries", len(suggestions.SlowQueries)),
		zap.Int("optimization_tips", len(suggestions.OptimizationTips)))

	return suggestions, nil
}

// GetConnectionPoolStats 获取连接池统计
func (dos *DatabaseOptimizationService) GetConnectionPoolStats() map[string]interface{} {
	return dos.connectionPoolOptimizer.GetConnectionPoolStats()
}

// OptimizeQuery 优化查询（带缓存）
func (dos *DatabaseOptimizationService) OptimizeQuery(ctx context.Context, sql string, args []interface{}) (interface{}, error) {
	// 生成缓存键
	cacheKey := dos.generateCacheKey(sql, args)

	// 检查缓存
	if result, exists := dos.queryCacheManager.GetCachedQuery(cacheKey); exists {
		logger.Debug("Query result retrieved from cache", zap.String("cache_key", cacheKey))
		return result, nil
	}

	// 执行查询并记录性能
	start := time.Now()
	var result interface{}

	// 这里应该执行实际的查询，简化示例
	err := dos.db.WithContext(ctx).Raw(sql, args...).Scan(&result).Error
	if err != nil {
		return nil, err
	}

	duration := time.Since(start)

	// 记录查询性能
	optimizedQuery := persistence.OptimizedQuery{
		SQL:      sql,
		Args:     args,
		Duration: duration,
		Rows:     1, // 简化，实际应该获取真实行数
	}
	dos.performanceMonitor.RecordQuery(optimizedQuery)

	// 缓存结果（如果查询时间较短，说明是常用查询）
	if duration < 100*time.Millisecond {
		dos.queryCacheManager.CacheQuery(cacheKey, result)
	}

	logger.Debug("Query executed and cached",
		zap.String("sql", sql),
		zap.Duration("duration", duration))

	return result, nil
}

// GetPerformanceStats 获取性能统计
func (dos *DatabaseOptimizationService) GetPerformanceStats() persistence.QueryPerformanceStats {
	return dos.performanceMonitor.GetStats()
}

// GetSlowQueries 获取慢查询列表
func (dos *DatabaseOptimizationService) GetSlowQueries(threshold time.Duration) []persistence.OptimizedQuery {
	return dos.performanceMonitor.GetSlowQueries(threshold)
}

// ClearQueryCache 清空查询缓存
func (dos *DatabaseOptimizationService) ClearQueryCache() {
	dos.queryCacheManager.ClearCache()
	logger.Info("Query cache cleared")
}

// ScheduleOptimizationTasks 调度优化任务
func (dos *DatabaseOptimizationService) ScheduleOptimizationTasks(ctx context.Context) {
	// 每小时执行一次表统计优化
	go func() {
		ticker := time.NewTicker(1 * time.Hour)
		defer ticker.Stop()

		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				if err := dos.indexOptimizer.OptimizeTableStatistics(ctx); err != nil {
					logger.Error("Scheduled table statistics optimization failed", zap.Error(err))
				} else {
					logger.Info("Scheduled table statistics optimization completed")
				}
			}
		}
	}()

	// 每6小时清空查询缓存
	go func() {
		ticker := time.NewTicker(6 * time.Hour)
		defer ticker.Stop()

		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				dos.ClearQueryCache()
			}
		}
	}()

	logger.Info("Database optimization tasks scheduled")
}

// generateCacheKey 生成缓存键
func (dos *DatabaseOptimizationService) generateCacheKey(sql string, args []interface{}) string {
	// 简化的缓存键生成逻辑
	key := sql
	for _, arg := range args {
		key += "_" + toString(arg)
	}
	return key
}

// toString 将任意类型转换为字符串
func toString(v interface{}) string {
	if v == nil {
		return "nil"
	}
	switch val := v.(type) {
	case string:
		return val
	case int:
		return string(rune(val))
	case int64:
		return string(rune(val))
	case float64:
		return string(rune(int(val)))
	default:
		return "unknown"
	}
}

// DatabaseHealthCheck 数据库健康检查
func (dos *DatabaseOptimizationService) DatabaseHealthCheck(ctx context.Context) map[string]interface{} {
	health := make(map[string]interface{})

	// 检查数据库连接
	sqlDB, err := dos.db.DB()
	if err != nil {
		health["connection"] = map[string]interface{}{
			"status": "error",
			"error":  err.Error(),
		}
		return health
	}

	// Ping数据库
	if err := sqlDB.PingContext(ctx); err != nil {
		health["connection"] = map[string]interface{}{
			"status": "error",
			"error":  err.Error(),
		}
		return health
	}

	health["connection"] = map[string]interface{}{
		"status": "healthy",
	}

	// 获取连接池统计
	health["connection_pool"] = dos.GetConnectionPoolStats()

	// 获取性能统计
	perfStats := dos.GetPerformanceStats()
	health["performance"] = map[string]interface{}{
		"total_queries":    perfStats.TotalQueries,
		"slow_queries":     perfStats.SlowQueries,
		"average_time_ms":  perfStats.AverageTime.Milliseconds(),
		"max_time_ms":      perfStats.MaxTime.Milliseconds(),
		"min_time_ms":      perfStats.MinTime.Milliseconds(),
		"cache_hit_rate":   perfStats.CacheHitRate,
		"index_usage_rate": perfStats.IndexUsageRate,
	}

	return health
}

// OptimizeSpecificTable 优化特定表
func (dos *DatabaseOptimizationService) OptimizeSpecificTable(ctx context.Context, tableName string) error {
	logger.Info("Optimizing specific table", zap.String("table", tableName))

	dbType := dos.db.Dialector.Name()

	switch dbType {
	case "postgres":
		sql := "ANALYZE " + tableName
		if err := dos.db.WithContext(ctx).Exec(sql).Error; err != nil {
			return err
		}
	case "mysql":
		sql := "ANALYZE TABLE " + tableName
		if err := dos.db.WithContext(ctx).Exec(sql).Error; err != nil {
			return err
		}
	case "sqlite":
		sql := "ANALYZE " + tableName
		if err := dos.db.WithContext(ctx).Exec(sql).Error; err != nil {
			return err
		}
	}

	logger.Info("Table optimization completed", zap.String("table", tableName))
	return nil
}

// GetDatabaseMetrics 获取数据库指标
func (dos *DatabaseOptimizationService) GetDatabaseMetrics(ctx context.Context) map[string]interface{} {
	metrics := make(map[string]interface{})

	// 获取表大小信息
	dbType := dos.db.Dialector.Name()

	switch dbType {
	case "postgres":
		var tableStats []struct {
			TableName string `gorm:"column:table_name"`
			RowCount  int64  `gorm:"column:row_count"`
			TableSize string `gorm:"column:table_size"`
		}

		dos.db.WithContext(ctx).Raw(`
			SELECT 
				schemaname||'.'||tablename as table_name,
				n_tup_ins + n_tup_upd + n_tup_del as row_count,
				pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as table_size
			FROM pg_stat_user_tables 
			ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
			LIMIT 10
		`).Scan(&tableStats)

		metrics["table_stats"] = tableStats

	case "mysql":
		var tableStats []struct {
			TableName string `gorm:"column:table_name"`
			RowCount  int64  `gorm:"column:row_count"`
			TableSize int64  `gorm:"column:table_size"`
		}

		dos.db.WithContext(ctx).Raw(`
			SELECT 
				table_name,
				table_rows as row_count,
				data_length + index_length as table_size
			FROM information_schema.tables 
			WHERE table_schema = DATABASE()
			ORDER BY data_length + index_length DESC
			LIMIT 10
		`).Scan(&tableStats)

		metrics["table_stats"] = tableStats
	}

	// 添加连接池指标
	metrics["connection_pool"] = dos.GetConnectionPoolStats()

	// 添加性能指标
	perfStats := dos.GetPerformanceStats()
	metrics["performance"] = perfStats

	return metrics
}
