package services

import (
	"context"
	"time"
	"github.com/azel-ko/final-ddd/internal/application/dto"
	"github.com/azel-ko/final-ddd/internal/application/errors"
	"github.com/azel-ko/final-ddd/internal/domain/entities"
	"github.com/azel-ko/final-ddd/internal/domain/repository"
	"github.com/azel-ko/final-ddd/internal/infrastructure/cache"
	"github.com/azel-ko/final-ddd/pkg/auth"
	"github.com/azel-ko/final-ddd/internal/pkg/logger"
	"go.uber.org/zap"
)

type AuthService struct {
	repo           repository.Repository
	jwtManager     *auth.JWTManager
	cache          *cache.RedisCache
	sessionService *SessionService
}

func NewAuthService(repo repository.Repository, jwtManager *auth.JWTManager, cache *cache.RedisCache, sessionService *SessionService) *AuthService {
	return &AuthService{
		repo:           repo,
		jwtManager:     jwtManager,
		cache:          cache,
		sessionService: sessionService,
	}
}

func (s *AuthService) Login(ctx context.Context, req *dto.LoginRequest, ipAddress string) (*dto.LoginResponse, error) {
	user, err := s.repo.GetUserByEmail(req.Email)
	if err != nil {
		logger.Error("login failed: user not found", zap.String("email", req.Email))
		return nil, err
	}

	if err := auth.CheckPassword(req.Password, user.Password); err != nil {
		logger.Error("login failed: invalid password", zap.String("email", req.Email))
		return nil, err
	}

	// Set default values for device info if not provided
	deviceID := req.DeviceID
	if deviceID == "" {
		deviceID = "unknown-device"
	}
	
	platform := req.Platform
	if platform == "" {
		platform = "web"
	}

	// Validate device access (check device limits)
	if s.sessionService != nil {
		if err := s.sessionService.ValidateDeviceAccess(ctx, user.ID, deviceID, platform); err != nil {
			logger.Error("device access validation failed", 
				zap.String("email", req.Email),
				zap.String("device_id", deviceID),
				zap.Error(err))
			return nil, err
		}
	}

	// Create new session
	var session *entities.UserSession
	if s.sessionService != nil {
		session, err = s.sessionService.CreateSession(ctx, user.ID, deviceID, platform, ipAddress, req.UserAgent)
		if err != nil {
			logger.Error("failed to create session", zap.Error(err))
			return nil, err
		}
	}

	// Generate JWT token with session information
	sessionID := ""
	if session != nil {
		sessionID = session.ID
	}
	
	token, err := s.jwtManager.GenerateTokenWithDevice(user.ID, user.Email, user.Role, deviceID, platform, sessionID)
	if err != nil {
		logger.Error("failed to generate token", zap.Error(err))
		return nil, err
	}

	// Update last login timestamp
	now := time.Now()
	user.LastLoginAt = &now
	if err := s.repo.UpdateUser(user); err != nil {
		logger.Error("failed to update last login time", zap.Error(err))
		// Don't fail login if we can't update last login time
	}

	expiresAt := time.Now().Add(8 * time.Hour).Unix()
	response := dto.ToLoginResponse(token, sessionID, expiresAt, user)

	logger.Info("user logged in successfully", 
		zap.String("email", req.Email),
		zap.String("platform", platform),
		zap.String("device_id", deviceID),
		zap.String("session_id", sessionID))
	
	return response, nil
}

// Register 用户注册
func (s *AuthService) Register(req *dto.UserRequest) (*dto.UserResponse, error) {
	if _, err := s.repo.GetUserByEmail(req.Email); err == nil {
		return nil, errors.ErrEmailAlreadyExists
	}

	user, err := dto.ToUserEntity(req)
	if err != nil {
		return nil, err
	}
	if err := s.repo.CreateUser(user); err != nil {
		return nil, err
	}

	return dto.ToUserResponse(user), nil
}
