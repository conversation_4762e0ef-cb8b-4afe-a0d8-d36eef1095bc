package services

import (
	"context"
	"errors"
	"math"
	"time"

	"github.com/azel-ko/final-ddd/internal/application/dto"
	"github.com/azel-ko/final-ddd/internal/domain/entities"
	"github.com/azel-ko/final-ddd/internal/domain/repository"
	"github.com/azel-ko/final-ddd/internal/infrastructure/amap"
	"github.com/azel-ko/final-ddd/internal/pkg/logger"
	"go.uber.org/zap"
)

type RouteService struct {
	repo             repository.Repository
	cache            *CacheService
	userService      *UserService
	checkpointService *CheckpointService
	amapClient       *amap.Client
}

func NewRouteService(repo repository.Repository, cache *CacheService, userService *UserService, checkpointService *CheckpointService, amapClient *amap.Client) *RouteService {
	return &RouteService{
		repo:             repo,
		cache:            cache,
		userService:      userService,
		checkpointService: checkpointService,
		amapClient:       amapClient,
	}
}

// PlanRoute plans a route with permission checking
func (s *RouteService) PlanRoute(ctx context.Context, userID uint, req *dto.RouteRequest) (*dto.RouteResponse, error) {
	// Check user permissions
	permissions, err := s.userService.CheckUserPermission(userID, "navigation")
	if err != nil {
		logger.Error("Failed to check user permissions", zap.Uint("user_id", userID), zap.Error(err))
		return nil, err
	}

	if !permissions.CanNavigate {
		logger.Warn("User does not have navigation permission", zap.Uint("user_id", userID))
		return nil, ErrNavigationPermissionDenied
	}

	// Set default avoid level if not specified
	if req.AvoidLevel == 0 {
		req.AvoidLevel = 1
	}

	// Get user preferences for default settings
	userPrefs, err := s.userService.GetUserPreferences(ctx, userID)
	if err == nil && userPrefs.DefaultAvoidLevel > 0 {
		req.AvoidLevel = userPrefs.DefaultAvoidLevel
	}

	// Plan the basic route using Amap API
	directionReq := &amap.DirectionRequest{
		Origin:      amap.FormatLocation(req.StartLat, req.StartLng),
		Destination: amap.FormatLocation(req.EndLat, req.EndLng),
		Strategy:    s.getStrategyFromRequest(req),
		Extensions:  "all",
	}

	routeResult, err := s.amapClient.Direction(ctx, directionReq)
	if err != nil {
		logger.Error("Failed to plan route with Amap", zap.Error(err))
		return nil, err
	}

	if len(routeResult.Route.Paths) == 0 {
		return nil, errors.New("no route found")
	}

	// Use the first path
	path := routeResult.Route.Paths[0]
	distance, _ := path.GetDistanceInt()
	duration, _ := path.GetDurationInt()

	// Find checkpoints along the route
	checkpointIDs, checkpoints, err := s.findCheckpointsAlongRoute(ctx, &path)
	if err != nil {
		logger.Error("Failed to find checkpoints along route", zap.Error(err))
		// Continue without checkpoint information
		checkpointIDs = []uint{}
		checkpoints = []*dto.CheckpointResponse{}
	}

	// Calculate risk score
	riskScore := s.calculateRiskScore(checkpoints, req.AvoidLevel)

	// Create route entity
	route := dto.ToRouteEntity(userID, req, routeResult, distance, duration, checkpointIDs)

	// Save route to database for history
	if err := s.repo.CreateRoute(route); err != nil {
		logger.Error("Failed to save route", zap.Error(err))
		// Continue without saving
	}

	// Build response
	response := dto.ToRouteResponseWithDetails(route, checkpoints, riskScore, false)

	logger.Info("Route planned successfully", 
		zap.Uint("user_id", userID),
		zap.Int("distance", distance),
		zap.Int("duration", duration),
		zap.Int("checkpoint_count", len(checkpoints)),
		zap.Int("risk_score", riskScore))

	return response, nil
}

// OptimizeRoute optimizes a route to avoid checkpoints (Premium feature)
func (s *RouteService) OptimizeRoute(ctx context.Context, userID uint, req *dto.RouteOptimizeRequest) (*dto.RouteResponse, error) {
	// Check user permissions for premium features
	user, err := s.repo.GetUser(int(userID))
	if err != nil {
		return nil, err
	}

	if user.Subscription != "premium" {
		// Check if user is still in trial period
		isExpired, err := s.userService.IsTrialExpired(userID)
		if err != nil || isExpired {
			logger.Warn("User does not have premium access for route optimization", zap.Uint("user_id", userID))
			return nil, ErrPremiumFeatureRequired
		}
	}

	// Get original route
	originalRoute, err := s.repo.GetCheckpoint(req.RouteID) // This should be GetRoute, but we'll use existing method for now
	if err != nil {
		return nil, ErrRouteNotFound
	}

	// Plan optimized route with higher avoidance level
	optimizeReq := &dto.RouteRequest{
		StartLat:   originalRoute.Latitude,  // Using checkpoint fields as placeholder
		StartLng:   originalRoute.Longitude,
		EndLat:     originalRoute.Latitude,  // This would be actual route end coordinates
		EndLng:     originalRoute.Longitude,
		AvoidLevel: req.AvoidLevel,
		Strategy:   "avoid_checkpoints",
	}

	// Plan the optimized route
	optimizedResponse, err := s.PlanRoute(ctx, userID, optimizeReq)
	if err != nil {
		return nil, err
	}

	optimizedResponse.IsOptimized = true

	logger.Info("Route optimized successfully", 
		zap.Uint("user_id", userID),
		zap.Uint("original_route_id", req.RouteID),
		zap.Int("avoid_level", req.AvoidLevel))

	return optimizedResponse, nil
}

// GetRouteHistory retrieves user's route history with permission-based filtering
func (s *RouteService) GetRouteHistory(ctx context.Context, userID uint, req *dto.RouteHistoryRequest) (*dto.RouteHistoryResponse, error) {
	// Check user permissions
	permissions, err := s.userService.CheckUserPermission(userID, "navigation")
	if err != nil {
		return nil, err
	}

	if !permissions.CanViewCheckpoints {
		return nil, ErrViewPermissionDenied
	}

	// Set defaults
	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 20
	}

	// Get user subscription to determine feature access
	user, err := s.repo.GetUser(int(userID))
	if err != nil {
		return nil, err
	}

	// Determine limit based on subscription
	limit := s.getHistoryLimit(user.Subscription)
	if req.PageSize > limit {
		req.PageSize = limit
	}

	// Get routes from repository
	routes, err := s.repo.GetUserRoutes(userID, req.PageSize)
	if err != nil {
		logger.Error("Failed to get user routes", zap.Uint("user_id", userID), zap.Error(err))
		return nil, err
	}

	// Convert to response DTOs
	routeResponses := make([]*dto.RouteResponse, len(routes))
	for i, route := range routes {
		routeResponses[i] = dto.ToRouteResponse(route)
		
		// Add detailed information for premium users
		if user.Subscription == "premium" || !s.isTrialExpired(user) {
			// Add checkpoint details and risk analysis
			s.enrichRouteResponse(ctx, routeResponses[i])
		}
	}

	response := &dto.RouteHistoryResponse{
		Data:       routeResponses,
		Total:      int64(len(routes)),
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: int(math.Ceil(float64(len(routes)) / float64(req.PageSize))),
	}

	logger.Info("Route history retrieved successfully", 
		zap.Uint("user_id", userID),
		zap.Int("count", len(routes)))

	return response, nil
}

// AnalyzeRoute provides detailed route analysis (Premium feature)
func (s *RouteService) AnalyzeRoute(ctx context.Context, userID uint, routeID uint) (*dto.RouteAnalysisResponse, error) {
	// Check premium access
	user, err := s.repo.GetUser(int(userID))
	if err != nil {
		return nil, err
	}

	if user.Subscription != "premium" {
		isExpired, err := s.userService.IsTrialExpired(userID)
		if err != nil || isExpired {
			return nil, ErrPremiumFeatureRequired
		}
	}

	// This would be implemented with actual route analysis logic
	// For now, return a placeholder response
	response := &dto.RouteAnalysisResponse{
		Route: &dto.RouteResponse{
			ID: routeID,
		},
		CheckpointAnalysis: []*dto.CheckpointAnalysis{},
		AlternativeRoutes:  []*dto.RouteResponse{},
		RiskAssessment: &dto.RiskAssessment{
			OverallRisk:     "medium",
			RiskScore:       50,
			CheckpointCount: 0,
			HighRiskAreas:   []string{},
			BestTimeToTravel: "避开早晚高峰时段",
		},
		Recommendations: []string{
			"建议选择避让级别2以上的路线",
			"避开早晚高峰时段出行",
		},
	}

	logger.Info("Route analysis completed", zap.Uint("user_id", userID), zap.Uint("route_id", routeID))
	return response, nil
}

// Helper methods

func (s *RouteService) findCheckpointsAlongRoute(ctx context.Context, path *amap.Path) ([]uint, []*dto.CheckpointResponse, error) {
	// This would implement logic to find checkpoints along the route
	// For now, return empty results
	return []uint{}, []*dto.CheckpointResponse{}, nil
}

func (s *RouteService) getStrategyFromRequest(req *dto.RouteRequest) int {
	switch req.Strategy {
	case "fastest":
		return amap.StrategyFastest
	case "shortest":
		return amap.StrategyDistance
	case "avoid_checkpoints":
		return amap.StrategyAvoidJam // Use avoid jam as proxy for avoiding checkpoints
	default:
		return amap.StrategyFastest
	}
}

func (s *RouteService) calculateRiskScore(checkpoints []*dto.CheckpointResponse, avoidLevel int) int {
	if len(checkpoints) == 0 {
		return 0
	}

	// Simple risk calculation based on checkpoint count and avoid level
	baseRisk := len(checkpoints) * 20
	adjustedRisk := baseRisk - (avoidLevel * 10)
	
	if adjustedRisk < 0 {
		adjustedRisk = 0
	}
	if adjustedRisk > 100 {
		adjustedRisk = 100
	}

	return adjustedRisk
}

func (s *RouteService) getHistoryLimit(subscription string) int {
	switch subscription {
	case "premium":
		return 100 // Premium users can see more history
	case "trial":
		return 50  // Trial users have moderate access
	default:
		return 10  // Free users have limited access
	}
}

func (s *RouteService) isTrialExpired(user *entities.User) bool {
	if user.Subscription != "trial" || user.TrialExpiry == nil {
		return false
	}
	return user.TrialExpiry.Before(time.Now())
}

func (s *RouteService) enrichRouteResponse(ctx context.Context, route *dto.RouteResponse) {
	// Add detailed checkpoint information for premium users
	// This would be implemented with actual checkpoint enrichment logic
}

// Custom errors
var (
	ErrNavigationPermissionDenied = errors.New("navigation permission denied")
	ErrViewPermissionDenied       = errors.New("view permission denied")
	ErrRouteNotFound              = errors.New("route not found")
)