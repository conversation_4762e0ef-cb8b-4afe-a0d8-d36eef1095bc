package services

import (
	"context"
	"errors"
	"time"
	"github.com/azel-ko/final-ddd/internal/application/dto"
	apperrors "github.com/azel-ko/final-ddd/internal/application/errors"
	"github.com/azel-ko/final-ddd/internal/domain/repository"
	"github.com/azel-ko/final-ddd/pkg/auth"
)

type UserService struct {
	repo  repository.Repository
	cache *CacheService
}

func NewUserService(repo repository.Repository, cache *CacheService) *UserService {
	return &UserService{repo: repo, cache: cache}
}

func (s *UserService) CreateUser(req *dto.UserRequest) (*dto.UserResponse, error) {
	if _, err := s.repo.GetUserByEmail(req.Email); err == nil {
		return nil, apperrors.ErrEmailAlreadyExists
	}

	user, err := dto.ToUserEntity(req)
	if err != nil {
		return nil, err
	}
	if err := s.repo.CreateUser(user); err != nil {
		return nil, err
	}

	// Initialize default preferences for new user
	if err := s.InitializeUserPreferences(context.Background(), user.ID); err != nil {
		// Log error but don't fail user creation
		// In production, you might want to handle this differently
	}

	return dto.ToUserResponse(user), nil
}

func (s *UserService) GetUser(id int) (*dto.UserResponse, error) {
	user, err := s.repo.GetUser(id)
	if err != nil {
		return nil, apperrors.ErrNotFound
	}
	return dto.ToUserResponse(user), nil
}

func (s *UserService) UpdateUser(id int, req *dto.UserRequest) (*dto.UserResponse, error) {
	user, err := s.repo.GetUser(id)
	if err != nil {
		return nil, apperrors.ErrNotFound
	}

	user.Name = req.Name
	user.Email = req.Email
	if req.Password != "" {
		// 使用 bcrypt 加密密码
		hashedPassword, err := auth.HashPassword(req.Password)
		if err != nil {
			return nil, errors.New("failed to hash password")
		}
		user.Password = hashedPassword
	}

	if err := s.repo.UpdateUser(user); err != nil {
		return nil, err
	}

	return dto.ToUserResponse(user), nil
}

func (s *UserService) DeleteUser(id int) error {
	return s.repo.DeleteUser(id)
}

func (s *UserService) GetSelf(userID uint) (*dto.UserProfileResponse, error) {
	user, err := s.repo.GetUser(int(userID))
	if err != nil {
		return nil, apperrors.ErrNotFound
	}
	return dto.ToUserProfileResponse(user), nil
}

func (s *UserService) UpdateSelf(userID uint, req *dto.UpdateUserProfileRequest) (*dto.UserProfileResponse, error) {
	user, err := s.repo.GetUser(int(userID))
	if err != nil {
		return nil, apperrors.ErrNotFound
	}

	if req.Name != nil {
		user.Name = *req.Name
	}

	if req.Email != nil {
		if *req.Email != user.Email {
			// Check if the new email already exists for another user
			existingUser, err := s.repo.GetUserByEmail(*req.Email)
			if err == nil && existingUser.ID != userID { // Email exists and belongs to another user
				return nil, apperrors.ErrEmailAlreadyExists
			}
			user.Email = *req.Email
		}
	}

	if req.CarPlate != nil {
		// Validate car plate before updating
		if err := s.ValidateCarPlate(*req.CarPlate, user.PlateRegion); err != nil {
			return nil, err
		}
		user.CarPlate = *req.CarPlate
	}

	if req.PlateRegion != nil {
		// Validate car plate with new region if car plate exists
		if user.CarPlate != "" {
			if err := s.ValidateCarPlate(user.CarPlate, *req.PlateRegion); err != nil {
				return nil, err
			}
		}
		user.PlateRegion = *req.PlateRegion
	}

	if req.Preferences != nil {
		user.Preferences = *req.Preferences
	}

	if err := s.repo.UpdateUserProfile(user); err != nil {
		return nil, err
	}

	return dto.ToUserProfileResponse(user), nil
}

// Permission represents user's access permissions
type Permission struct {
	CanNavigate        bool `json:"can_navigate"`
	CanViewCheckpoints bool `json:"can_view_checkpoints"`
}

// CheckUserPermission checks what features the user can access based on their subscription
func (s *UserService) CheckUserPermission(userID uint, feature string) (*Permission, error) {
	user, err := s.repo.GetUser(int(userID))
	if err != nil {
		return nil, err
	}
	
	now := time.Now()
	
	// Trial period - all features available
	if user.Subscription == "trial" && user.TrialExpiry != nil && now.Before(*user.TrialExpiry) {
		return &Permission{CanNavigate: true, CanViewCheckpoints: true}, nil
	}
	
	// Trial expired or free user - only checkpoint viewing
	if user.Subscription == "free" || (user.Subscription == "trial" && (user.TrialExpiry == nil || now.After(*user.TrialExpiry))) {
		return &Permission{CanNavigate: false, CanViewCheckpoints: true}, nil
	}
	
	// Premium user - all features available
	if user.Subscription == "premium" {
		return &Permission{CanNavigate: true, CanViewCheckpoints: true}, nil
	}
	
	return &Permission{CanNavigate: false, CanViewCheckpoints: false}, nil
}

// GetSubscriptionStatus returns the user's current subscription status
func (s *UserService) GetSubscriptionStatus(userID uint) (*dto.UserProfileResponse, error) {
	user, err := s.repo.GetUser(int(userID))
	if err != nil {
		return nil, apperrors.ErrNotFound
	}
	return dto.ToUserProfileResponse(user), nil
}

// UpgradeSubscription upgrades user's subscription to premium
func (s *UserService) UpgradeSubscription(userID uint, subscriptionType string) error {
	user, err := s.repo.GetUser(int(userID))
	if err != nil {
		return apperrors.ErrNotFound
	}
	
	user.Subscription = subscriptionType
	// Clear trial expiry for premium users
	if subscriptionType == "premium" {
		user.TrialExpiry = nil
	}
	
	return s.repo.UpdateUser(user)
}

// UpdateLastLogin updates the user's last login timestamp
func (s *UserService) UpdateLastLogin(userID uint) error {
	user, err := s.repo.GetUser(int(userID))
	if err != nil {
		return err
	}
	
	now := time.Now()
	user.LastLoginAt = &now
	
	return s.repo.UpdateUser(user)
}

// GetUserPreferences retrieves user preferences with caching
func (s *UserService) GetUserPreferences(ctx context.Context, userID uint) (*dto.UserPreferenceResponse, error) {
	// Try to get from cache first
	if s.cache != nil {
		if cachedPreference, err := s.cache.GetUserPreference(ctx, userID); err == nil {
			return dto.ToUserPreferenceResponse(cachedPreference)
		}
	}
	
	// Get from database
	preference, err := s.repo.GetUserPreference(userID)
	if err != nil {
		// If not found, create default preferences
		defaultPreference := dto.GetDefaultUserPreference(userID)
		if createErr := s.repo.CreateOrUpdateUserPreference(defaultPreference); createErr != nil {
			return nil, createErr
		}
		preference = defaultPreference
	}
	
	// Cache the result
	if s.cache != nil {
		s.cache.SetUserPreference(ctx, preference)
	}
	
	return dto.ToUserPreferenceResponse(preference)
}

// UpdateUserPreferences updates user preferences
func (s *UserService) UpdateUserPreferences(ctx context.Context, userID uint, req *dto.UserPreferenceRequest) (*dto.UserPreferenceResponse, error) {
	// Convert DTO to entity
	preference, err := dto.ToUserPreferenceEntity(userID, req)
	if err != nil {
		return nil, err
	}
	
	// Check if preference exists
	existingPreference, err := s.repo.GetUserPreference(userID)
	if err == nil {
		// Update existing preference
		preference.ID = existingPreference.ID
	}
	
	// Save to database
	if err := s.repo.CreateOrUpdateUserPreference(preference); err != nil {
		return nil, err
	}
	
	// Update cache
	if s.cache != nil {
		s.cache.SetUserPreference(ctx, preference)
	}
	
	return dto.ToUserPreferenceResponse(preference)
}

// InitializeUserPreferences creates default preferences for a new user
func (s *UserService) InitializeUserPreferences(ctx context.Context, userID uint) error {
	defaultPreference := dto.GetDefaultUserPreference(userID)
	
	if err := s.repo.CreateOrUpdateUserPreference(defaultPreference); err != nil {
		return err
	}
	
	// Cache the default preferences
	if s.cache != nil {
		s.cache.SetUserPreference(ctx, defaultPreference)
	}
	
	return nil
}

// IsTrialExpired checks if user's trial period has expired
func (s *UserService) IsTrialExpired(userID uint) (bool, error) {
	user, err := s.repo.GetUser(int(userID))
	if err != nil {
		return false, err
	}
	
	if user.Subscription != "trial" {
		return false, nil // Not a trial user
	}
	
	if user.TrialExpiry == nil {
		return true, nil // No expiry date set, consider expired
	}
	
	return time.Now().After(*user.TrialExpiry), nil
}

// GetTrialRemainingDays returns the number of days remaining in trial
func (s *UserService) GetTrialRemainingDays(userID uint) (int, error) {
	user, err := s.repo.GetUser(int(userID))
	if err != nil {
		return 0, err
	}
	
	if user.Subscription != "trial" || user.TrialExpiry == nil {
		return 0, nil
	}
	
	remaining := time.Until(*user.TrialExpiry)
	if remaining <= 0 {
		return 0, nil
	}
	
	return int(remaining.Hours() / 24), nil
}

// ValidateCarPlate validates car plate format
func (s *UserService) ValidateCarPlate(carPlate, plateRegion string) error {
	if carPlate == "" {
		return errors.New("car plate cannot be empty")
	}
	
	if plateRegion == "" {
		return errors.New("plate region cannot be empty")
	}
	
	// Basic validation - can be enhanced with more specific rules
	if len(carPlate) < 6 || len(carPlate) > 8 {
		return errors.New("invalid car plate format")
	}
	
	return nil
}