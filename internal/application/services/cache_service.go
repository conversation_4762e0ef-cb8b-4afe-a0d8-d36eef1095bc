package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/azel-ko/final-ddd/internal/domain/entities"
	"github.com/azel-ko/final-ddd/internal/infrastructure/cache"
	"github.com/azel-ko/final-ddd/internal/pkg/logger"
	"go.uber.org/zap"
)

// CacheService 缓存服务
type CacheService struct {
	cache *cache.RedisCache
}

// NewCacheService 创建缓存服务
func NewCacheService(cache *cache.RedisCache) *CacheService {
	return &CacheService{
		cache: cache,
	}
}

// CacheKeys 缓存键常量
const (
	CheckpointListKey    = "checkpoints:list:%s"             // 检查站列表缓存
	CheckpointDetailKey  = "checkpoint:detail:%d"            // 检查站详情缓存
	NearbyCheckpointsKey = "checkpoints:nearby:%.6f:%.6f:%d" // 附近检查站缓存
	UserPreferenceKey    = "user:preference:%d"              // 用户偏好缓存
	UserRoutesKey        = "user:routes:%d"                  // 用户路线缓存
	DataUpdateLogKey     = "data:update:log:%s"              // 数据更新日志缓存
)

// 缓存过期时间
const (
	CheckpointCacheTTL   = 10 * time.Minute // 检查站缓存10分钟
	NearbyCheckpointsTTL = 5 * time.Minute  // 附近检查站缓存5分钟
	UserPreferenceTTL    = 30 * time.Minute // 用户偏好缓存30分钟
	UserRoutesTTL        = 15 * time.Minute // 用户路线缓存15分钟
	DataUpdateLogTTL     = 60 * time.Minute // 数据更新日志缓存1小时
)

// GetCheckpointList 获取检查站列表缓存
func (s *CacheService) GetCheckpointList(ctx context.Context, cacheKey string) ([]*entities.Checkpoint, error) {
	var checkpoints []*entities.Checkpoint
	err := s.cache.Get(ctx, cacheKey, &checkpoints)
	if err != nil {
		return nil, err
	}
	return checkpoints, nil
}

// SetCheckpointList 设置检查站列表缓存
func (s *CacheService) SetCheckpointList(ctx context.Context, cacheKey string, checkpoints []*entities.Checkpoint) error {
	return s.cache.Set(ctx, cacheKey, checkpoints, CheckpointCacheTTL)
}

// GetCheckpointDetail 获取检查站详情缓存
func (s *CacheService) GetCheckpointDetail(ctx context.Context, id uint) (*entities.Checkpoint, error) {
	key := fmt.Sprintf(CheckpointDetailKey, id)
	var checkpoint entities.Checkpoint
	err := s.cache.Get(ctx, key, &checkpoint)
	if err != nil {
		return nil, err
	}
	return &checkpoint, nil
}

// SetCheckpointDetail 设置检查站详情缓存
func (s *CacheService) SetCheckpointDetail(ctx context.Context, checkpoint *entities.Checkpoint) error {
	key := fmt.Sprintf(CheckpointDetailKey, checkpoint.ID)
	return s.cache.Set(ctx, key, checkpoint, CheckpointCacheTTL)
}

// GetNearbyCheckpoints 获取附近检查站缓存
func (s *CacheService) GetNearbyCheckpoints(ctx context.Context, lat, lng float64, radius int) ([]*entities.Checkpoint, error) {
	key := fmt.Sprintf(NearbyCheckpointsKey, lat, lng, radius)
	var checkpoints []*entities.Checkpoint
	err := s.cache.Get(ctx, key, &checkpoints)
	if err != nil {
		return nil, err
	}
	return checkpoints, nil
}

// SetNearbyCheckpoints 设置附近检查站缓存
func (s *CacheService) SetNearbyCheckpoints(ctx context.Context, lat, lng float64, radius int, checkpoints []*entities.Checkpoint) error {
	key := fmt.Sprintf(NearbyCheckpointsKey, lat, lng, radius)
	return s.cache.Set(ctx, key, checkpoints, NearbyCheckpointsTTL)
}

// GetUserPreference 获取用户偏好缓存
func (s *CacheService) GetUserPreference(ctx context.Context, userID uint) (*entities.UserPreference, error) {
	key := fmt.Sprintf(UserPreferenceKey, userID)
	var preference entities.UserPreference
	err := s.cache.Get(ctx, key, &preference)
	if err != nil {
		return nil, err
	}
	return &preference, nil
}

// SetUserPreference 设置用户偏好缓存
func (s *CacheService) SetUserPreference(ctx context.Context, preference *entities.UserPreference) error {
	key := fmt.Sprintf(UserPreferenceKey, preference.UserID)
	return s.cache.Set(ctx, key, preference, UserPreferenceTTL)
}

// GetUserRoutes 获取用户路线缓存
func (s *CacheService) GetUserRoutes(ctx context.Context, userID uint) ([]*entities.Route, error) {
	key := fmt.Sprintf(UserRoutesKey, userID)
	var routes []*entities.Route
	err := s.cache.Get(ctx, key, &routes)
	if err != nil {
		return nil, err
	}
	return routes, nil
}

// SetUserRoutes 设置用户路线缓存
func (s *CacheService) SetUserRoutes(ctx context.Context, userID uint, routes []*entities.Route) error {
	key := fmt.Sprintf(UserRoutesKey, userID)
	return s.cache.Set(ctx, key, routes, UserRoutesTTL)
}

// GetDataUpdateLog 获取数据更新日志缓存
func (s *CacheService) GetDataUpdateLog(ctx context.Context, source string) (*entities.DataUpdateLog, error) {
	key := fmt.Sprintf(DataUpdateLogKey, source)
	var log entities.DataUpdateLog
	err := s.cache.Get(ctx, key, &log)
	if err != nil {
		return nil, err
	}
	return &log, nil
}

// SetDataUpdateLog 设置数据更新日志缓存
func (s *CacheService) SetDataUpdateLog(ctx context.Context, log *entities.DataUpdateLog) error {
	key := fmt.Sprintf(DataUpdateLogKey, log.Source)
	return s.cache.Set(ctx, key, log, DataUpdateLogTTL)
}

// InvalidateCheckpointCache 使检查站相关缓存失效
func (s *CacheService) InvalidateCheckpointCache(ctx context.Context) error {
	// 删除所有检查站相关的缓存
	patterns := []string{
		"checkpoints:*",
		"checkpoint:*",
	}

	for _, pattern := range patterns {
		if err := s.cache.DeletePattern(ctx, pattern); err != nil {
			logger.Error("Failed to invalidate cache pattern",
				zap.String("pattern", pattern),
				zap.Error(err))
		}
	}

	logger.Info("Checkpoint cache invalidated")
	return nil
}

// InvalidateUserCache 使用户相关缓存失效
func (s *CacheService) InvalidateUserCache(ctx context.Context, userID uint) error {
	keys := []string{
		fmt.Sprintf(UserPreferenceKey, userID),
		fmt.Sprintf(UserRoutesKey, userID),
	}

	for _, key := range keys {
		if err := s.cache.Delete(ctx, key); err != nil {
			logger.Error("Failed to invalidate user cache",
				zap.String("key", key),
				zap.Error(err))
		}
	}

	logger.Info("User cache invalidated", zap.Uint("user_id", userID))
	return nil
}

// WarmupCache 预热缓存
func (s *CacheService) WarmupCache(ctx context.Context, checkpoints []*entities.Checkpoint) error {
	logger.Info("Starting cache warmup", zap.Int("checkpoint_count", len(checkpoints)))

	// 预热检查站详情缓存
	for _, checkpoint := range checkpoints {
		if err := s.SetCheckpointDetail(ctx, checkpoint); err != nil {
			logger.Error("Failed to warmup checkpoint detail cache",
				zap.Uint("checkpoint_id", checkpoint.ID),
				zap.Error(err))
		}
	}

	// 预热北京地区的检查站列表缓存
	beijingKey := fmt.Sprintf(CheckpointListKey, "beijing")
	beijingCheckpoints := make([]*entities.Checkpoint, 0)
	for _, checkpoint := range checkpoints {
		if checkpoint.Province == "北京" {
			beijingCheckpoints = append(beijingCheckpoints, checkpoint)
		}
	}

	if len(beijingCheckpoints) > 0 {
		if err := s.SetCheckpointList(ctx, beijingKey, beijingCheckpoints); err != nil {
			logger.Error("Failed to warmup Beijing checkpoint list cache", zap.Error(err))
		}
	}

	logger.Info("Cache warmup completed",
		zap.Int("total_checkpoints", len(checkpoints)),
		zap.Int("beijing_checkpoints", len(beijingCheckpoints)))

	return nil
}

// GetCacheStats 获取缓存统计信息
func (s *CacheService) GetCacheStats(ctx context.Context) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 获取各类缓存的键数量
	patterns := map[string]string{
		"checkpoint_details": "checkpoint:detail:*",
		"checkpoint_lists":   "checkpoints:list:*",
		"nearby_checkpoints": "checkpoints:nearby:*",
		"user_preferences":   "user:preference:*",
		"user_routes":        "user:routes:*",
		"data_update_logs":   "data:update:log:*",
	}

	for name, pattern := range patterns {
		count, err := s.cache.CountKeys(ctx, pattern)
		if err != nil {
			logger.Error("Failed to count cache keys",
				zap.String("pattern", pattern),
				zap.Error(err))
			stats[name] = -1
		} else {
			stats[name] = count
		}
	}

	return stats, nil
}

// BuildCacheKey 构建缓存键
func (s *CacheService) BuildCacheKey(prefix string, params ...interface{}) string {
	key := prefix
	for _, param := range params {
		key += fmt.Sprintf(":%v", param)
	}
	return key
}

// SerializeForCache 序列化数据用于缓存
func (s *CacheService) SerializeForCache(data interface{}) ([]byte, error) {
	return json.Marshal(data)
}

// DeserializeFromCache 从缓存反序列化数据
func (s *CacheService) DeserializeFromCache(data []byte, target interface{}) error {
	return json.Unmarshal(data, target)
}

// CheckpointStats 检查站统计信息
type CheckpointStats struct {
	TotalCount    int `json:"total_count"`
	ActiveCount   int `json:"active_count"`
	InactiveCount int `json:"inactive_count"`
	UnknownCount  int `json:"unknown_count"`
}

// GetCheckpointStats 获取检查站统计信息缓存
func (s *CacheService) GetCheckpointStats(ctx context.Context) (*CheckpointStats, error) {
	key := "checkpoint:stats"
	var stats CheckpointStats
	err := s.cache.Get(ctx, key, &stats)
	if err != nil {
		return nil, err
	}
	return &stats, nil
}

// SetCheckpointStats 设置检查站统计信息缓存
func (s *CacheService) SetCheckpointStats(ctx context.Context, stats *CheckpointStats) error {
	key := "checkpoint:stats"
	return s.cache.Set(ctx, key, stats, CheckpointCacheTTL)
}
