package services

import (
	"context"
	"errors"
	"time"

	"github.com/azel-ko/final-ddd/internal/domain/entities"
	"github.com/azel-ko/final-ddd/internal/domain/repository"
	"github.com/azel-ko/final-ddd/internal/pkg/logger"
	"go.uber.org/zap"
)

type PermissionService struct {
	repo           repository.Repository
	cache          *CacheService
	sessionService *SessionService
}

func NewPermissionService(repo repository.Repository, cache *CacheService, sessionService *SessionService) *PermissionService {
	return &PermissionService{
		repo:           repo,
		cache:          cache,
		sessionService: sessionService,
	}
}

// PermissionLevel represents different permission levels
type PermissionLevel int

const (
	PermissionNone PermissionLevel = iota
	PermissionBasic
	PermissionNavigation
	PermissionPremium
	PermissionAdmin
)

// UserPermissions represents comprehensive user permissions
type UserPermissions struct {
	UserID              uint            `json:"user_id"`
	Level               PermissionLevel `json:"level"`
	CanViewCheckpoints  bool            `json:"can_view_checkpoints"`
	CanNavigate         bool            `json:"can_navigate"`
	CanOptimizeRoutes   bool            `json:"can_optimize_routes"`
	CanViewHistory      bool            `json:"can_view_history"`
	CanAnalyzeRoutes    bool            `json:"can_analyze_routes"`
	CanManageDevices    bool            `json:"can_manage_devices"`
	MaxHistoryRecords   int             `json:"max_history_records"`
	MaxDevices          int             `json:"max_devices"`
	IsTrialExpired      bool            `json:"is_trial_expired"`
	TrialRemainingDays  int             `json:"trial_remaining_days"`
	RequiresOnline      bool            `json:"requires_online"`
	FeatureLimitations  map[string]int  `json:"feature_limitations"`
}

// CheckUserPermissions performs comprehensive permission checking
func (s *PermissionService) CheckUserPermissions(ctx context.Context, userID uint) (*UserPermissions, error) {
	user, err := s.repo.GetUser(int(userID))
	if err != nil {
		logger.Error("Failed to get user for permission check", zap.Uint("user_id", userID), zap.Error(err))
		return nil, err
	}

	permissions := &UserPermissions{
		UserID:             userID,
		RequiresOnline:     true, // Always require online verification
		FeatureLimitations: make(map[string]int),
	}

	// Determine permission level based on subscription
	switch user.Subscription {
	case "trial":
		permissions.Level = s.getTrialPermissionLevel(user)
	case "free":
		permissions.Level = PermissionBasic
	case "premium", "monthly", "yearly", "lifetime":
		permissions.Level = PermissionPremium
	default:
		if user.Role == "admin" {
			permissions.Level = PermissionAdmin
		} else {
			permissions.Level = PermissionNone
		}
	}

	// Set specific permissions based on level
	s.setPermissionsByLevel(permissions, user)

	logger.Info("User permissions checked", 
		zap.Uint("user_id", userID),
		zap.String("subscription", user.Subscription),
		zap.Int("level", int(permissions.Level)))

	return permissions, nil
}

// ValidateFeatureAccess validates access to specific features
func (s *PermissionService) ValidateFeatureAccess(ctx context.Context, userID uint, feature string) error {
	permissions, err := s.CheckUserPermissions(ctx, userID)
	if err != nil {
		return err
	}

	switch feature {
	case "navigation":
		if !permissions.CanNavigate {
			return ErrNavigationAccessDenied
		}
	case "route_optimization":
		if !permissions.CanOptimizeRoutes {
			return ErrPremiumFeatureRequired
		}
	case "route_analysis":
		if !permissions.CanAnalyzeRoutes {
			return ErrPremiumFeatureRequired
		}
	case "checkpoint_view":
		if !permissions.CanViewCheckpoints {
			return ErrBasicAccessDenied
		}
	case "history_view":
		if !permissions.CanViewHistory {
			return ErrBasicAccessDenied
		}
	default:
		return ErrUnknownFeature
	}

	// Additional online verification for critical features
	if feature == "navigation" || feature == "route_optimization" {
		if err := s.validateOnlineAccess(ctx, userID); err != nil {
			return err
		}
	}

	return nil
}

// ValidateDeviceAccess validates device access and limits
func (s *PermissionService) ValidateDeviceAccess(ctx context.Context, userID uint, deviceID, platform string) error {
	permissions, err := s.CheckUserPermissions(ctx, userID)
	if err != nil {
		return err
	}

	// Get user's active sessions
	sessions, err := s.repo.GetUserSessions(userID)
	if err != nil {
		return err
	}

	// Count unique active devices
	deviceMap := make(map[string]bool)
	for _, session := range sessions {
		if session.IsActive && !session.IsExpired() {
			deviceMap[session.DeviceID] = true
		}
	}

	// Check if device already exists
	if deviceMap[deviceID] {
		return nil // Device already registered
	}

	// Check device limit
	if len(deviceMap) >= permissions.MaxDevices {
		return ErrDeviceLimitExceeded
	}

	// Platform-specific restrictions
	if err := s.validatePlatformAccess(permissions, platform); err != nil {
		return err
	}

	return nil
}

// EnforceTrialLimitations enforces trial period limitations
func (s *PermissionService) EnforceTrialLimitations(ctx context.Context, userID uint) error {
	user, err := s.repo.GetUser(int(userID))
	if err != nil {
		return err
	}

	if user.Subscription != "trial" {
		return nil // Not a trial user
	}

	// Check if trial has expired
	if user.TrialExpiry != nil && time.Now().After(*user.TrialExpiry) {
		// Automatically downgrade to free plan
		user.Subscription = "free"
		user.TrialExpiry = nil

		if err := s.repo.UpdateUser(user); err != nil {
			logger.Error("Failed to downgrade expired trial user", zap.Uint("user_id", userID), zap.Error(err))
			return err
		}

		// Invalidate user cache
		if s.cache != nil {
			s.cache.InvalidateUserCache(ctx, userID)
		}

		logger.Info("Trial user automatically downgraded to free", zap.Uint("user_id", userID))
		return ErrTrialExpired
	}

	return nil
}

// Helper methods

func (s *PermissionService) getTrialPermissionLevel(user *entities.User) PermissionLevel {
	if user.TrialExpiry != nil && time.Now().After(*user.TrialExpiry) {
		return PermissionBasic // Expired trial = basic permissions
	}
	return PermissionNavigation // Active trial = navigation permissions
}

func (s *PermissionService) setPermissionsByLevel(permissions *UserPermissions, user *entities.User) {
	switch permissions.Level {
	case PermissionNone:
		// No permissions
		permissions.CanViewCheckpoints = false
		permissions.CanNavigate = false
		permissions.CanOptimizeRoutes = false
		permissions.CanViewHistory = false
		permissions.CanAnalyzeRoutes = false
		permissions.MaxHistoryRecords = 0
		permissions.MaxDevices = 1

	case PermissionBasic:
		// Free user permissions
		permissions.CanViewCheckpoints = true
		permissions.CanNavigate = false
		permissions.CanOptimizeRoutes = false
		permissions.CanViewHistory = true
		permissions.CanAnalyzeRoutes = false
		permissions.MaxHistoryRecords = 10
		permissions.MaxDevices = 2
		permissions.FeatureLimitations["daily_requests"] = 50

	case PermissionNavigation:
		// Trial user permissions
		permissions.CanViewCheckpoints = true
		permissions.CanNavigate = true
		permissions.CanOptimizeRoutes = false
		permissions.CanViewHistory = true
		permissions.CanAnalyzeRoutes = false
		permissions.MaxHistoryRecords = 50
		permissions.MaxDevices = 3
		permissions.FeatureLimitations["daily_requests"] = 200
		
		// Set trial information
		if user.Subscription == "trial" {
			permissions.IsTrialExpired = user.TrialExpiry != nil && time.Now().After(*user.TrialExpiry)
			if user.TrialExpiry != nil {
				remaining := time.Until(*user.TrialExpiry)
				permissions.TrialRemainingDays = int(remaining.Hours() / 24)
				if permissions.TrialRemainingDays < 0 {
					permissions.TrialRemainingDays = 0
				}
			}
		}

	case PermissionPremium:
		// Premium user permissions
		permissions.CanViewCheckpoints = true
		permissions.CanNavigate = true
		permissions.CanOptimizeRoutes = true
		permissions.CanViewHistory = true
		permissions.CanAnalyzeRoutes = true
		permissions.CanManageDevices = true
		permissions.MaxHistoryRecords = -1 // Unlimited
		permissions.MaxDevices = 5
		permissions.FeatureLimitations["daily_requests"] = -1 // Unlimited

	case PermissionAdmin:
		// Admin permissions
		permissions.CanViewCheckpoints = true
		permissions.CanNavigate = true
		permissions.CanOptimizeRoutes = true
		permissions.CanViewHistory = true
		permissions.CanAnalyzeRoutes = true
		permissions.CanManageDevices = true
		permissions.MaxHistoryRecords = -1 // Unlimited
		permissions.MaxDevices = -1        // Unlimited
		permissions.FeatureLimitations["daily_requests"] = -1 // Unlimited
	}
}

func (s *PermissionService) validateOnlineAccess(ctx context.Context, userID uint) error {
	// In a real implementation, this would check:
	// 1. Network connectivity
	// 2. Server reachability
	// 3. License validation
	// 4. Anti-tampering checks
	
	// For now, we'll assume online access is always required and available
	return nil
}

func (s *PermissionService) validatePlatformAccess(permissions *UserPermissions, platform string) error {
	// Platform-specific restrictions based on subscription level
	switch platform {
	case "car":
		// Car platform might require premium subscription
		if permissions.Level < PermissionPremium {
			return ErrCarPlatformRequiresPremium
		}
	case "miniprogram":
		// Mini-program might have different limitations
		if permissions.Level < PermissionBasic {
			return ErrMiniProgramAccessDenied
		}
	}
	return nil
}

// Custom errors
var (
	ErrNavigationAccessDenied      = errors.New("navigation access denied - upgrade required")
	ErrPremiumFeatureRequired      = errors.New("premium subscription required for this feature")
	ErrBasicAccessDenied          = errors.New("basic access denied")
	ErrUnknownFeature             = errors.New("unknown feature")
	ErrDeviceLimitExceeded        = errors.New("device limit exceeded")
	ErrTrialExpired               = errors.New("trial period has expired")
	ErrCarPlatformRequiresPremium = errors.New("car platform requires premium subscription")
	ErrMiniProgramAccessDenied    = errors.New("mini-program access denied")
)