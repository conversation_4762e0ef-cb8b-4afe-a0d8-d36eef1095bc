package services

import (
	"context"
	"fmt"
	"math"
	"strconv"
	"sync"
	"time"

	"github.com/azel-ko/final-ddd/internal/domain/entities"
	"github.com/azel-ko/final-ddd/internal/domain/repository"
	"github.com/azel-ko/final-ddd/internal/infrastructure/websocket"
	"github.com/azel-ko/final-ddd/internal/pkg/logger"
	"go.uber.org/zap"
)

// NotificationService 通知服务
type NotificationService struct {
	wsHub    *websocket.Hub
	repo     repository.Repository
	userSubs map[string]*UserSubscription
	mu       sync.RWMutex
	ctx      context.Context
	cancel   context.CancelFunc
}

// UserSubscription 用户订阅信息
type UserSubscription struct {
	UserID       string            `json:"user_id"`
	Routes       []string          `json:"routes"`        // 订阅的路线ID
	Locations    []Location        `json:"locations"`     // 关注的位置
	Checkpoints  []string          `json:"checkpoints"`   // 关注的检查站ID
	Preferences  NotificationPrefs `json:"preferences"`   // 通知偏好
	LastActivity time.Time         `json:"last_activity"` // 最后活跃时间
}

// Location 位置信息
type Location struct {
	Latitude  float64 `json:"latitude"`
	Longitude float64 `json:"longitude"`
	Radius    float64 `json:"radius"` // 关注半径（米）
	Name      string  `json:"name"`   // 位置名称
}

// NotificationPrefs 通知偏好设置
type NotificationPrefs struct {
	CheckpointUpdates bool     `json:"checkpoint_updates"` // 检查站状态更新
	RouteChanges      bool     `json:"route_changes"`      // 路线变化通知
	SystemAlerts      bool     `json:"system_alerts"`      // 系统警告
	EnabledTypes      []string `json:"enabled_types"`      // 启用的通知类型
}

// Notification 通知消息
type Notification struct {
	ID        string                 `json:"id"`
	Type      string                 `json:"type"`
	Title     string                 `json:"title"`
	Content   string                 `json:"content"`
	Data      map[string]interface{} `json:"data"`
	Priority  int                    `json:"priority"` // 1-5, 5最高
	UserID    string                 `json:"user_id,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
	ExpiresAt *time.Time             `json:"expires_at,omitempty"`
}

// NotificationType 通知类型常量
const (
	NotificationTypeCheckpointUpdate = "checkpoint_update"
	NotificationTypeRouteChange      = "route_change"
	NotificationTypeSystemAlert      = "system_alert"
	NotificationTypeTrafficAlert     = "traffic_alert"
	NotificationTypeUserReminder     = "user_reminder"
)

// NewNotificationService 创建通知服务
func NewNotificationService(
	wsHub *websocket.Hub,
	repo repository.Repository,
) *NotificationService {
	ctx, cancel := context.WithCancel(context.Background())

	return &NotificationService{
		wsHub:    wsHub,
		repo:     repo,
		userSubs: make(map[string]*UserSubscription),
		ctx:      ctx,
		cancel:   cancel,
	}
}

// Start 启动通知服务
func (ns *NotificationService) Start() {
	logger.Info("Notification service started")

	// 启动定期清理过期订阅的协程
	go ns.cleanupExpiredSubscriptions()
}

// Stop 停止通知服务
func (ns *NotificationService) Stop() {
	ns.cancel()
	logger.Info("Notification service stopped")
}

// SubscribeUser 用户订阅通知
func (ns *NotificationService) SubscribeUser(userID string, subscription *UserSubscription) error {
	ns.mu.Lock()
	defer ns.mu.Unlock()

	subscription.UserID = userID
	subscription.LastActivity = time.Now()
	ns.userSubs[userID] = subscription

	logger.Info("User subscribed to notifications",
		zap.String("user_id", userID),
		zap.Int("routes", len(subscription.Routes)),
		zap.Int("locations", len(subscription.Locations)))

	return nil
}

// UnsubscribeUser 用户取消订阅
func (ns *NotificationService) UnsubscribeUser(userID string) error {
	ns.mu.Lock()
	defer ns.mu.Unlock()

	delete(ns.userSubs, userID)

	logger.Info("User unsubscribed from notifications", zap.String("user_id", userID))
	return nil
}

// UpdateUserSubscription 更新用户订阅
func (ns *NotificationService) UpdateUserSubscription(userID string, subscription *UserSubscription) error {
	ns.mu.Lock()
	defer ns.mu.Unlock()

	if existing, exists := ns.userSubs[userID]; exists {
		// 更新现有订阅
		existing.Routes = subscription.Routes
		existing.Locations = subscription.Locations
		existing.Checkpoints = subscription.Checkpoints
		existing.Preferences = subscription.Preferences
		existing.LastActivity = time.Now()
	} else {
		// 创建新订阅
		subscription.UserID = userID
		subscription.LastActivity = time.Now()
		ns.userSubs[userID] = subscription
	}

	logger.Info("User subscription updated", zap.String("user_id", userID))
	return nil
}

// NotifyCheckpointUpdate 通知检查站状态更新
func (ns *NotificationService) NotifyCheckpointUpdate(checkpoint *entities.Checkpoint, oldStatus string) error {
	notification := &Notification{
		ID:       fmt.Sprintf("cp_update_%d_%d", checkpoint.ID, time.Now().Unix()),
		Type:     NotificationTypeCheckpointUpdate,
		Title:    "检查站状态更新",
		Content:  fmt.Sprintf("检查站 %s 状态从 %s 变更为 %s", checkpoint.Name, oldStatus, checkpoint.Status),
		Priority: ns.getCheckpointPriority(checkpoint.Status),
		Data: map[string]interface{}{
			"checkpoint_id":   checkpoint.ID,
			"checkpoint_name": checkpoint.Name,
			"old_status":      oldStatus,
			"new_status":      checkpoint.Status,
			"location": map[string]interface{}{
				"latitude":  checkpoint.Latitude,
				"longitude": checkpoint.Longitude,
				"address":   checkpoint.Location,
			},
		},
		Timestamp: time.Now(),
	}

	// 找到需要通知的用户
	affectedUsers := ns.findAffectedUsers(checkpoint)

	// 发送通知
	for _, userID := range affectedUsers {
		notification.UserID = userID
		if err := ns.sendNotification(notification); err != nil {
			logger.Error("Failed to send checkpoint update notification",
				zap.Error(err), zap.String("user_id", userID))
		}
	}

	// 广播到WebSocket
	ns.wsHub.BroadcastToTopic(websocket.TopicCheckpointUpdate, notification.Data)

	logger.Info("Checkpoint update notification sent",
		zap.Uint("checkpoint_id", checkpoint.ID),
		zap.Int("affected_users", len(affectedUsers)))

	return nil
}

// NotifyRouteChange 通知路线变化
func (ns *NotificationService) NotifyRouteChange(routeID string, reason string, affectedCheckpoints []string) error {
	notification := &Notification{
		ID:       fmt.Sprintf("route_change_%s_%d", routeID, time.Now().Unix()),
		Type:     NotificationTypeRouteChange,
		Title:    "路线需要重新规划",
		Content:  fmt.Sprintf("由于%s，建议重新规划路线", reason),
		Priority: 4, // 高优先级
		Data: map[string]interface{}{
			"route_id":             routeID,
			"reason":               reason,
			"affected_checkpoints": affectedCheckpoints,
			"action":               "replan_route",
		},
		Timestamp: time.Now(),
	}

	// 找到使用该路线的用户
	affectedUsers := ns.findUsersForRoute(routeID)

	// 发送通知
	for _, userID := range affectedUsers {
		notification.UserID = userID
		if err := ns.sendNotification(notification); err != nil {
			logger.Error("Failed to send route change notification",
				zap.Error(err), zap.String("user_id", userID))
		}
	}

	// 广播到WebSocket
	ns.wsHub.BroadcastToTopic(websocket.TopicRouteChange, notification.Data)

	logger.Info("Route change notification sent",
		zap.String("route_id", routeID),
		zap.Int("affected_users", len(affectedUsers)))

	return nil
}

// NotifySystemAlert 发送系统警告
func (ns *NotificationService) NotifySystemAlert(title, content string, priority int) error {
	notification := &Notification{
		ID:       fmt.Sprintf("system_alert_%d", time.Now().Unix()),
		Type:     NotificationTypeSystemAlert,
		Title:    title,
		Content:  content,
		Priority: priority,
		Data: map[string]interface{}{
			"alert_type": "system",
		},
		Timestamp: time.Now(),
	}

	// 广播给所有用户
	ns.wsHub.BroadcastToTopic(websocket.TopicSystemAlert, notification.Data)

	logger.Info("System alert sent",
		zap.String("title", title),
		zap.Int("priority", priority))

	return nil
}

// sendNotification 发送通知给特定用户
func (ns *NotificationService) sendNotification(notification *Notification) error {
	// 检查用户订阅偏好
	if !ns.shouldNotifyUser(notification.UserID, notification.Type) {
		return nil
	}

	// 通过WebSocket发送
	messageData := map[string]interface{}{
		"notification": notification,
	}

	// 这里可以扩展为发送给特定用户的WebSocket连接
	// 目前通过主题广播，客户端根据user_id过滤
	ns.wsHub.BroadcastToTopic("user_notification", messageData)

	return nil
}

// shouldNotifyUser 检查是否应该通知用户
func (ns *NotificationService) shouldNotifyUser(userID, notificationType string) bool {
	ns.mu.RLock()
	defer ns.mu.RUnlock()

	subscription, exists := ns.userSubs[userID]
	if !exists {
		return false
	}

	// 检查通知偏好
	switch notificationType {
	case NotificationTypeCheckpointUpdate:
		return subscription.Preferences.CheckpointUpdates
	case NotificationTypeRouteChange:
		return subscription.Preferences.RouteChanges
	case NotificationTypeSystemAlert:
		return subscription.Preferences.SystemAlerts
	default:
		// 检查是否在启用的类型列表中
		for _, enabledType := range subscription.Preferences.EnabledTypes {
			if enabledType == notificationType {
				return true
			}
		}
	}

	return false
}

// findAffectedUsers 找到受检查站影响的用户
func (ns *NotificationService) findAffectedUsers(checkpoint *entities.Checkpoint) []string {
	ns.mu.RLock()
	defer ns.mu.RUnlock()

	var affectedUsers []string

	for userID, subscription := range ns.userSubs {
		// 检查是否关注该检查站
		checkpointIDStr := strconv.FormatUint(uint64(checkpoint.ID), 10)
		for _, cpID := range subscription.Checkpoints {
			if cpID == checkpointIDStr {
				affectedUsers = append(affectedUsers, userID)
				break
			}
		}

		// 检查是否在关注的位置范围内
		for _, location := range subscription.Locations {
			distance := ns.calculateDistance(
				location.Latitude, location.Longitude,
				checkpoint.Latitude, checkpoint.Longitude,
			)
			if distance <= location.Radius {
				affectedUsers = append(affectedUsers, userID)
				break
			}
		}
	}

	return affectedUsers
}

// findUsersForRoute 找到使用特定路线的用户
func (ns *NotificationService) findUsersForRoute(routeID string) []string {
	ns.mu.RLock()
	defer ns.mu.RUnlock()

	var affectedUsers []string

	for userID, subscription := range ns.userSubs {
		for _, subRouteID := range subscription.Routes {
			if subRouteID == routeID {
				affectedUsers = append(affectedUsers, userID)
				break
			}
		}
	}

	return affectedUsers
}

// getCheckpointPriority 根据检查站状态获取通知优先级
func (ns *NotificationService) getCheckpointPriority(status string) int {
	switch status {
	case "active":
		return 5 // 最高优先级
	case "inactive":
		return 2 // 低优先级
	case "unknown":
		return 3 // 中等优先级
	default:
		return 1 // 最低优先级
	}
}

// calculateDistance 计算两点间距离（使用Haversine公式）
func (ns *NotificationService) calculateDistance(lat1, lon1, lat2, lon2 float64) float64 {
	const earthRadius = 6371000 // 地球半径（米）

	dLat := (lat2 - lat1) * math.Pi / 180
	dLon := (lon2 - lon1) * math.Pi / 180

	a := math.Sin(dLat/2)*math.Sin(dLat/2) +
		math.Cos(lat1*math.Pi/180)*math.Cos(lat2*math.Pi/180)*
			math.Sin(dLon/2)*math.Sin(dLon/2)

	c := 2 * math.Atan2(math.Sqrt(a), math.Sqrt(1-a))
	distance := earthRadius * c

	return distance
}

// cleanupExpiredSubscriptions 清理过期的订阅
func (ns *NotificationService) cleanupExpiredSubscriptions() {
	ticker := time.NewTicker(1 * time.Hour)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			ns.mu.Lock()
			expiredTime := time.Now().Add(-24 * time.Hour) // 24小时未活跃视为过期

			for userID, subscription := range ns.userSubs {
				if subscription.LastActivity.Before(expiredTime) {
					delete(ns.userSubs, userID)
					logger.Info("Cleaned up expired subscription", zap.String("user_id", userID))
				}
			}
			ns.mu.Unlock()

		case <-ns.ctx.Done():
			return
		}
	}
}

// GetUserSubscription 获取用户订阅信息
func (ns *NotificationService) GetUserSubscription(userID string) (*UserSubscription, error) {
	ns.mu.RLock()
	defer ns.mu.RUnlock()

	subscription, exists := ns.userSubs[userID]
	if !exists {
		return nil, fmt.Errorf("user subscription not found")
	}

	return subscription, nil
}

// GetSubscriptionStats 获取订阅统计信息
func (ns *NotificationService) GetSubscriptionStats() map[string]interface{} {
	ns.mu.RLock()
	defer ns.mu.RUnlock()

	stats := map[string]interface{}{
		"total_subscriptions":   len(ns.userSubs),
		"active_subscriptions":  0,
		"websocket_connections": ns.wsHub.GetClientCount(),
	}

	// 统计活跃订阅（最近1小时内有活动）
	activeTime := time.Now().Add(-1 * time.Hour)
	for _, subscription := range ns.userSubs {
		if subscription.LastActivity.After(activeTime) {
			stats["active_subscriptions"] = stats["active_subscriptions"].(int) + 1
		}
	}

	return stats
}
