package services

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/azel-ko/final-ddd/internal/domain/entities"
	"github.com/azel-ko/final-ddd/internal/domain/repository"
	"github.com/azel-ko/final-ddd/internal/infrastructure/push"
	"github.com/azel-ko/final-ddd/internal/pkg/config"
	"github.com/azel-ko/final-ddd/internal/pkg/logger"
	"go.uber.org/zap"
)

// PushService 推送服务
type PushService struct {
	repo              repository.Repository
	firebaseClient    *push.FirebaseClient
	apnsClient        *push.APNSClient
	miniprogramClient *push.MiniprogramClient
	config            *config.PushConfig
}

// PushMessage 推送消息结构
type PushMessage struct {
	Title    string                 `json:"title"`
	Body     string                 `json:"body"`
	Data     map[string]interface{} `json:"data,omitempty"`
	Badge    int                    `json:"badge,omitempty"`
	Sound    string                 `json:"sound,omitempty"`
	Priority string                 `json:"priority,omitempty"` // high, normal
}

// 实现push包中的PushMessage接口
func (m *PushMessage) GetTitle() string                { return m.Title }
func (m *PushMessage) GetBody() string                 { return m.Body }
func (m *PushMessage) GetData() map[string]interface{} { return m.Data }
func (m *PushMessage) GetPriority() string             { return m.Priority }
func (m *PushMessage) GetSound() string                { return m.Sound }

// PushTarget 推送目标
type PushTarget struct {
	UserID     uint     `json:"user_id"`
	DeviceType string   `json:"device_type"` // ios, android, miniprogram
	Token      string   `json:"token"`
	Tags       []string `json:"tags,omitempty"`
}

// PushResult 推送结果
type PushResult struct {
	Success     bool   `json:"success"`
	MessageID   string `json:"message_id,omitempty"`
	Error       string `json:"error,omitempty"`
	DeviceToken string `json:"device_token"`
}

// NewPushService 创建推送服务
func NewPushService(
	repo repository.Repository,
	config *config.PushConfig,
) *PushService {
	service := &PushService{
		repo:   repo,
		config: config,
	}

	// 初始化Firebase客户端
	if config.Firebase.Enabled {
		firebaseClient, err := push.NewFirebaseClient(&config.Firebase)
		if err != nil {
			logger.Error("Failed to initialize Firebase client", zap.Error(err))
		} else {
			service.firebaseClient = firebaseClient
			logger.Info("Firebase push client initialized")
		}
	}

	// 初始化APNs客户端
	if config.APNS.Enabled {
		apnsClient, err := push.NewAPNSClient(&config.APNS)
		if err != nil {
			logger.Error("Failed to initialize APNs client", zap.Error(err))
		} else {
			service.apnsClient = apnsClient
			logger.Info("APNs push client initialized")
		}
	}

	// 初始化小程序客户端
	if config.Miniprogram.Enabled {
		miniprogramConfig := &push.MiniprogramConfig{
			Enabled:   config.Miniprogram.Enabled,
			AppID:     config.Miniprogram.AppID,
			AppSecret: config.Miniprogram.AppSecret,
		}
		miniprogramClient, err := push.NewMiniprogramClient(miniprogramConfig)
		if err != nil {
			logger.Error("Failed to initialize Miniprogram client", zap.Error(err))
		} else {
			service.miniprogramClient = miniprogramClient
			logger.Info("Miniprogram push client initialized")
		}
	}

	return service
}

// SendToUser 向指定用户发送推送
func (s *PushService) SendToUser(ctx context.Context, userID uint, message *PushMessage) error {
	// 获取用户的设备token
	devices, err := s.getUserDevices(userID)
	if err != nil {
		logger.Error("Failed to get user devices", zap.Uint("user_id", userID), zap.Error(err))
		return err
	}

	if len(devices) == 0 {
		logger.Info("No devices found for user", zap.Uint("user_id", userID))
		return nil
	}

	// 向所有设备发送推送
	var results []PushResult
	for _, device := range devices {
		result := s.sendToDevice(ctx, device, message)
		results = append(results, result)
	}

	// 记录推送结果
	s.recordPushResults(userID, message, results)

	return nil
}

// SendToUsers 向多个用户发送推送
func (s *PushService) SendToUsers(ctx context.Context, userIDs []uint, message *PushMessage) error {
	for _, userID := range userIDs {
		if err := s.SendToUser(ctx, userID, message); err != nil {
			logger.Error("Failed to send push to user", zap.Uint("user_id", userID), zap.Error(err))
		}
	}
	return nil
}

// SendCheckpointUpdate 发送检查站更新推送
func (s *PushService) SendCheckpointUpdate(ctx context.Context, checkpoint *entities.Checkpoint, oldStatus string, affectedUsers []uint) error {
	message := &PushMessage{
		Title: "检查站状态更新",
		Body:  fmt.Sprintf("%s 状态从 %s 变更为 %s", checkpoint.Name, s.getStatusText(oldStatus), s.getStatusText(checkpoint.Status)),
		Data: map[string]interface{}{
			"type":          "checkpoint_update",
			"checkpoint_id": checkpoint.ID,
			"old_status":    oldStatus,
			"new_status":    checkpoint.Status,
			"latitude":      checkpoint.Latitude,
			"longitude":     checkpoint.Longitude,
		},
		Priority: "high",
		Sound:    "default",
	}

	return s.SendToUsers(ctx, affectedUsers, message)
}

// SendRouteChange 发送路线变化推送
func (s *PushService) SendRouteChange(ctx context.Context, routeID string, reason string, affectedUsers []uint) error {
	message := &PushMessage{
		Title: "路线需要重新规划",
		Body:  fmt.Sprintf("由于%s，建议重新规划路线", reason),
		Data: map[string]interface{}{
			"type":     "route_change",
			"route_id": routeID,
			"reason":   reason,
		},
		Priority: "high",
		Sound:    "default",
	}

	return s.SendToUsers(ctx, affectedUsers, message)
}

// SendSystemAlert 发送系统通知
func (s *PushService) SendSystemAlert(ctx context.Context, title, content string, userIDs []uint) error {
	message := &PushMessage{
		Title: title,
		Body:  content,
		Data: map[string]interface{}{
			"type": "system_alert",
		},
		Priority: "normal",
		Sound:    "default",
	}

	return s.SendToUsers(ctx, userIDs, message)
}

// SendTemplateMessage 使用模板发送推送消息
func (s *PushService) SendTemplateMessage(ctx context.Context, templateName string, data map[string]interface{}, userIDs []uint) error {
	// 获取推送模板
	template, err := s.repo.GetPushTemplate(templateName)
	if err != nil {
		logger.Error("Failed to get push template", zap.String("template", templateName), zap.Error(err))
		return err
	}

	// 渲染模板内容
	title, body, err := s.renderTemplate(template, data)
	if err != nil {
		logger.Error("Failed to render template", zap.String("template", templateName), zap.Error(err))
		return err
	}

	// 合并模板数据和自定义数据
	templateData := make(map[string]interface{})
	if template.Data != "" {
		if err := json.Unmarshal([]byte(template.Data), &templateData); err != nil {
			logger.Error("Failed to parse template data", zap.String("template", templateName), zap.Error(err))
		}
	}

	// 自定义数据覆盖模板数据
	for key, value := range data {
		templateData[key] = value
	}

	message := &PushMessage{
		Title:    title,
		Body:     body,
		Data:     templateData,
		Priority: "normal",
		Sound:    "default",
	}

	return s.SendToUsers(ctx, userIDs, message)
}

// SendTrialExpiryWarning 发送试用期到期警告
func (s *PushService) SendTrialExpiryWarning(ctx context.Context, userID uint, daysLeft int) error {
	data := map[string]interface{}{
		"days": daysLeft,
	}

	return s.SendTemplateMessage(ctx, "trial_expiry_warning", data, []uint{userID})
}

// SendSubscriptionExpired 发送订阅过期通知
func (s *PushService) SendSubscriptionExpired(ctx context.Context, userID uint) error {
	data := map[string]interface{}{}

	return s.SendTemplateMessage(ctx, "subscription_expired", data, []uint{userID})
}

// RegisterDevice 注册设备token
func (s *PushService) RegisterDevice(ctx context.Context, userID uint, deviceType, token string, tags []string) error {
	device := &entities.PushDevice{
		UserID:     userID,
		DeviceType: deviceType,
		Token:      token,
		Tags:       s.tagsToJSON(tags),
		IsActive:   true,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}

	// 检查设备是否已存在
	existingDevice, err := s.repo.GetPushDeviceByToken(token)
	if err == nil && existingDevice != nil {
		// 更新现有设备
		existingDevice.UserID = userID
		existingDevice.Tags = device.Tags
		existingDevice.IsActive = true
		existingDevice.UpdatedAt = time.Now()

		return s.repo.UpdatePushDevice(existingDevice)
	}

	// 创建新设备
	return s.repo.CreatePushDevice(device)
}

// UnregisterDevice 注销设备token
func (s *PushService) UnregisterDevice(ctx context.Context, token string) error {
	device, err := s.repo.GetPushDeviceByToken(token)
	if err != nil {
		return err
	}

	device.IsActive = false
	device.UpdatedAt = time.Now()

	return s.repo.UpdatePushDevice(device)
}

// GetPushStatistics 获取推送统计
func (s *PushService) GetPushStatistics(ctx context.Context, userID uint, days int) (*PushStatistics, error) {
	stats, err := s.repo.GetPushStatistics(userID, days)
	if err != nil {
		return nil, err
	}

	return &PushStatistics{
		TotalSent:      stats.TotalSent,
		TotalDelivered: stats.TotalDelivered,
		TotalFailed:    stats.TotalFailed,
		DeliveryRate:   s.calculateDeliveryRate(stats.TotalDelivered, stats.TotalSent),
		DeviceCount:    stats.DeviceCount,
	}, nil
}

// GetPushSubscription 获取用户推送订阅设置
func (s *PushService) GetPushSubscription(ctx context.Context, userID uint) (*entities.PushSubscription, error) {
	subscription, err := s.repo.GetPushSubscription(userID)
	if err != nil {
		// 如果用户没有订阅设置，创建默认设置
		if err.Error() == "record not found" || err.Error() == "sql: no rows in result set" {
			defaultSubscription := &entities.PushSubscription{
				UserID:            userID,
				CheckpointUpdates: true,
				RouteChanges:      true,
				SystemAlerts:      true,
				QuietHours:        "{}",
				CreatedAt:         time.Now(),
				UpdatedAt:         time.Now(),
			}

			if createErr := s.repo.CreateOrUpdatePushSubscription(defaultSubscription); createErr != nil {
				return nil, createErr
			}

			return defaultSubscription, nil
		}
		return nil, err
	}

	return subscription, nil
}

// UpdatePushSubscription 更新用户推送订阅设置
func (s *PushService) UpdatePushSubscription(ctx context.Context, subscription *entities.PushSubscription) error {
	subscription.UpdatedAt = time.Now()
	return s.repo.CreateOrUpdatePushSubscription(subscription)
}

// CheckUserSubscription 检查用户是否订阅了特定类型的推送
func (s *PushService) CheckUserSubscription(ctx context.Context, userID uint, messageType string) (bool, error) {
	subscription, err := s.GetPushSubscription(ctx, userID)
	if err != nil {
		return false, err
	}

	switch messageType {
	case "checkpoint_update":
		return subscription.CheckpointUpdates, nil
	case "route_change":
		return subscription.RouteChanges, nil
	case "system_alert":
		return subscription.SystemAlerts, nil
	default:
		return true, nil // 默认允许未知类型的推送
	}
}

// SendBatchNotification 批量发送推送通知
func (s *PushService) SendBatchNotification(ctx context.Context, message *PushMessage, userIDs []uint) (*BatchPushResult, error) {
	result := &BatchPushResult{
		TotalUsers:   len(userIDs),
		SuccessCount: 0,
		FailureCount: 0,
		Results:      make(map[uint]string),
	}

	// 检查消息类型，过滤未订阅的用户
	messageType := "unknown"
	if data := message.GetData(); data != nil {
		if msgType, ok := data["type"].(string); ok {
			messageType = msgType
		}
	}

	var filteredUserIDs []uint
	for _, userID := range userIDs {
		subscribed, err := s.CheckUserSubscription(ctx, userID, messageType)
		if err != nil {
			result.Results[userID] = fmt.Sprintf("Failed to check subscription: %v", err)
			result.FailureCount++
			continue
		}

		if subscribed {
			filteredUserIDs = append(filteredUserIDs, userID)
		} else {
			result.Results[userID] = "User not subscribed to this message type"
			result.FailureCount++
		}
	}

	// 向订阅用户发送推送
	for _, userID := range filteredUserIDs {
		if err := s.SendToUser(ctx, userID, message); err != nil {
			result.Results[userID] = fmt.Sprintf("Failed to send: %v", err)
			result.FailureCount++
		} else {
			result.Results[userID] = "Success"
			result.SuccessCount++
		}
	}

	return result, nil
}

// CleanupInactiveDevices 清理不活跃的设备
func (s *PushService) CleanupInactiveDevices(ctx context.Context, days int) error {
	return s.repo.DeleteInactivePushDevices(days)
}

// CleanupOldPushLogs 清理旧的推送日志
func (s *PushService) CleanupOldPushLogs(ctx context.Context, days int) error {
	return s.repo.DeleteOldPushLogs(days)
}

// GetPushLogs 获取用户推送日志
func (s *PushService) GetPushLogs(ctx context.Context, userID uint, limit int) ([]*entities.PushLog, error) {
	return s.repo.GetPushLogs(userID, limit)
}

// ValidateDeviceToken 验证设备token有效性
func (s *PushService) ValidateDeviceToken(ctx context.Context, deviceType, token string) error {
	switch deviceType {
	case "android":
		if s.firebaseClient != nil {
			// 发送测试消息验证token
			testMessage := &PushMessage{
				Title: "Token Validation",
				Body:  "This is a test message",
				Data: map[string]interface{}{
					"test": true,
				},
				Priority: "normal",
			}
			_, err := s.firebaseClient.SendToToken(ctx, token, testMessage)
			return err
		}
		return fmt.Errorf("Firebase client not initialized")

	case "ios":
		if s.apnsClient != nil {
			return s.apnsClient.ValidateToken(ctx, token)
		}
		return fmt.Errorf("APNs client not initialized")

	case "miniprogram":
		// 小程序token验证比较复杂，这里简单返回成功
		// 实际使用中可以通过发送测试模板消息来验证
		return nil

	default:
		return fmt.Errorf("unsupported device type: %s", deviceType)
	}
}

// GetPushTemplates 获取所有推送模板
func (s *PushService) GetPushTemplates(ctx context.Context) ([]*entities.PushTemplate, error) {
	return s.repo.GetPushTemplates()
}

// CreatePushTemplate 创建推送模板
func (s *PushService) CreatePushTemplate(ctx context.Context, template *entities.PushTemplate) error {
	template.CreatedAt = time.Now()
	template.UpdatedAt = time.Now()
	return s.repo.CreatePushTemplate(template)
}

// UpdatePushTemplate 更新推送模板
func (s *PushService) UpdatePushTemplate(ctx context.Context, template *entities.PushTemplate) error {
	template.UpdatedAt = time.Now()
	return s.repo.UpdatePushTemplate(template)
}

// 私有方法

// getUserDevices 获取用户设备列表
func (s *PushService) getUserDevices(userID uint) ([]*entities.PushDevice, error) {
	return s.repo.GetUserPushDevices(userID)
}

// sendToDevice 向单个设备发送推送
func (s *PushService) sendToDevice(ctx context.Context, device *entities.PushDevice, message *PushMessage) PushResult {
	result := PushResult{
		DeviceToken: device.Token,
	}

	switch device.DeviceType {
	case "android":
		if s.firebaseClient != nil {
			messageID, err := s.firebaseClient.SendToToken(ctx, device.Token, message)
			if err != nil {
				result.Error = err.Error()
				logger.Error("Failed to send Firebase push", zap.String("token", device.Token), zap.Error(err))
			} else {
				result.Success = true
				result.MessageID = messageID
			}
		} else {
			result.Error = "Firebase client not initialized"
		}

	case "ios":
		if s.apnsClient != nil {
			messageID, err := s.apnsClient.SendToToken(ctx, device.Token, message)
			if err != nil {
				result.Error = err.Error()
				logger.Error("Failed to send APNs push", zap.String("token", device.Token), zap.Error(err))
			} else {
				result.Success = true
				result.MessageID = messageID
			}
		} else {
			result.Error = "APNs client not initialized"
		}

	case "miniprogram":
		if s.miniprogramClient != nil {
			messageID, err := s.miniprogramClient.SendTemplateMessage(ctx, device.Token, message)
			if err != nil {
				result.Error = err.Error()
				logger.Error("Failed to send Miniprogram push", zap.String("token", device.Token), zap.Error(err))
			} else {
				result.Success = true
				result.MessageID = messageID
			}
		} else {
			result.Error = "Miniprogram client not initialized"
		}

	default:
		result.Error = "Unsupported device type"
	}

	return result
}

// recordPushResults 记录推送结果
func (s *PushService) recordPushResults(userID uint, message *PushMessage, results []PushResult) {
	for _, result := range results {
		pushLog := &entities.PushLog{
			UserID:      userID,
			DeviceToken: result.DeviceToken,
			Title:       message.Title,
			Body:        message.Body,
			Data:        s.dataToJSON(message.Data),
			Success:     result.Success,
			Error:       result.Error,
			MessageID:   result.MessageID,
			CreatedAt:   time.Now(),
		}

		if err := s.repo.CreatePushLog(pushLog); err != nil {
			logger.Error("Failed to create push log", zap.Error(err))
		}
	}
}

// getStatusText 获取状态文本
func (s *PushService) getStatusText(status string) string {
	switch status {
	case "active":
		return "检查中"
	case "inactive":
		return "未检查"
	case "unknown":
		return "状态未知"
	default:
		return "未知"
	}
}

// tagsToJSON 将标签数组转换为JSON字符串
func (s *PushService) tagsToJSON(tags []string) string {
	if len(tags) == 0 {
		return "[]"
	}

	data, _ := json.Marshal(tags)
	return string(data)
}

// dataToJSON 将数据转换为JSON字符串
func (s *PushService) dataToJSON(data map[string]interface{}) string {
	if len(data) == 0 {
		return "{}"
	}

	jsonData, _ := json.Marshal(data)
	return string(jsonData)
}

// calculateDeliveryRate 计算送达率
func (s *PushService) calculateDeliveryRate(delivered, total int) float64 {
	if total == 0 {
		return 0
	}
	return float64(delivered) / float64(total) * 100
}

// renderTemplate 渲染推送模板
func (s *PushService) renderTemplate(template *entities.PushTemplate, data map[string]interface{}) (string, string, error) {
	// 简单的模板渲染，支持 {{.key}} 格式
	title := template.Title
	body := template.Body

	for key, value := range data {
		placeholder := fmt.Sprintf("{{.%s}}", key)
		valueStr := fmt.Sprintf("%v", value)

		title = strings.Replace(title, placeholder, valueStr, -1)
		body = strings.Replace(body, placeholder, valueStr, -1)
	}

	return title, body, nil
}

// PushStatistics 推送统计
type PushStatistics struct {
	TotalSent      int     `json:"total_sent"`
	TotalDelivered int     `json:"total_delivered"`
	TotalFailed    int     `json:"total_failed"`
	DeliveryRate   float64 `json:"delivery_rate"`
	DeviceCount    int     `json:"device_count"`
}

// BatchPushResult 批量推送结果
type BatchPushResult struct {
	TotalUsers   int             `json:"total_users"`
	SuccessCount int             `json:"success_count"`
	FailureCount int             `json:"failure_count"`
	Results      map[uint]string `json:"results"`
}
