# 北京导航避让系统 - 任务运行器配置
# https://taskfile.dev

version: '3'

vars:
  PROJECT_NAME: beijing-navigation
  GO_VERSION: 1.23
  NODE_VERSION: 18

env:
  CGO_ENABLED: 0

tasks:
  default:
    desc: 显示可用任务
    cmds:
      - task --list

  # 开发环境
  dev:
    desc: 启动完整开发环境
    cmds:
      - ./deploy/dev/start-dev.sh

  dev-backend:
    desc: 仅启动后端服务
    cmds:
      - ./deploy/dev/debug-start.sh

  dev-frontend:
    desc: 仅启动前端服务
    cmds:
      - cd frontend && pnpm install && pnpm run dev

  dev-stop:
    desc: 停止开发环境
    cmds:
      - ./deploy/dev/stop-dev.sh

  # 依赖管理
  install:
    desc: 安装所有依赖
    cmds:
      - go mod download
      - cd frontend && pnpm install

  # 构建
  build:
    desc: 构建后端应用
    cmds:
      - go build -o bin/app cmd/main.go

  build-frontend:
    desc: 构建前端
    cmds:
      - cd frontend && pnpm run build

  build-all:
    desc: 构建前后端
    cmds:
      - task: build
      - task: build-frontend

  # 测试
  test:
    desc: 运行所有测试
    cmds:
      - go test ./test -v

  test-frontend:
    desc: 运行前端测试
    cmds:
      - cd frontend && pnpm test

  # 代码质量
  format:
    desc: 格式化代码
    cmds:
      - go fmt ./...
      - cd frontend && pnpm run lint --fix

  lint:
    desc: 代码检查
    cmds:
      - go vet ./...
      - cd frontend && pnpm run lint

  # 数据库
  db-init:
    desc: 初始化数据库
    cmds:
      - ./deploy/dev/database/init-database.sh

  db-migrate:
    desc: 数据库迁移
    cmds:
      - ./deploy/dev/database/migrate-database.sh

  db-backup:
    desc: 备份数据库
    cmds:
      - ./deploy/dev/database/backup-database.sh

  # 清理
  clean:
    desc: 清理构建文件
    cmds:
      - rm -rf bin/
      - rm -rf frontend/dist/
      - go clean -cache -testcache

  # Docker
  docker-dev:
    desc: 使用Docker启动开发环境
    cmds:
      - docker-compose -f docker-compose.dev.yml up --build

  docker-stop:
    desc: 停止Docker开发环境
    cmds:
      - docker-compose -f docker-compose.dev.yml down

  # 部署
  deploy-local:
    desc: 本地k3s部署
    cmds:
      - ./deploy/scripts/local-k3s-deploy.sh

  deploy-remote:
    desc: 远程部署 (需要指定主机)
    cmds:
      - echo "使用方法{{":"}} ./deploy/scripts/remote-deploy.sh --host YOUR_HOST --all"

  deploy-quick:
    desc: 快速部署更新
    cmds:
      - ./deploy/scripts/quick-deploy.sh --no-build

  deploy-check:
    desc: 检查部署状态
    cmds:
      - ./deploy/scripts/check-deployment.sh

  # k3s相关
  k3s-build:
    desc: k3s环境构建前后端镜像
    cmds:
      - ./deploy/local-k3s/build.sh

  k3s-deploy:
    desc: k3s环境部署前后端应用
    cmds:
      - ./deploy/local-k3s/deploy.sh

  k3s-smart-deploy:
    desc: k3s环境智能部署前后端应用（自动解决DNS问题）
    cmds:
      - ./deploy/local-k3s/smart-deploy.sh

  # 数据库工具
  db-tools:
    desc: 数据库工具 (显示帮助)
    cmds:
      - ./deploy/scripts/database-tools.sh

  db-unify:
    desc: 统一数据库配置
    cmds:
      - ./deploy/scripts/database-tools.sh unify-config

  db-verify:
    desc: 验证数据库配置
    cmds:
      - ./deploy/scripts/database-tools.sh verify-config

  # 配置同步
  config-sync:
    desc: 同步所有配置文件
    cmds:
      - ./deploy/scripts/sync-config.sh

  config-check:
    desc: 检查配置一致性
    cmds:
      - ./deploy/scripts/sync-config.sh --check-only

  # 健康检查
  health:
    desc: 健康检查
    cmds:
      - curl -f http://localhost:8080/health || echo "后端服务未运行"
      - curl -f http://localhost:3000 || echo "前端服务未运行"

  # 快速开始
  quick-start:
    desc: 快速开始 (安装依赖 + 启动开发环境)
    cmds:
      - task: install
      - task: dev