#!/bin/bash

# k3s 前后端分离部署脚本
set -e

echo "🚀 部署前后端分离应用到 k3s..."

# 切换到脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 检查 k3s 是否运行
if ! systemctl is-active --quiet k3s; then
    echo "❌ k3s 未运行，请先安装: ./install-k3s.sh"
    exit 1
fi

# 检查 kubectl
if ! command -v kubectl &> /dev/null; then
    echo "❌ kubectl 未找到"
    exit 1
fi

# 应用 Kubernetes 资源
echo "📦 应用 Kubernetes 资源..."

# 创建命名空间
kubectl apply -f manifests/namespace.yaml

# 应用配置
kubectl apply -f manifests/configmap.yaml
kubectl apply -f manifests/secret.yaml

# 部署数据库
kubectl apply -f manifests/postgres.yaml
kubectl apply -f manifests/redis.yaml

# 等待数据库就绪
echo "⏳ 等待数据库就绪..."
kubectl wait --for=condition=ready pod -l app=postgres -n app --timeout=300s
echo "⏳ redis数据库就绪..."
kubectl wait --for=condition=ready pod -l app=redis -n app --timeout=300s

# 部署后端应用
echo "📦 部署后端应用..."
kubectl apply -f manifests/deployment.yaml
kubectl apply -f manifests/service.yaml

# 部署前端应用
echo "📦 部署前端应用..."
kubectl apply -f manifests/frontend-deployment.yaml
kubectl apply -f manifests/frontend-service.yaml

# 等待应用就绪
echo "⏳ 等待后端应用就绪..."
kubectl wait --for=condition=available deployment/app -n app --timeout=300s

echo "⏳ 等待前端应用就绪..."
kubectl wait --for=condition=available deployment/frontend -n app --timeout=300s

echo "✅ 前后端分离部署完成！"
echo ""
echo "📋 访问信息："
echo "  - 前端访问: http://localhost:30080 (NodePort)"
echo "  - 后端API: http://localhost:30081 (NodePort)"
echo "  - 前端端口转发: kubectl port-forward -n app svc/frontend-service 3000:80"
echo "  - 后端端口转发: kubectl port-forward -n app svc/backend-service 8080:8080"
echo ""
echo "📊 管理命令："
echo "  - 查看状态: kubectl get all -n app"
echo "  - 查看前端日志: kubectl logs -f -l app=frontend -n app"
echo "  - 查看后端日志: kubectl logs -f -l app=app -n app"
echo "  - 删除部署: kubectl delete namespace app"