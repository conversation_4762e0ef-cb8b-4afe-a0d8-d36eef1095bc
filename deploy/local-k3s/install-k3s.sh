#!/bin/bash

# k3s 安装脚本
set -e

echo "🚀 安装 k3s..."

# 检查是否已安装
if systemctl is-active --quiet k3s 2>/dev/null; then
    echo "✅ k3s 已安装并运行"
    exit 0
fi

# 安装 k3s
echo "📦 下载并安装 k3s..."
curl -sfL https://get.k3s.io | sh -s - --write-kubeconfig-mode 644

# 等待 k3s 启动
echo "⏳ 等待 k3s 启动..."
sleep 10

# 配置 kubectl
echo "🔧 配置 kubectl..."
mkdir -p ~/.kube
sudo cp /etc/rancher/k3s/k3s.yaml ~/.kube/config
sudo chown $(id -u):$(id -g) ~/.kube/config

# 验证安装
echo "🔍 验证安装..."
kubectl get nodes

echo "✅ k3s 安装完成！"
echo ""
echo "📋 管理命令："
echo "  - 查看节点: kubectl get nodes"
echo "  - 查看Pod: kubectl get pods -A"
echo "  - 卸载k3s: /usr/local/bin/k3s-uninstall.sh"