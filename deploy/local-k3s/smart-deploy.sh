#!/bin/bash

# 智能部署脚本 - 前后端分离，自动解决DNS问题
set -e

echo "🚀 智能部署前后端分离应用（自动解决DNS问题）..."

# 切换到脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 检查 k3s 是否运行
if ! systemctl is-active --quiet k3s; then
    echo "❌ k3s 未运行，请先安装: ./install-k3s.sh"
    exit 1
fi

# 检查 kubectl
if ! command -v kubectl &> /dev/null; then
    echo "❌ kubectl 未找到"
    exit 1
fi

echo "🗑️  清理现有部署..."
# 删除现有的namespace（这会删除所有资源）
kubectl delete namespace app --ignore-not-found=true

# 等待namespace完全删除
echo "⏳ 等待资源清理完成..."
while kubectl get namespace app &> /dev/null; do
    echo "等待namespace删除..."
    sleep 2
done

echo "🔧 重启CoreDNS..."
kubectl rollout restart deployment/coredns -n kube-system
kubectl wait --for=condition=available deployment/coredns -n kube-system --timeout=60s

echo "📦 部署基础设施..."

# 创建命名空间
kubectl apply -f manifests/namespace.yaml
sleep 2

# 应用secret
kubectl apply -f manifests/secret.yaml

# 部署数据库（不包含应用）
kubectl apply -f manifests/postgres.yaml
kubectl apply -f manifests/redis.yaml

# 等待数据库就绪
echo "⏳ 等待PostgreSQL就绪..."
kubectl wait --for=condition=ready pod -l app=postgres -n app --timeout=300s

echo "⏳ 等待Redis就绪..."
kubectl wait --for=condition=ready pod -l app=redis -n app --timeout=300s

# 获取服务IP地址
echo "🔍 获取服务IP地址..."
POSTGRES_IP=$(kubectl get svc postgres-service -n app -o jsonpath='{.spec.clusterIP}')
REDIS_IP=$(kubectl get svc redis-service -n app -o jsonpath='{.spec.clusterIP}')

echo "PostgreSQL IP: $POSTGRES_IP"
echo "Redis IP: $REDIS_IP"

# 创建动态ConfigMap
echo "📝 创建动态ConfigMap..."
cat > /tmp/dynamic-configmap.yaml << EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
  namespace: app
  annotations:
    description: "Dynamic ConfigMap with service IPs to avoid DNS issues"
data:
  APP_ENV: "production"
  LOG_LEVEL: "info"
  DB_HOST: "$POSTGRES_IP"
  DB_PORT: "5432"
  DB_NAME: "driving_navigation"
  DB_SSL_MODE: "disable"
  REDIS_HOST: "$REDIS_IP"
  REDIS_PORT: "6379"
  # 高德地图API配置
  AMAP_API_KEY: "9e471ba01ae18f216cd0fb84032ec7e2"
  AMAP_WEB_SERVICE_KEY: "9e471ba01ae18f216cd0fb84032ec7e2"
  AMAP_BASE_URL: "https://restapi.amap.com"
  AMAP_TIMEOUT: "30s"
  AMAP_RATE_LIMIT: "10"
  AMAP_BURST: "20"
EOF

# 应用动态ConfigMap
kubectl apply -f /tmp/dynamic-configmap.yaml

# 创建简化的后端部署文件（移除初始化容器）
echo "📝 创建简化的后端部署配置..."
cat > /tmp/simple-backend-deployment.yaml << EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: app
  namespace: app
  labels:
    app: app
    component: backend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: app
  template:
    metadata:
      labels:
        app: app
        component: backend
    spec:
      containers:
      - name: app
        image: docker.io/library/app:latest
        imagePullPolicy: Never  # 使用本地镜像
        ports:
        - containerPort: 8080
        envFrom:
        - configMapRef:
            name: app-config
        - secretRef:
            name: app-secret
        startupProbe:
          httpGet:
            path: /api/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
        readinessProbe:
          httpGet:
            path: /api/health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        livenessProbe:
          httpGet:
            path: /api/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
EOF

# 创建前端部署文件
echo "📝 创建前端部署配置..."
cat > /tmp/simple-frontend-deployment.yaml << EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend
  namespace: app
  labels:
    app: frontend
    component: frontend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: frontend
  template:
    metadata:
      labels:
        app: frontend
        component: frontend
    spec:
      containers:
      - name: frontend
        image: docker.io/library/frontend:latest
        imagePullPolicy: Never  # 使用本地镜像
        ports:
        - containerPort: 80
          name: http
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 3
          failureThreshold: 3
        env:
        - name: NGINX_WORKER_PROCESSES
          value: "auto"
        - name: NGINX_WORKER_CONNECTIONS
          value: "1024"
EOF

# 部署后端应用
echo "📦 部署后端应用..."
kubectl apply -f /tmp/simple-backend-deployment.yaml
kubectl apply -f manifests/service.yaml

# 部署前端应用
echo "📦 部署前端应用..."
kubectl apply -f /tmp/simple-frontend-deployment.yaml
kubectl apply -f manifests/frontend-service.yaml

# 等待应用就绪
echo "⏳ 等待后端应用就绪..."
kubectl wait --for=condition=available deployment/app -n app --timeout=300s

echo "⏳ 等待前端应用就绪..."
kubectl wait --for=condition=available deployment/frontend -n app --timeout=300s

# 清理临时文件
rm -f /tmp/dynamic-configmap.yaml /tmp/simple-backend-deployment.yaml /tmp/simple-frontend-deployment.yaml

echo "✅ 前后端分离智能部署完成！"
echo ""
echo "🔌 端口映射信息："
echo "  - 前端应用 (Frontend):  http://localhost:30080"
echo "  - 后端API (Backend):    http://localhost:30081"
echo "  - PostgreSQL数据库:     localhost:30432"
echo "  - Redis缓存:           localhost:30379"
echo ""
echo "📋 访问方法："
echo "  - 前端Web应用: http://localhost:30080"
echo "  - 后端健康检查: curl http://localhost:30081/api/health"
echo "  - 数据库:  psql -h localhost -p 30432 -U driving_nav_user -d driving_navigation"
echo "  - Redis:   redis-cli -h localhost -p 30379"
echo ""
echo "📊 管理命令："
echo "  - 查看状态: kubectl get all -n app"
echo "  - 查看前端日志: kubectl logs -f -l app=frontend -n app"
echo "  - 查看后端日志: kubectl logs -f -l app=app -n app"
echo "  - 删除部署: kubectl delete namespace app"
echo ""
echo "🔧 内部配置信息："
echo "  - PostgreSQL IP: $POSTGRES_IP"
echo "  - Redis IP: $REDIS_IP"
echo ""
echo "🏗️ 架构说明："
echo "  - 前端: Nginx 容器，处理静态文件和API代理"
echo "  - 后端: Go API 服务，处理业务逻辑"
echo "  - 通信: 前端通过Nginx反向代理访问后端API"
echo ""
echo "📖 详细端口映射说明请查看: PORT_MAPPING.md"
