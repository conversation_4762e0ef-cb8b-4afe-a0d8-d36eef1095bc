apiVersion: v1
kind: Service
metadata:
  name: frontend-service
  namespace: app
  labels:
    app: frontend
    component: frontend
spec:
  type: NodePort
  ports:
  - port: 80
    targetPort: 80
    nodePort: 30080
    protocol: TCP
    name: http
  selector:
    app: frontend
---
apiVersion: v1
kind: Service
metadata:
  name: frontend-internal
  namespace: app
  labels:
    app: frontend
    component: frontend
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 80
    protocol: TCP
    name: http
  selector:
    app: frontend
