apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend
  namespace: app
  labels:
    app: frontend
    component: frontend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: frontend
  template:
    metadata:
      labels:
        app: frontend
        component: frontend
    spec:
      # 等待后端服务就绪
      initContainers:
      - name: wait-for-backend
        image: busybox:1.35
        command: ['sh', '-c']
        args:
        - |
          echo "等待后端服务就绪..."
          # 获取后端服务 IP
          BACKEND_IP=$(nslookup backend-service 2>/dev/null | grep "Address:" | tail -1 | awk '{print $2}' || echo "************")
          echo "后端服务 IP: $BACKEND_IP"

          # 等待后端服务端口可用
          until nc -z $BACKEND_IP 8080; do
            echo "等待后端服务 $BACKEND_IP:8080 可用..."
            sleep 2
          done
          echo "后端服务已就绪"
      containers:
      - name: frontend
        image: docker.io/library/frontend:v2
        imagePullPolicy: Never  # 使用本地镜像
        ports:
        - containerPort: 80
          name: http
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 3
          failureThreshold: 3
        env:
        - name: NGINX_WORKER_PROCESSES
          value: "auto"
        - name: NGINX_WORKER_CONNECTIONS
          value: "1024"
