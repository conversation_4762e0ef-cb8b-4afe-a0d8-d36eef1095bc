apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-config
  namespace: app
data:
  pg_hba.conf: |
    # TYPE  DATABASE        USER            ADDRESS                 METHOD
    local   all             all                                     trust
    host    all             all             127.0.0.1/32            trust
    host    all             all             ::1/128                 trust
    host    all             all             0.0.0.0/0               md5
  postgresql.conf: |
    # Basic settings
    listen_addresses = '*'
    port = 5432
    max_connections = 100
    shared_buffers = 128MB
    effective_cache_size = 512MB
    work_mem = 4MB
    maintenance_work_mem = 64MB
    
    # Logging
    log_destination = 'stderr'
    logging_collector = off
    log_statement = 'none'
    log_min_messages = warning
    log_min_error_statement = error
    
    # Performance
    checkpoint_completion_target = 0.9
    wal_buffers = 16MB
    default_statistics_target = 100
    random_page_cost = 1.1
    effective_io_concurrency = 200
