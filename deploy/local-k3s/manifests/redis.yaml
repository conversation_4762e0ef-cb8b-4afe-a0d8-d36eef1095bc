apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: app
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        command: ["redis-server"]
        args:
        - --appendonly
        - "yes"
        env:
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: app-secret
              key: REDIS_PASSWORD
              optional: true
        ports:
        - containerPort: 6379
          hostPort: 6379  # 直接映射到主机6379端口
        volumeMounts:
        - name: redis-storage
          mountPath: /data
        readinessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 5
          periodSeconds: 5
        livenessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 15
          periodSeconds: 10
      volumes:
      - name: redis-storage
        persistentVolumeClaim:
          claimName: redis-pvc

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-pvc
  namespace: app
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi

---
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: app
  labels:
    app: redis
spec:
  selector:
    app: redis
  ports:
  - name: redis
    port: 6379
    targetPort: 6379
  type: ClusterIP

---
# NodePort 服务用于外部访问（可选启用）
apiVersion: v1
kind: Service
metadata:
  name: redis-nodeport
  namespace: app
  labels:
    app: redis
    service-type: external
spec:
  selector:
    app: redis
  ports:
  - name: redis
    port: 6379
    targetPort: 6379
    nodePort: 30379  # 外部访问端口 (映射到主机6379)
  type: NodePort