apiVersion: v1
kind: Service
metadata:
  name: backend-service
  namespace: app
  labels:
    app: app
    component: backend
spec:
  selector:
    app: app
  ports:
  - name: http
    port: 8080
    targetPort: 8080
  type: ClusterIP

---
# NodePort 服务用于后端直接访问（调试用）
apiVersion: v1
kind: Service
metadata:
  name: backend-nodeport
  namespace: app
  labels:
    app: app
    component: backend
    service-type: external
spec:
  selector:
    app: app
  ports:
  - name: http
    port: 8080
    targetPort: 8080
    nodePort: 30081  # 后端直接访问端口
  type: NodePort