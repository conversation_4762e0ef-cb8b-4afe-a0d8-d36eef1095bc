# 构建阶段
FROM docker.1ms.run/node:18-alpine as builder
WORKDIR /app

# 安装 pnpm
RUN corepack enable && corepack prepare pnpm@latest --activate

COPY package*.json pnpm*.yaml ./
RUN pnpm install
COPY . .
RUN CI=true pnpm build

# 运行阶段 - 使用 Nginx
FROM docker.1ms.run/nginx:alpine
WORKDIR /usr/share/nginx/html

# 复制构建产物
COPY --from=builder /app/dist .

# 复制 Nginx 配置
COPY nginx.conf /etc/nginx/conf.d/default.conf

# 暴露端口
EXPOSE 80

# 启动 Nginx
CMD ["nginx", "-g", "daemon off;"]
