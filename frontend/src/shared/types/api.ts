// 通用API响应类型
export interface ApiResponse<T = any> {
  data?: T
  message?: string
  code?: string | number
  success?: boolean
}

// 分页响应类型
export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page?: number
  pageSize?: number
  totalPages?: number
}

// 分页请求参数
export interface PaginationParams {
  page?: number
  pageSize?: number
  current?: number
}

// 排序参数
export interface SortParams {
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 搜索参数
export interface SearchParams {
  keyword?: string
  [key: string]: any
}

// 通用查询参数
export interface QueryParams extends PaginationParams, SortParams, SearchParams {}

// HTTP方法类型
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'

// API错误类型
export interface ApiError {
  message: string
  code?: string | number
  details?: any
}

// 用户相关类型
export interface User {
  id: number
  username: string
  email: string
  role?: string
  car_plate?: string
  plate_region?: string
  subscription?: string
  trial_expiry?: string
  preferences?: string
  created_at?: string
  updated_at?: string
  last_login_at?: string
}

export interface LoginRequest {
  email: string
  password: string
  platform?: 'web' | 'mobile' | 'car' | 'miniprogram'
  device_id?: string
  user_agent?: string
}

export interface RegisterRequest {
  username: string
  email: string
  password: string
  platform?: 'web' | 'mobile' | 'car' | 'miniprogram'
}

export interface LoginResponse {
  token: string
  session_id: string
  expires_at: number
  User: User  // 注意：后端返回的是大写的 "User"
}

export interface UpdateProfileRequest {
  username?: string
  email?: string
  car_plate?: string
  plate_region?: string
  preferences?: string
}

// 订阅相关类型
export interface SubscriptionRequest {
  subscription_type: 'free' | 'premium'
}

export interface PermissionResponse {
  can_navigate: boolean
  can_view_checkpoints: boolean
}

export interface TrialStatusResponse {
  is_expired: boolean
  remaining_days: number
}

// 仪表板统计类型
export interface DashboardStats {
  totalUsers: number
  recentActivity: ActivityItem[]
}

export interface ActivityItem {
  id: string
  type: 'user'
  action: 'create' | 'update' | 'delete'
  description: string
  timestamp: string
}
