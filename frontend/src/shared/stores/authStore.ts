import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { message } from 'antd'
import apiClient from '@/shared/api/client'
import type { User, LoginRequest, RegisterRequest, LoginResponse } from '@/shared/types/api'

interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
}

interface AuthActions {
  login: (credentials: LoginRequest) => Promise<void>
  register: (userData: RegisterRequest) => Promise<void>
  logout: () => void
  checkAuth: () => void
  updateUser: (user: User) => void
}

type AuthStore = AuthState & AuthActions

export const useAuthStore = create<AuthStore>()(
  persist(
    (set,) => ({
      // 初始状态
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,

      // 登录
      login: async (credentials: LoginRequest) => {
        try {
          set({ isLoading: true })

          // 确保传递必要的字段
          const loginData = {
            ...credentials,
            platform: credentials.platform || 'web', // 默认为web平台
            device_id: credentials.device_id || 'web-browser',
            user_agent: credentials.user_agent || navigator.userAgent
          }

          const response: LoginResponse = await apiClient.post('/auth/login', loginData)

          const { token, User: user } = response

          // 确保 token 格式正确（不包含 Bearer 前缀）
          const cleanToken = token.startsWith('Bearer ') ? token.replace('Bearer ', '') : token

          // 保存token和用户信息到localStorage
          localStorage.setItem('token', cleanToken)
          localStorage.setItem('user', JSON.stringify(user))

          set({
            user,
            token: cleanToken,
            isAuthenticated: true,
            isLoading: false,
          })

          message.success('登录成功')
        } catch (error) {
          console.error('Login failed:', error)
          set({ isLoading: false })
          throw error
        }
      },

      // 注册
      register: async (userData: RegisterRequest) => {
        try {
          set({ isLoading: true })

          // 确保传递必要的字段
          const registerData = {
            ...userData,
            platform: userData.platform || 'web' // 默认为web平台
          }

          await apiClient.post('/auth/register', registerData)

          set({ isLoading: false })
          message.success('注册成功，请登录')
        } catch (error) {
          set({ isLoading: false })
          throw error
        }
      },

      // 退出登录
      logout: () => {
        // 清理所有认证相关的存储
        localStorage.removeItem('token')
        localStorage.removeItem('user')
        localStorage.removeItem('auth-storage')

        set({
          user: null,
          token: null,
          isAuthenticated: false,
        })

        message.success('已退出登录')

        // 延迟重定向，确保状态已更新
        setTimeout(() => {
          if (window.location.pathname !== '/login') {
            window.location.href = '/login'
          }
        }, 100)
      },

      // 检查认证状态
      checkAuth: () => {
        const token = localStorage.getItem('token')
        const storedUser = localStorage.getItem('user')

        if (token && storedUser) {
          try {
            const user = JSON.parse(storedUser)

            // 验证 token 格式是否正确
            if (token.startsWith('Bearer ')) {
              // 如果 token 包含 Bearer 前缀，移除它
              const cleanToken = token.replace('Bearer ', '')
              localStorage.setItem('token', cleanToken)
            }

            // 验证用户数据完整性
            if (user && user.id && user.username) {
              set({
                user,
                token,
                isAuthenticated: true,
              })
            } else {
              // 用户数据不完整，清理状态
              localStorage.removeItem('token')
              localStorage.removeItem('user')
              localStorage.removeItem('auth-storage')
              set({
                user: null,
                token: null,
                isAuthenticated: false,
              })
            }
          } catch (error) {
            console.error('Failed to parse stored user data:', error)
            // 如果解析失败，清除无效数据
            localStorage.removeItem('token')
            localStorage.removeItem('user')
            localStorage.removeItem('auth-storage')
            set({
              user: null,
              token: null,
              isAuthenticated: false,
            })
          }
        } else {
          // 没有 token 或用户数据
          set({
            user: null,
            token: null,
            isAuthenticated: false,
          })
        }
      },

      // 更新用户信息
      updateUser: (user: User) => {
        set({ user })
        localStorage.setItem('user', JSON.stringify(user))
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
)
