import { ReactNode, useEffect, useState } from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { Spin } from 'antd'
import { useAuthStore } from '@/shared/stores/authStore'

interface AuthGuardProps {
  children: ReactNode
}

export default function AuthGuard({ children }: AuthGuardProps) {
  const { isAuthenticated, isLoading, checkAuth } = useAuthStore()
  const location = useLocation()
  const [hasCheckedAuth, setHasCheckedAuth] = useState(false)

  useEffect(() => {
    const performAuthCheck = async () => {
      if (!hasCheckedAuth) {
        try {
          await checkAuth()
        } catch (error) {
          console.error('Auth check failed:', error)
        } finally {
          setHasCheckedAuth(true)
        }
      }
    }

    performAuthCheck()
  }, [checkAuth, hasCheckedAuth])

  // 显示加载状态 - 只在初始检查或明确的加载状态时显示
  if (isLoading || !hasCheckedAuth) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh' 
      }}>
        <Spin size="large" />
      </div>
    )
  }

  // 未认证则重定向到登录页
  if (!isAuthenticated) {
    // 如果已经在登录页面或注册页面，直接渲染children
    if (location.pathname === '/login' || location.pathname === '/register') {
      return <>{children}</>
    }
    
    return <Navigate to="/login" state={{ from: location }} replace />
  }

  return <>{children}</>
}
