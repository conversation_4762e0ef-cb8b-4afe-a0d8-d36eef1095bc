import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { message } from 'antd'
import apiClient from '@/shared/api/client'

// 系统统计信息类型
export interface SystemStats {
  uptime: number
  cpu_usage: number
  memory_usage: number
  disk_usage: number
  active_connections: number
  total_requests: number
  error_rate: number
  response_time: number
}

// API统计信息类型
export interface ApiStats {
  endpoint: string
  method: string
  total_calls: number
  success_rate: number
  avg_response_time: number
  error_count: number
  last_called: string
}

// 日志条目类型
export interface LogEntry {
  id: string
  timestamp: string
  level: 'INFO' | 'WARN' | 'ERROR' | 'DEBUG'
  message: string
  source: string
  details?: any
}

// 错误条目类型
export interface ErrorEntry {
  id: string
  timestamp: string
  error_type: string
  message: string
  stack_trace?: string
  user_id?: string
  request_id?: string
  count: number
}

// 健康检查响应类型
export interface HealthCheck {
  status: string
  timestamp: string
  version: string
  components: Record<string, any>
}

// 查询参数类型
export interface LogQueryParams {
  level?: string
  source?: string
  search?: string
  start_time?: string
  end_time?: string
  page?: number
  page_size?: number
}

export interface ErrorQueryParams {
  error_type?: string
  search?: string
  start_time?: string
  end_time?: string
  page?: number
  page_size?: number
}

export interface ApiStatsQueryParams {
  endpoint?: string
  method?: string
  start_time?: string
  end_time?: string
  sort_by?: string
  sort_order?: string
}

// API函数
export const monitoringApi = {
  // 获取系统统计信息
  getSystemStats: async (): Promise<SystemStats> => {
    return apiClient.get('/monitoring/system/stats')
  },

  // 获取API统计信息
  getApiStats: async (params?: ApiStatsQueryParams): Promise<{ data: ApiStats[]; total: number }> => {
    return apiClient.get('/monitoring/api/stats', { params })
  },

  // 获取系统日志
  getLogs: async (params?: LogQueryParams): Promise<{ data: LogEntry[]; total: number; page: number; page_size: number }> => {
    return apiClient.get('/monitoring/logs', { params })
  },

  // 获取错误信息
  getErrors: async (params?: ErrorQueryParams): Promise<{ data: ErrorEntry[]; total: number; page: number; page_size: number }> => {
    return apiClient.get('/monitoring/errors', { params })
  },

  // 获取健康检查信息
  getHealthCheck: async (): Promise<HealthCheck> => {
    return apiClient.get('/monitoring/health')
  },

  // 导出日志
  exportLogs: async (params?: LogQueryParams): Promise<Blob> => {
    const response = await fetch('/api/monitoring/logs/export?' + new URLSearchParams(params as any), {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
      },
    })
    return response.blob()
  },

  // 导出错误报告
  exportErrors: async (params?: ErrorQueryParams): Promise<Blob> => {
    const response = await fetch('/api/monitoring/errors/export?' + new URLSearchParams(params as any), {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
      },
    })
    return response.blob()
  },

  // 导出API统计报告
  exportApiReport: async (params?: ApiStatsQueryParams): Promise<Blob> => {
    const response = await fetch('/api/monitoring/api/export?' + new URLSearchParams(params as any), {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
      },
    })
    return response.blob()
  },
}

// React Query Hooks
export const useSystemStats = () => {
  return useQuery({
    queryKey: ['monitoring', 'system-stats'],
    queryFn: monitoringApi.getSystemStats,
    refetchInterval: 30000, // 30秒自动刷新
    staleTime: 10000, // 10秒内认为数据是新鲜的
  })
}

export const useApiStats = (params?: ApiStatsQueryParams) => {
  return useQuery({
    queryKey: ['monitoring', 'api-stats', params],
    queryFn: () => monitoringApi.getApiStats(params),
    staleTime: 60000, // 1分钟
  })
}

export const useLogs = (params?: LogQueryParams) => {
  return useQuery({
    queryKey: ['monitoring', 'logs', params],
    queryFn: () => monitoringApi.getLogs(params),
    staleTime: 30000, // 30秒
  })
}

export const useErrors = (params?: ErrorQueryParams) => {
  return useQuery({
    queryKey: ['monitoring', 'errors', params],
    queryFn: () => monitoringApi.getErrors(params),
    staleTime: 60000, // 1分钟
  })
}

export const useHealthCheck = () => {
  return useQuery({
    queryKey: ['monitoring', 'health'],
    queryFn: monitoringApi.getHealthCheck,
    refetchInterval: 60000, // 1分钟自动刷新
    staleTime: 30000, // 30秒
  })
}

// 导出相关的mutation hooks
export const useExportLogs = () => {
  return useMutation({
    mutationFn: monitoringApi.exportLogs,
    onSuccess: (blob) => {
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `system_logs_${new Date().toISOString().split('T')[0]}.csv`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
      message.success('日志导出成功')
    },
    onError: () => {
      message.error('日志导出失败')
    },
  })
}

export const useExportErrors = () => {
  return useMutation({
    mutationFn: monitoringApi.exportErrors,
    onSuccess: (blob) => {
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `error_report_${new Date().toISOString().split('T')[0]}.csv`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
      message.success('错误报告导出成功')
    },
    onError: () => {
      message.error('错误报告导出失败')
    },
  })
}

export const useExportApiReport = () => {
  return useMutation({
    mutationFn: monitoringApi.exportApiReport,
    onSuccess: (blob) => {
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `api_stats_report_${new Date().toISOString().split('T')[0]}.csv`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
      message.success('API统计报告导出成功')
    },
    onError: () => {
      message.error('API统计报告导出失败')
    },
  })
}