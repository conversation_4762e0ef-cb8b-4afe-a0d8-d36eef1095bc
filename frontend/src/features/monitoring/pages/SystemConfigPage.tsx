import { useState } from 'react'
import {
  Card,
  Form,
  InputNumber,
  Switch,
  Input,
  Button,
  Space,
  Alert,
  Divider,
  Typography,
  Row,
  Col,
  Table,
  Tag,
  Modal,
  message,
} from 'antd'
import {
  SaveOutlined,
  ReloadOutlined,
  SettingOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
} from '@ant-design/icons'
import { motion } from 'framer-motion'
import dayjs from 'dayjs'
import type { ColumnsType } from 'antd/es/table'

const { Title, Text } = Typography

interface MonitoringConfig {
  cpu_threshold: number
  memory_threshold: number
  disk_threshold: number
  error_threshold: number
  alert_enabled: boolean
  alert_email: string
}

interface Alert {
  id: string
  type: string
  level: string
  title: string
  message: string
  timestamp: string
  resolved: boolean
  resolved_at?: string
  resolved_by?: string
}

export default function SystemConfigPage() {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [alertsLoading, setAlertsLoading] = useState(false)
  const [config, setConfig] = useState<MonitoringConfig>({
    cpu_threshold: 80,
    memory_threshold: 85,
    disk_threshold: 90,
    error_threshold: 5,
    alert_enabled: true,
    alert_email: '<EMAIL>'
  })

  // 模拟告警数据
  const [alerts, setAlerts] = useState<Alert[]>([
    {
      id: 'alert_001',
      type: 'system',
      level: 'warning',
      title: '高内存使用率',
      message: '系统内存使用率超过80%，当前使用率：85.2%',
      timestamp: dayjs().subtract(30, 'minutes').toISOString(),
      resolved: false,
    },
    {
      id: 'alert_002',
      type: 'api',
      level: 'error',
      title: 'API错误率过高',
      message: '路线规划API错误率超过5%，当前错误率：7.8%',
      timestamp: dayjs().subtract(1, 'hour').toISOString(),
      resolved: false,
    },
    {
      id: 'alert_003',
      type: 'data_source',
      level: 'warning',
      title: '数据源响应缓慢',
      message: '进京365网站响应时间超过3秒，当前响应时间：3.2秒',
      timestamp: dayjs().subtract(2, 'hours').toISOString(),
      resolved: true,
      resolved_at: dayjs().subtract(1, 'hour').toISOString(),
      resolved_by: 'admin',
    },
  ])

  const handleSaveConfig = async (values: MonitoringConfig) => {
    setLoading(true)
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      setConfig(values)
      message.success('监控配置保存成功')
    } catch (error) {
      message.error('保存配置失败')
    } finally {
      setLoading(false)
    }
  }

  const handleResetConfig = () => {
    form.setFieldsValue(config)
  }

  const handleResolveAlert = (alertId: string) => {
    Modal.confirm({
      title: '确认解决告警',
      content: '确定要将此告警标记为已解决吗？',
      onOk: async () => {
        try {
          // 模拟API调用
          await new Promise(resolve => setTimeout(resolve, 500))
          setAlerts(prev => prev.map(alert => 
            alert.id === alertId 
              ? { 
                  ...alert, 
                  resolved: true, 
                  resolved_at: dayjs().toISOString(),
                  resolved_by: 'admin'
                }
              : alert
          ))
          message.success('告警已解决')
        } catch (error) {
          message.error('解决告警失败')
        }
      }
    })
  }

  const refreshAlerts = async () => {
    setAlertsLoading(true)
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      message.success('告警列表已刷新')
    } catch (error) {
      message.error('刷新失败')
    } finally {
      setAlertsLoading(false)
    }
  }

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'error': return 'red'
      case 'warning': return 'orange'
      case 'info': return 'blue'
      default: return 'default'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'system': return <SettingOutlined />
      case 'api': return <WarningOutlined />
      case 'data_source': return <CheckCircleOutlined />
      default: return <SettingOutlined />
    }
  }

  // 告警表格列定义
  const alertColumns: ColumnsType<Alert> = [
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type: string) => (
        <Space>
          {getTypeIcon(type)}
          <Text>{type === 'system' ? '系统' : type === 'api' ? 'API' : '数据源'}</Text>
        </Space>
      ),
    },
    {
      title: '级别',
      dataIndex: 'level',
      key: 'level',
      width: 80,
      render: (level: string) => (
        <Tag color={getLevelColor(level)}>
          {level === 'error' ? '错误' : level === 'warning' ? '警告' : '信息'}
        </Tag>
      ),
    },
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      width: 150,
    },
    {
      title: '消息',
      dataIndex: 'message',
      key: 'message',
      ellipsis: true,
    },
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 120,
      render: (time: string) => dayjs(time).format('MM-DD HH:mm'),
      sorter: (a, b) => dayjs(a.timestamp).unix() - dayjs(b.timestamp).unix(),
    },
    {
      title: '状态',
      dataIndex: 'resolved',
      key: 'resolved',
      width: 100,
      render: (resolved: boolean, record) => (
        <Space direction="vertical" size="small">
          <Tag color={resolved ? 'green' : 'red'}>
            {resolved ? '已解决' : '未解决'}
          </Tag>
          {resolved && record.resolved_by && (
            <Text type="secondary" style={{ fontSize: 12 }}>
              by {record.resolved_by}
            </Text>
          )}
        </Space>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 100,
      render: (_, record) => (
        !record.resolved && (
          <Button
            type="link"
            size="small"
            icon={<CheckCircleOutlined />}
            onClick={() => handleResolveAlert(record.id)}
          >
            解决
          </Button>
        )
      ),
    },
  ]

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div style={{ marginBottom: 24 }}>
        <Title level={3} style={{ margin: 0 }}>
          系统配置管理
        </Title>
        <Text type="secondary">配置监控阈值和告警设置</Text>
      </div>

      <Row gutter={[16, 16]}>
        {/* 监控配置 */}
        <Col span={12}>
          <Card title="监控配置" extra={<SettingOutlined />}>
            <Form
              form={form}
              layout="vertical"
              initialValues={config}
              onFinish={handleSaveConfig}
            >
              <Form.Item
                label="CPU使用率阈值 (%)"
                name="cpu_threshold"
                rules={[
                  { required: true, message: '请输入CPU使用率阈值' },
                  { type: 'number', min: 0, max: 100, message: '请输入0-100之间的数值' }
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  max={100}
                  formatter={value => `${value}%`}
                  parser={value => value!.replace('%', '')}
                />
              </Form.Item>

              <Form.Item
                label="内存使用率阈值 (%)"
                name="memory_threshold"
                rules={[
                  { required: true, message: '请输入内存使用率阈值' },
                  { type: 'number', min: 0, max: 100, message: '请输入0-100之间的数值' }
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  max={100}
                  formatter={value => `${value}%`}
                  parser={value => value!.replace('%', '')}
                />
              </Form.Item>

              <Form.Item
                label="磁盘使用率阈值 (%)"
                name="disk_threshold"
                rules={[
                  { required: true, message: '请输入磁盘使用率阈值' },
                  { type: 'number', min: 0, max: 100, message: '请输入0-100之间的数值' }
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  max={100}
                  formatter={value => `${value}%`}
                  parser={value => value!.replace('%', '')}
                />
              </Form.Item>

              <Form.Item
                label="错误率阈值 (%)"
                name="error_threshold"
                rules={[
                  { required: true, message: '请输入错误率阈值' },
                  { type: 'number', min: 0, max: 100, message: '请输入0-100之间的数值' }
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  max={100}
                  formatter={value => `${value}%`}
                  parser={value => value!.replace('%', '')}
                />
              </Form.Item>

              <Divider />

              <Form.Item
                label="启用告警"
                name="alert_enabled"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                label="告警邮箱"
                name="alert_email"
                rules={[
                  { required: true, message: '请输入告警邮箱' },
                  { type: 'email', message: '请输入有效的邮箱地址' }
                ]}
              >
                <Input placeholder="<EMAIL>" />
              </Form.Item>

              <Form.Item>
                <Space>
                  <Button
                    type="primary"
                    htmlType="submit"
                    icon={<SaveOutlined />}
                    loading={loading}
                  >
                    保存配置
                  </Button>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={handleResetConfig}
                  >
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>
        </Col>

        {/* 系统状态概览 */}
        <Col span={12}>
          <Card title="系统状态概览">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Alert
                message="系统运行正常"
                description="所有核心服务运行正常，无严重告警"
                type="success"
                showIcon
              />
              
              <div>
                <Text strong>活跃告警: </Text>
                <Tag color="red">{alerts.filter(a => !a.resolved).length}</Tag>
              </div>
              
              <div>
                <Text strong>已解决告警: </Text>
                <Tag color="green">{alerts.filter(a => a.resolved).length}</Tag>
              </div>
              
              <div>
                <Text strong>系统运行时间: </Text>
                <Text>2天 14小时 32分钟</Text>
              </div>
              
              <div>
                <Text strong>最后检查: </Text>
                <Text>{dayjs().format('YYYY-MM-DD HH:mm:ss')}</Text>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 告警管理 */}
      <Card 
        title="告警管理" 
        extra={
          <Button
            icon={<ReloadOutlined />}
            onClick={refreshAlerts}
            loading={alertsLoading}
          >
            刷新
          </Button>
        }
        style={{ marginTop: 16 }}
      >
        <Table
          columns={alertColumns}
          dataSource={alerts}
          rowKey="id"
          size="small"
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
        />
      </Card>
    </motion.div>
  )
}