import { useState } from 'react'
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  Button,
  Space,
  Tag,
  Progress,
  Alert,
  Tabs,
  Typography,
  Select,
  DatePicker,
  Input,
  Badge,
} from 'antd'
import {
  ReloadOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  InfoCircleOutlined,
  SearchOutlined,
  DownloadOutlined,
} from '@ant-design/icons'
import { motion } from 'framer-motion'
import dayjs from 'dayjs'
import type { ColumnsType } from 'antd/es/table'
import {
  useSystemStats,
  useApiStats,
  useLogs,
  useErrors,
  useExportLogs,
  useExportErrors,
  useExportApiReport,
  type SystemStats,
  type ApiStats,
  type LogEntry,
  type ErrorEntry,
  type LogQueryParams,
  type ErrorQueryParams,
  type ApiStatsQueryParams,
} from '../api/monitoringApi'

const { Title, Text } = Typography
const { RangePicker } = DatePicker
const { Search } = Input
const { TabPane } = Tabs

// 移除重复的类型定义，使用从API模块导入的类型

export default function SystemMonitorPage() {
  const [activeTab, setActiveTab] = useState('overview')
  const [logLevel, setLogLevel] = useState<string>('')
  const [searchText, setSearchText] = useState('')
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs | null, dayjs.Dayjs | null] | null>(null)
  const [logParams, setLogParams] = useState<LogQueryParams>({ page: 1, page_size: 50 })
  const [errorParams, setErrorParams] = useState<ErrorQueryParams>({ page: 1, page_size: 50 })
  const [apiStatsParams, setApiStatsParams] = useState<ApiStatsQueryParams>({})

  // 使用React Query hooks
  const { data: systemStats, isLoading: systemStatsLoading, refetch: refetchSystemStats } = useSystemStats()
  const { data: apiStatsData, isLoading: apiStatsLoading, refetch: refetchApiStats } = useApiStats(apiStatsParams)
  const { data: logsData, isLoading: logsLoading, refetch: refetchLogs } = useLogs(logParams)
  const { data: errorsData, isLoading: errorsLoading, refetch: refetchErrors } = useErrors(errorParams)

  // 导出相关的mutations
  const exportLogsMutation = useExportLogs()
  const exportErrorsMutation = useExportErrors()
  const exportApiReportMutation = useExportApiReport()

  // 处理搜索和过滤参数更新
  const handleLogSearch = () => {
    setLogParams(prev => ({
      ...prev,
      level: logLevel || undefined,
      search: searchText || undefined,
      start_time: dateRange?.[0]?.toISOString(),
      end_time: dateRange?.[1]?.toISOString(),
      page: 1,
    }))
  }

  const handleErrorSearch = () => {
    setErrorParams(prev => ({
      ...prev,
      search: searchText || undefined,
      start_time: dateRange?.[0]?.toISOString(),
      end_time: dateRange?.[1]?.toISOString(),
      page: 1,
    }))
  }

  const handleApiStatsFilter = () => {
    setApiStatsParams(prev => ({
      ...prev,
      start_time: dateRange?.[0]?.toISOString(),
      end_time: dateRange?.[1]?.toISOString(),
    }))
  }

  const getStatusColor = (value: number, thresholds: { warning: number; danger: number }) => {
    if (value >= thresholds.danger) return 'red'
    if (value >= thresholds.warning) return 'orange'
    return 'green'
  }

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'ERROR': return 'red'
      case 'WARN': return 'orange'
      case 'INFO': return 'blue'
      case 'DEBUG': return 'gray'
      default: return 'default'
    }
  }

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400)
    const hours = Math.floor((seconds % 86400) / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return `${days}天 ${hours}小时 ${minutes}分钟`
  }

  // API统计表格列定义
  const apiColumns: ColumnsType<ApiStats> = [
    {
      title: 'API端点',
      dataIndex: 'endpoint',
      key: 'endpoint',
      render: (endpoint: string, record) => (
        <Space>
          <Tag color={record.method === 'GET' ? 'blue' : record.method === 'POST' ? 'green' : 'orange'}>
            {record.method}
          </Tag>
          <Text code>{endpoint}</Text>
        </Space>
      ),
    },
    {
      title: '总调用次数',
      dataIndex: 'total_calls',
      key: 'total_calls',
      render: (count: number) => count.toLocaleString(),
      sorter: (a, b) => a.total_calls - b.total_calls,
    },
    {
      title: '成功率',
      dataIndex: 'success_rate',
      key: 'success_rate',
      render: (rate: number) => (
        <Progress
          percent={rate}
          size="small"
          status={rate >= 99 ? 'success' : rate >= 95 ? 'normal' : 'exception'}
          format={percent => `${percent}%`}
        />
      ),
      sorter: (a, b) => a.success_rate - b.success_rate,
    },
    {
      title: '平均响应时间',
      dataIndex: 'avg_response_time',
      key: 'avg_response_time',
      render: (time: number) => `${time}ms`,
      sorter: (a, b) => a.avg_response_time - b.avg_response_time,
    },
    {
      title: '错误次数',
      dataIndex: 'error_count',
      key: 'error_count',
      render: (count: number) => (
        <Badge count={count} showZero style={{ backgroundColor: count > 100 ? '#ff4d4f' : '#52c41a' }} />
      ),
      sorter: (a, b) => a.error_count - b.error_count,
    },
    {
      title: '最后调用',
      dataIndex: 'last_called',
      key: 'last_called',
      render: (time: string) => dayjs(time).format('HH:mm:ss'),
      sorter: (a, b) => dayjs(a.last_called).unix() - dayjs(b.last_called).unix(),
    },
  ]

  // 日志表格列定义
  const logColumns: ColumnsType<LogEntry> = [
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 120,
      render: (time: string) => dayjs(time).format('HH:mm:ss'),
      sorter: (a, b) => dayjs(a.timestamp).unix() - dayjs(b.timestamp).unix(),
    },
    {
      title: '级别',
      dataIndex: 'level',
      key: 'level',
      width: 80,
      render: (level: string) => <Tag color={getLevelColor(level)}>{level}</Tag>,
      filters: [
        { text: 'ERROR', value: 'ERROR' },
        { text: 'WARN', value: 'WARN' },
        { text: 'INFO', value: 'INFO' },
        { text: 'DEBUG', value: 'DEBUG' },
      ],
      onFilter: (value, record) => record.level === value,
    },
    {
      title: '来源',
      dataIndex: 'source',
      key: 'source',
      width: 120,
      render: (source: string) => <Text code>{source}</Text>,
    },
    {
      title: '消息',
      dataIndex: 'message',
      key: 'message',
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'actions',
      width: 100,
      render: (_, record) => (
        <Button
          type="link"
          size="small"
          icon={<InfoCircleOutlined />}
          onClick={() => {
            // 显示详细信息
            console.log('Log details:', record)
          }}
        >
          详情
        </Button>
      ),
    },
  ]

  // 错误表格列定义
  const errorColumns: ColumnsType<ErrorEntry> = [
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 120,
      render: (time: string) => dayjs(time).format('MM-DD HH:mm'),
      sorter: (a, b) => dayjs(a.timestamp).unix() - dayjs(b.timestamp).unix(),
    },
    {
      title: '错误类型',
      dataIndex: 'error_type',
      key: 'error_type',
      width: 150,
      render: (type: string) => <Tag color="red">{type}</Tag>,
    },
    {
      title: '错误消息',
      dataIndex: 'message',
      key: 'message',
      ellipsis: true,
    },
    {
      title: '次数',
      dataIndex: 'count',
      key: 'count',
      width: 80,
      render: (count: number) => (
        <Badge count={count} showZero style={{ backgroundColor: '#ff4d4f' }} />
      ),
      sorter: (a, b) => a.count - b.count,
    },
    {
      title: '操作',
      key: 'actions',
      width: 100,
      render: (_, record) => (
        <Button
          type="link"
          size="small"
          icon={<InfoCircleOutlined />}
          onClick={() => {
            // 显示错误详情
            console.log('Error details:', record)
          }}
        >
          详情
        </Button>
      ),
    },
  ]

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div style={{ marginBottom: 24 }}>
        <Title level={3} style={{ margin: 0 }}>
          系统监控和运维
        </Title>
        <Text type="secondary">实时监控系统状态、API调用统计和错误追踪</Text>
      </div>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="系统概览" key="overview">
          <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
            <Col span={6}>
              <Card>
                <Statistic
                  title="系统运行时间"
                  value={systemStats ? formatUptime(systemStats.uptime) : '-'}
                  prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="CPU使用率"
                  value={systemStats?.cpu_usage || 0}
                  suffix="%"
                  prefix={
                    <div
                      style={{
                        color: getStatusColor(systemStats?.cpu_usage || 0, { warning: 70, danger: 90 })
                      }}
                    >
                      <WarningOutlined />
                    </div>
                  }
                />
                <Progress
                  percent={systemStats?.cpu_usage || 0}
                  size="small"
                  status={
                    (systemStats?.cpu_usage || 0) >= 90 ? 'exception' :
                    (systemStats?.cpu_usage || 0) >= 70 ? 'normal' : 'success'
                  }
                  showInfo={false}
                  style={{ marginTop: 8 }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="内存使用率"
                  value={systemStats?.memory_usage || 0}
                  suffix="%"
                  prefix={
                    <div
                      style={{
                        color: getStatusColor(systemStats?.memory_usage || 0, { warning: 80, danger: 95 })
                      }}
                    >
                      <WarningOutlined />
                    </div>
                  }
                />
                <Progress
                  percent={systemStats?.memory_usage || 0}
                  size="small"
                  status={
                    (systemStats?.memory_usage || 0) >= 95 ? 'exception' :
                    (systemStats?.memory_usage || 0) >= 80 ? 'normal' : 'success'
                  }
                  showInfo={false}
                  style={{ marginTop: 8 }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="磁盘使用率"
                  value={systemStats?.disk_usage || 0}
                  suffix="%"
                  prefix={
                    <div
                      style={{
                        color: getStatusColor(systemStats?.disk_usage || 0, { warning: 80, danger: 95 })
                      }}
                    >
                      <WarningOutlined />
                    </div>
                  }
                />
                <Progress
                  percent={systemStats?.disk_usage || 0}
                  size="small"
                  status={
                    (systemStats?.disk_usage || 0) >= 95 ? 'exception' :
                    (systemStats?.disk_usage || 0) >= 80 ? 'normal' : 'success'
                  }
                  showInfo={false}
                  style={{ marginTop: 8 }}
                />
              </Card>
            </Col>
          </Row>

          <Row gutter={[16, 16]}>
            <Col span={6}>
              <Card>
                <Statistic
                  title="活跃连接数"
                  value={systemStats?.active_connections || 0}
                  prefix={<InfoCircleOutlined style={{ color: '#1890ff' }} />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="总请求数"
                  value={systemStats?.total_requests || 0}
                  prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="错误率"
                  value={systemStats?.error_rate || 0}
                  suffix="%"
                  prefix={
                    <CloseCircleOutlined
                      style={{
                        color: (systemStats?.error_rate || 0) > 5 ? '#ff4d4f' : '#52c41a'
                      }}
                    />
                  }
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="平均响应时间"
                  value={systemStats?.response_time || 0}
                  suffix="ms"
                  prefix={<InfoCircleOutlined style={{ color: '#1890ff' }} />}
                />
              </Card>
            </Col>
          </Row>

          {systemStats && (systemStats.cpu_usage > 80 || systemStats.memory_usage > 80) && (
            <Alert
              message="系统资源使用率较高"
              description="CPU或内存使用率超过80%，建议检查系统负载情况"
              type="warning"
              showIcon
              style={{ marginTop: 16 }}
            />
          )}
        </TabPane>

        <TabPane tab="API统计" key="api">
          <Card>
            <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
              <Space>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={() => refetchApiStats()}
                  loading={apiStatsLoading}
                >
                  刷新
                </Button>
                <Button 
                  icon={<DownloadOutlined />}
                  onClick={() => exportApiReportMutation.mutate(apiStatsParams)}
                  loading={exportApiReportMutation.isPending}
                >
                  导出报告
                </Button>
              </Space>
              <Space>
                <RangePicker
                  value={dateRange}
                  onChange={setDateRange}
                  showTime
                  format="YYYY-MM-DD HH:mm"
                />
                <Button 
                  icon={<SearchOutlined />}
                  onClick={handleApiStatsFilter}
                >
                  筛选
                </Button>
              </Space>
            </div>
            <Table
              columns={apiColumns}
              dataSource={apiStatsData?.data || []}
              rowKey="endpoint"
              loading={apiStatsLoading}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                total: apiStatsData?.total || 0,
              }}
            />
          </Card>
        </TabPane>

        <TabPane tab="系统日志" key="logs">
          <Card>
            <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
              <Space>
                <Select
                  value={logLevel}
                  onChange={setLogLevel}
                  style={{ width: 120 }}
                >
                  <Select.Option value="">全部级别</Select.Option>
                  <Select.Option value="ERROR">错误</Select.Option>
                  <Select.Option value="WARN">警告</Select.Option>
                  <Select.Option value="INFO">信息</Select.Option>
                  <Select.Option value="DEBUG">调试</Select.Option>
                </Select>
                <Search
                  placeholder="搜索日志内容"
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  onSearch={handleLogSearch}
                  style={{ width: 250 }}
                  enterButton={<SearchOutlined />}
                />
                <Button
                  icon={<ReloadOutlined />}
                  onClick={() => refetchLogs()}
                  loading={logsLoading}
                >
                  刷新
                </Button>
              </Space>
              <Space>
                <RangePicker
                  value={dateRange}
                  onChange={setDateRange}
                  showTime
                  format="YYYY-MM-DD HH:mm"
                />
                <Button 
                  icon={<DownloadOutlined />}
                  onClick={() => exportLogsMutation.mutate(logParams)}
                  loading={exportLogsMutation.isPending}
                >
                  导出日志
                </Button>
              </Space>
            </div>
            <Table
              columns={logColumns}
              dataSource={logsData?.data || []}
              rowKey="id"
              loading={logsLoading}
              size="small"
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                total: logsData?.total || 0,
                current: logsData?.page || 1,
                pageSize: logParams.page_size,
                onChange: (page, pageSize) => {
                  setLogParams(prev => ({ ...prev, page, page_size: pageSize || 50 }))
                },
              }}
            />
          </Card>
        </TabPane>

        <TabPane tab="错误追踪" key="errors">
          <Card>
            <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
              <Space>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={() => refetchErrors()}
                  loading={errorsLoading}
                >
                  刷新
                </Button>
                <Button 
                  icon={<DownloadOutlined />}
                  onClick={() => exportErrorsMutation.mutate(errorParams)}
                  loading={exportErrorsMutation.isPending}
                >
                  导出错误报告
                </Button>
              </Space>
              <Space>
                <Search
                  placeholder="搜索错误信息"
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  onSearch={handleErrorSearch}
                  style={{ width: 250 }}
                  enterButton={<SearchOutlined />}
                />
                <RangePicker
                  value={dateRange}
                  onChange={setDateRange}
                  showTime
                  format="YYYY-MM-DD HH:mm"
                />
                <Button 
                  icon={<SearchOutlined />}
                  onClick={handleErrorSearch}
                >
                  筛选
                </Button>
              </Space>
            </div>
            <Table
              columns={errorColumns}
              dataSource={errorsData?.data || []}
              rowKey="id"
              loading={errorsLoading}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                total: errorsData?.total || 0,
                current: errorsData?.page || 1,
                pageSize: errorParams.page_size,
                onChange: (page, pageSize) => {
                  setErrorParams(prev => ({ ...prev, page, page_size: pageSize || 50 }))
                },
              }}
            />
          </Card>
        </TabPane>
      </Tabs>
    </motion.div>
  )
}