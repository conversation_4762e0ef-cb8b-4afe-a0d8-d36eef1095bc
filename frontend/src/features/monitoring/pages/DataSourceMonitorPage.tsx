import { useState } from 'react'
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  Button,
  Space,
  Tag,
  Progress,
  Alert,
  Typography,
  Timeline,
  Descriptions,
  Badge,
} from 'antd'
import {
  ReloadOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  SyncOutlined,
} from '@ant-design/icons'
import { motion } from 'framer-motion'
import dayjs from 'dayjs'
import type { ColumnsType } from 'antd/es/table'
import { useHealthCheck } from '../api/monitoringApi'

const { Title, Text } = Typography

interface DataSource {
  id: string
  name: string
  type: string
  status: 'healthy' | 'warning' | 'error'
  lastUpdate: string
  responseTime: number
  successRate: number
  errorCount: number
  description: string
}

interface CrawlerStatus {
  id: string
  name: string
  status: 'running' | 'stopped' | 'error'
  lastRun: string
  nextRun: string
  recordsProcessed: number
  errorCount: number
  successRate: number
}

export default function DataSourceMonitorPage() {
  const [refreshing, setRefreshing] = useState(false)
  
  // 使用健康检查API
  const { data: healthCheck, isLoading, refetch } = useHealthCheck()

  // 模拟数据源状态数据
  const dataSources: DataSource[] = [
    {
      id: 'jinjing365',
      name: '进京365网站',
      type: 'Web Crawler',
      status: 'healthy',
      lastUpdate: dayjs().subtract(2, 'minutes').format('YYYY-MM-DD HH:mm:ss'),
      responseTime: 1250,
      successRate: 98.5,
      errorCount: 3,
      description: '检查站数据主要来源'
    },
    {
      id: 'amap_api',
      name: '高德地图API',
      type: 'REST API',
      status: 'healthy',
      lastUpdate: dayjs().subtract(30, 'seconds').format('YYYY-MM-DD HH:mm:ss'),
      responseTime: 156,
      successRate: 99.8,
      errorCount: 1,
      description: '地图和路线规划服务'
    },
    {
      id: 'backup_source',
      name: '备用数据源',
      type: 'Web Crawler',
      status: 'warning',
      lastUpdate: dayjs().subtract(15, 'minutes').format('YYYY-MM-DD HH:mm:ss'),
      responseTime: 3200,
      successRate: 85.2,
      errorCount: 12,
      description: '备用检查站信息源'
    }
  ]

  // 模拟爬虫状态数据
  const crawlerStatus: CrawlerStatus[] = [
    {
      id: 'checkpoint_crawler',
      name: '检查站数据爬虫',
      status: 'running',
      lastRun: dayjs().subtract(3, 'minutes').format('YYYY-MM-DD HH:mm:ss'),
      nextRun: dayjs().add(2, 'minutes').format('YYYY-MM-DD HH:mm:ss'),
      recordsProcessed: 1247,
      errorCount: 2,
      successRate: 99.8
    },
    {
      id: 'traffic_crawler',
      name: '交通状况爬虫',
      status: 'running',
      lastRun: dayjs().subtract(1, 'minutes').format('YYYY-MM-DD HH:mm:ss'),
      nextRun: dayjs().add(4, 'minutes').format('YYYY-MM-DD HH:mm:ss'),
      recordsProcessed: 856,
      errorCount: 0,
      successRate: 100
    },
    {
      id: 'backup_crawler',
      name: '备用数据爬虫',
      status: 'stopped',
      lastRun: dayjs().subtract(2, 'hours').format('YYYY-MM-DD HH:mm:ss'),
      nextRun: '-',
      recordsProcessed: 0,
      errorCount: 5,
      successRate: 0
    }
  ]

  const handleRefresh = async () => {
    setRefreshing(true)
    await refetch()
    setTimeout(() => setRefreshing(false), 1000)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'running':
        return '#52c41a'
      case 'warning':
        return '#faad14'
      case 'error':
      case 'stopped':
        return '#ff4d4f'
      default:
        return '#d9d9d9'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'running':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />
      case 'warning':
        return <WarningOutlined style={{ color: '#faad14' }} />
      case 'error':
      case 'stopped':
        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
      default:
        return <InfoCircleOutlined style={{ color: '#d9d9d9' }} />
    }
  }

  // 数据源表格列定义
  const dataSourceColumns: ColumnsType<DataSource> = [
    {
      title: '数据源',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record) => (
        <Space>
          {getStatusIcon(record.status)}
          <div>
            <div style={{ fontWeight: 500 }}>{name}</div>
            <Text type="secondary" style={{ fontSize: 12 }}>
              {record.description}
            </Text>
          </div>
        </Space>
      ),
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => <Tag>{type}</Tag>,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Badge
          color={getStatusColor(status)}
          text={status === 'healthy' ? '正常' : status === 'warning' ? '警告' : '错误'}
        />
      ),
    },
    {
      title: '响应时间',
      dataIndex: 'responseTime',
      key: 'responseTime',
      render: (time: number) => `${time}ms`,
      sorter: (a, b) => a.responseTime - b.responseTime,
    },
    {
      title: '成功率',
      dataIndex: 'successRate',
      key: 'successRate',
      render: (rate: number) => (
        <Progress
          percent={rate}
          size="small"
          status={rate >= 95 ? 'success' : rate >= 85 ? 'normal' : 'exception'}
          format={percent => `${percent}%`}
        />
      ),
      sorter: (a, b) => a.successRate - b.successRate,
    },
    {
      title: '最后更新',
      dataIndex: 'lastUpdate',
      key: 'lastUpdate',
      render: (time: string) => dayjs(time).fromNow(),
    },
  ]

  // 爬虫状态表格列定义
  const crawlerColumns: ColumnsType<CrawlerStatus> = [
    {
      title: '爬虫名称',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record) => (
        <Space>
          {record.status === 'running' ? (
            <SyncOutlined spin style={{ color: '#52c41a' }} />
          ) : (
            getStatusIcon(record.status)
          )}
          <Text strong>{name}</Text>
        </Space>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Badge
          color={getStatusColor(status)}
          text={status === 'running' ? '运行中' : status === 'stopped' ? '已停止' : '错误'}
        />
      ),
    },
    {
      title: '处理记录数',
      dataIndex: 'recordsProcessed',
      key: 'recordsProcessed',
      render: (count: number) => count.toLocaleString(),
      sorter: (a, b) => a.recordsProcessed - b.recordsProcessed,
    },
    {
      title: '成功率',
      dataIndex: 'successRate',
      key: 'successRate',
      render: (rate: number) => (
        <Progress
          percent={rate}
          size="small"
          status={rate >= 95 ? 'success' : rate >= 85 ? 'normal' : 'exception'}
          format={percent => `${percent}%`}
        />
      ),
    },
    {
      title: '最后运行',
      dataIndex: 'lastRun',
      key: 'lastRun',
      render: (time: string) => dayjs(time).fromNow(),
    },
    {
      title: '下次运行',
      dataIndex: 'nextRun',
      key: 'nextRun',
      render: (time: string) => time === '-' ? '-' : dayjs(time).fromNow(),
    },
  ]

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div style={{ marginBottom: 24 }}>
        <Title level={3} style={{ margin: 0 }}>
          数据源监控
        </Title>
        <Text type="secondary">监控数据采集状态和外部服务健康状况</Text>
      </div>

      {/* 系统健康状态概览 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="数据源总数"
              value={dataSources.length}
              prefix={<InfoCircleOutlined style={{ color: '#1890ff' }} />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="正常运行"
              value={dataSources.filter(ds => ds.status === 'healthy').length}
              prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="警告状态"
              value={dataSources.filter(ds => ds.status === 'warning').length}
              prefix={<WarningOutlined style={{ color: '#faad14' }} />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="错误状态"
              value={dataSources.filter(ds => ds.status === 'error').length}
              prefix={<CloseCircleOutlined style={{ color: '#ff4d4f' }} />}
            />
          </Card>
        </Col>
      </Row>

      {/* 警告提示 */}
      {dataSources.some(ds => ds.status !== 'healthy') && (
        <Alert
          message="数据源状态异常"
          description="部分数据源存在警告或错误状态，请及时处理以确保数据质量"
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {/* 数据源状态表格 */}
      <Card 
        title="数据源状态" 
        extra={
          <Button
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
            loading={refreshing || isLoading}
          >
            刷新
          </Button>
        }
        style={{ marginBottom: 16 }}
      >
        <Table
          columns={dataSourceColumns}
          dataSource={dataSources}
          rowKey="id"
          pagination={false}
          size="small"
        />
      </Card>

      {/* 爬虫状态表格 */}
      <Card title="爬虫状态" style={{ marginBottom: 16 }}>
        <Table
          columns={crawlerColumns}
          dataSource={crawlerStatus}
          rowKey="id"
          pagination={false}
          size="small"
        />
      </Card>

      {/* 系统组件健康状态 */}
      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Card title="系统组件状态">
            <Descriptions column={1} size="small">
              <Descriptions.Item 
                label="数据库"
                labelStyle={{ width: 100 }}
              >
                <Space>
                  <Badge status="success" />
                  <Text>PostgreSQL - 正常运行</Text>
                </Space>
              </Descriptions.Item>
              <Descriptions.Item 
                label="缓存"
                labelStyle={{ width: 100 }}
              >
                <Space>
                  <Badge status="success" />
                  <Text>Redis - 正常运行</Text>
                </Space>
              </Descriptions.Item>
              <Descriptions.Item 
                label="消息队列"
                labelStyle={{ width: 100 }}
              >
                <Space>
                  <Badge status="success" />
                  <Text>WebSocket - 正常运行</Text>
                </Space>
              </Descriptions.Item>
              <Descriptions.Item 
                label="外部API"
                labelStyle={{ width: 100 }}
              >
                <Space>
                  <Badge status="success" />
                  <Text>高德地图API - 正常运行</Text>
                </Space>
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="最近事件">
            <Timeline size="small">
              <Timeline.Item color="green">
                <Text style={{ fontSize: 12 }}>
                  {dayjs().subtract(2, 'minutes').format('HH:mm')} - 检查站数据更新成功
                </Text>
              </Timeline.Item>
              <Timeline.Item color="blue">
                <Text style={{ fontSize: 12 }}>
                  {dayjs().subtract(5, 'minutes').format('HH:mm')} - 系统性能检查完成
                </Text>
              </Timeline.Item>
              <Timeline.Item color="orange">
                <Text style={{ fontSize: 12 }}>
                  {dayjs().subtract(15, 'minutes').format('HH:mm')} - 备用数据源响应缓慢
                </Text>
              </Timeline.Item>
              <Timeline.Item color="green">
                <Text style={{ fontSize: 12 }}>
                  {dayjs().subtract(30, 'minutes').format('HH:mm')} - 定时任务执行成功
                </Text>
              </Timeline.Item>
            </Timeline>
          </Card>
        </Col>
      </Row>
    </motion.div>
  )
}