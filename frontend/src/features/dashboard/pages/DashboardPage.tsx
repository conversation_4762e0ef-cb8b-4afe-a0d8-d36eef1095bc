import { Row, Col, Card, Statistic, Typography, Space, Avatar, List, Progress, Badge, Alert } from 'antd'
import {
  UserOutlined,
  TrophyOutlined,
  EnvironmentOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  QuestionCircleOutlined,
  DatabaseOutlined,
  CloudServerOutlined,
  WarningOutlined,
} from '@ant-design/icons'
import { motion } from 'framer-motion'
import { useAuthStore } from '@/shared/stores/authStore'
import { useCheckpointStats, useDataSourceStatus } from '@/features/checkpoints/api/checkpointsApi'

const { Title, Text } = Typography

// 模拟用户数据
const mockUserStats = {
  totalUsers: 1234,
  activeUsers: 890,
  trialUsers: 234,
  premiumUsers: 156,
}

const mockRecentActivity = [
  {
    id: '1',
    type: 'checkpoint',
    action: '检查站状态更新',
    description: '京藏高速康庄检查站 状态变更为检查中',
    timestamp: '2分钟前',
    avatar: <Avatar icon={<EnvironmentOutlined />} style={{ backgroundColor: '#f56a00' }} />,
  },
  {
    id: '2',
    type: 'user',
    action: '新用户注册',
    description: '张三 注册了新账户',
    timestamp: '5分钟前',
    avatar: <Avatar icon={<UserOutlined />} />,
  },
  {
    id: '3',
    type: 'system',
    action: '数据源更新',
    description: '成功更新了 156 个检查站信息',
    timestamp: '10分钟前',
    avatar: <Avatar icon={<DatabaseOutlined />} style={{ backgroundColor: '#52c41a' }} />,
  },
  {
    id: '4',
    type: 'checkpoint',
    action: '检查站状态更新',
    description: '京承高速密云检查站 状态变更为未检查',
    timestamp: '15分钟前',
    avatar: <Avatar icon={<EnvironmentOutlined />} style={{ backgroundColor: '#52c41a' }} />,
  },
]

export default function DashboardPage() {
  const { user } = useAuthStore()
  
  // API hooks
  const { data: checkpointStats } = useCheckpointStats()
  const { data: dataSourcesData } = useDataSourceStatus()

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  }

  // 计算数据源健康状态
  const totalSources = dataSourcesData?.length || 0
  const onlineSources = dataSourcesData?.filter(s => s.status === 'online').length || 0
  const errorSources = dataSourcesData?.filter(s => s.status === 'error').length || 0
  const healthRate = totalSources > 0 ? Math.round((onlineSources / totalSources) * 100) : 0

  return (
    <div>
      <motion.div
        initial="hidden"
        animate="visible"
        variants={{
          visible: {
            transition: {
              staggerChildren: 0.1,
            },
          },
        }}
      >
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          {/* 欢迎信息 */}
          <motion.div variants={cardVariants}>
            <Card>
              <Title level={3} style={{ marginBottom: 8 }}>
                欢迎回来，{user?.username}！
              </Title>
              <Text type="secondary">
                今天是 {new Date().toLocaleDateString('zh-CN', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  weekday: 'long',
                })}
              </Text>
            </Card>
          </motion.div>

          {/* 系统状态告警 */}
          {errorSources > 0 && (
            <motion.div variants={cardVariants}>
              <Alert
                message="系统异常"
                description={`检测到 ${errorSources} 个数据源出现异常，请及时处理`}
                type="error"
                showIcon
                icon={<WarningOutlined />}
                closable
              />
            </motion.div>
          )}

          {/* 统计卡片 */}
          <Row gutter={[16, 16]}>
            {/* 检查站统计 */}
            <Col xs={24} sm={12} lg={6}>
              <motion.div variants={cardVariants}>
                <Card>
                  <Statistic
                    title="检查站总数"
                    value={checkpointStats?.total || 0}
                    prefix={<EnvironmentOutlined />}
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Card>
              </motion.div>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <motion.div variants={cardVariants}>
                <Card>
                  <Statistic
                    title="检查中"
                    value={checkpointStats?.active || 0}
                    prefix={<ExclamationCircleOutlined />}
                    valueStyle={{ color: '#cf1322' }}
                  />
                </Card>
              </motion.div>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <motion.div variants={cardVariants}>
                <Card>
                  <Statistic
                    title="未检查"
                    value={checkpointStats?.inactive || 0}
                    prefix={<CheckCircleOutlined />}
                    valueStyle={{ color: '#3f8600' }}
                  />
                </Card>
              </motion.div>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <motion.div variants={cardVariants}>
                <Card>
                  <Statistic
                    title="总用户数"
                    value={mockUserStats.totalUsers}
                    prefix={<UserOutlined />}
                    valueStyle={{ color: '#722ed1' }}
                  />
                </Card>
              </motion.div>
            </Col>
          </Row>

          {/* 系统健康状态和用户统计 */}
          <Row gutter={[16, 16]}>
            <Col xs={24} lg={12}>
              <motion.div variants={cardVariants}>
                <Card title="系统健康状态" extra={<Badge status={healthRate >= 90 ? 'success' : 'error'} text={healthRate >= 90 ? '正常' : '异常'} />}>
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Text>数据源连通性</Text>
                      <Progress
                        percent={healthRate}
                        size="small"
                        status={healthRate >= 90 ? 'success' : 'exception'}
                        style={{ width: 120 }}
                      />
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Text>在线数据源</Text>
                      <Text strong>{onlineSources}/{totalSources}</Text>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Text>数据更新频率</Text>
                      <Badge status="success" text="正常" />
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Text>最近更新</Text>
                      <Text type="secondary">{checkpointStats?.recentUpdates || 0} 个检查站</Text>
                    </div>
                  </Space>
                </Card>
              </motion.div>
            </Col>
            <Col xs={24} lg={12}>
              <motion.div variants={cardVariants}>
                <Card title="用户统计" extra={<CloudServerOutlined />}>
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Text>活跃用户</Text>
                      <Text strong style={{ color: '#3f8600' }}>{mockUserStats.activeUsers}</Text>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Text>试用用户</Text>
                      <Text strong style={{ color: '#faad14' }}>{mockUserStats.trialUsers}</Text>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Text>付费用户</Text>
                      <Text strong style={{ color: '#722ed1' }}>{mockUserStats.premiumUsers}</Text>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Text>用户活跃度</Text>
                      <Progress
                        percent={Math.round((mockUserStats.activeUsers / mockUserStats.totalUsers) * 100)}
                        size="small"
                        style={{ width: 120 }}
                      />
                    </div>
                  </Space>
                </Card>
              </motion.div>
            </Col>
          </Row>

          {/* 最近活动 */}
          <motion.div variants={cardVariants}>
            <Card title="最近活动" extra={<Text type="secondary">实时更新</Text>}>
              <List
                itemLayout="horizontal"
                dataSource={mockRecentActivity}
                renderItem={(item) => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={item.avatar}
                      title={item.action}
                      description={
                        <Space direction="vertical" size={0}>
                          <Text>{item.description}</Text>
                          <Text type="secondary" style={{ fontSize: 12 }}>
                            {item.timestamp}
                          </Text>
                        </Space>
                      }
                    />
                  </List.Item>
                )}
              />
            </Card>
          </motion.div>
        </Space>
      </motion.div>
    </div>
  )
}
