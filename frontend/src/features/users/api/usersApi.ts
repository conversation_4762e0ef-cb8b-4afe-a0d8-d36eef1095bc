import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { message } from 'antd'
import apiClient from '@/shared/api/client'
import type { 
  User, 
  PaginatedResponse, 
  QueryParams,
  UpdateProfileRequest,
  SubscriptionRequest,
  PermissionResponse,
  TrialStatusResponse
} from '@/shared/types/api'

// API函数
export const usersApi = {
  // 获取用户列表
  getUsers: async (params?: QueryParams): Promise<PaginatedResponse<User>> => {
    return apiClient.get('/users', { params })
  },

  // 获取单个用户
  getUser: async (id: number): Promise<User> => {
    return apiClient.get(`/users/${id}`)
  },

  // 获取当前用户信息
  getSelfProfile: async (): Promise<User> => {
    return apiClient.get('/users/me')
  },

  // 更新当前用户信息
  updateSelfProfile: async (data: UpdateProfileRequest): Promise<User> => {
    return apiClient.put('/users/me', data)
  },

  // 创建用户
  createUser: async (data: Partial<User>): Promise<User> => {
    return apiClient.post('/users', data)
  },

  // 更新用户
  updateUser: async (id: number, data: Partial<User>): Promise<User> => {
    return apiClient.put(`/users/${id}`, data)
  },

  // 删除用户
  deleteUser: async (id: number): Promise<void> => {
    return apiClient.delete(`/users/${id}`)
  },

  // 获取用户订阅状态
  getSubscription: async (): Promise<User> => {
    return apiClient.get('/users/subscription')
  },

  // 升级用户订阅
  upgradeSubscription: async (data: SubscriptionRequest): Promise<void> => {
    return apiClient.post('/users/subscribe', data)
  },

  // 获取用户权限
  getPermissions: async (feature?: string): Promise<PermissionResponse> => {
    return apiClient.get('/users/permissions', { params: { feature } })
  },

  // 获取试用状态
  getTrialStatus: async (): Promise<TrialStatusResponse> => {
    return apiClient.get('/users/trial-status')
  },

  // 获取用户偏好设置
  getPreferences: async (): Promise<any> => {
    return apiClient.get('/users/preferences')
  },

  // 更新用户偏好设置
  updatePreferences: async (data: any): Promise<any> => {
    return apiClient.put('/users/preferences', data)
  },
}

// React Query Hooks
export const useUsers = (params?: QueryParams) => {
  return useQuery({
    queryKey: ['users', params],
    queryFn: () => usersApi.getUsers(params),
    staleTime: 5 * 60 * 1000, // 5分钟
  })
}

export const useUser = (id: number) => {
  return useQuery({
    queryKey: ['user', id],
    queryFn: () => usersApi.getUser(id),
    enabled: !!id,
  })
}

export const useSelfProfile = () => {
  return useQuery({
    queryKey: ['user', 'me'],
    queryFn: usersApi.getSelfProfile,
  })
}

export const useUpdateSelfProfile = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: usersApi.updateSelfProfile,
    onSuccess: (data) => {
      queryClient.setQueryData(['user', 'me'], data)
      message.success('个人资料更新成功')
    },
    onError: () => {
      message.error('更新失败，请重试')
    },
  })
}

export const useCreateUser = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: usersApi.createUser,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
      message.success('用户创建成功')
    },
    onError: () => {
      message.error('创建失败，请重试')
    },
  })
}

export const useUpdateUser = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: Partial<User> }) =>
      usersApi.updateUser(id, data),
    onSuccess: (data, variables) => {
      queryClient.setQueryData(['user', variables.id], data)
      queryClient.invalidateQueries({ queryKey: ['users'] })
      message.success('用户更新成功')
    },
    onError: () => {
      message.error('更新失败，请重试')
    },
  })
}

export const useDeleteUser = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: usersApi.deleteUser,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
      message.success('用户删除成功')
    },
    onError: () => {
      message.error('删除失败，请重试')
    },
  })
}

// 订阅管理相关hooks
export const useSubscription = () => {
  return useQuery({
    queryKey: ['subscription'],
    queryFn: usersApi.getSubscription,
  })
}

export const useUpgradeSubscription = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: usersApi.upgradeSubscription,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['subscription'] })
      queryClient.invalidateQueries({ queryKey: ['users'] })
      message.success('订阅升级成功')
    },
    onError: () => {
      message.error('订阅升级失败，请重试')
    },
  })
}

export const usePermissions = (feature?: string) => {
  return useQuery({
    queryKey: ['permissions', feature],
    queryFn: () => usersApi.getPermissions(feature),
  })
}

export const useTrialStatus = () => {
  return useQuery({
    queryKey: ['trial-status'],
    queryFn: usersApi.getTrialStatus,
  })
}

export const usePreferences = () => {
  return useQuery({
    queryKey: ['preferences'],
    queryFn: usersApi.getPreferences,
  })
}

export const useUpdatePreferences = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: usersApi.updatePreferences,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['preferences'] })
      message.success('偏好设置更新成功')
    },
    onError: () => {
      message.error('更新失败，请重试')
    },
  })
}
