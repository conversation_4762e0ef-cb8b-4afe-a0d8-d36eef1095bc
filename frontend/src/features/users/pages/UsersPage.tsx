import { useState } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Tag,
  Popconfirm,
  Modal,
  Form,
  Typography,
  Select,
  DatePicker,
  Tooltip,
  Progress,
  Descriptions,
  Drawer,
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  ReloadOutlined,
  CrownOutlined,
  ClockCircleOutlined,
  UserOutlined,
  SettingOutlined,
} from '@ant-design/icons'
import { motion } from 'framer-motion'
import dayjs from 'dayjs'
import { 
  useUsers, 
  useCreateUser, 
  useUpdateUser, 
  useDeleteUser,
  useUpgradeSubscription 
} from '../api/usersApi'
import type { User, SubscriptionRequest } from '@/shared/types/api'
import type { ColumnsType } from 'antd/es/table'

const { Title } = Typography
const { Search } = Input

interface UserFormData {
  username: string
  email: string
  password?: string
  car_plate?: string
  plate_region?: string
}

export default function UsersPage() {
  const [searchText, setSearchText] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [modalVisible, setModalVisible] = useState(false)
  const [subscriptionModalVisible, setSubscriptionModalVisible] = useState(false)
  const [userDetailDrawerVisible, setUserDetailDrawerVisible] = useState(false)
  const [editingUser, setEditingUser] = useState<User | null>(null)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [form] = Form.useForm()
  const [subscriptionForm] = Form.useForm()

  // API hooks
  const { data: usersData, isLoading, refetch } = useUsers({
    page: currentPage,
    pageSize,
    keyword: searchText,
  })

  const createUserMutation = useCreateUser()
  const updateUserMutation = useUpdateUser()
  const deleteUserMutation = useDeleteUser()
  const upgradeSubscriptionMutation = useUpgradeSubscription()

  // 获取订阅状态标签颜色
  const getSubscriptionColor = (subscription: string) => {
    switch (subscription) {
      case 'trial': return 'orange'
      case 'free': return 'default'
      case 'premium': return 'gold'
      default: return 'default'
    }
  }

  // 获取订阅状态文本
  const getSubscriptionText = (subscription: string) => {
    switch (subscription) {
      case 'trial': return '试用期'
      case 'free': return '免费版'
      case 'premium': return '高级版'
      default: return '未知'
    }
  }

  // 计算试用期剩余天数
  const getTrialRemainingDays = (trialExpiry: string | undefined) => {
    if (!trialExpiry) return 0
    const expiry = dayjs(trialExpiry)
    const now = dayjs()
    const remaining = expiry.diff(now, 'day')
    return Math.max(0, remaining)
  }

  // 表格列定义
  const columns: ColumnsType<User> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      ellipsis: true,
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      ellipsis: true,
    },
    {
      title: '车牌信息',
      key: 'car_info',
      width: 120,
      render: (_, record) => {
        if (record.car_plate && record.plate_region) {
          return (
            <Tooltip title={`${record.plate_region} ${record.car_plate}`}>
              <Tag icon={<UserOutlined />}>
                {record.plate_region} {record.car_plate}
              </Tag>
            </Tooltip>
          )
        }
        return <span style={{ color: '#999' }}>未设置</span>
      },
    },
    {
      title: '订阅状态',
      dataIndex: 'subscription',
      key: 'subscription',
      width: 120,
      render: (subscription: string, record) => {
        const isTrialExpired = subscription === 'trial' && 
          record.trial_expiry && 
          dayjs().isAfter(dayjs(record.trial_expiry))
        
        return (
          <Space direction="vertical" size="small">
            <Tag 
              color={getSubscriptionColor(subscription)} 
              icon={subscription === 'premium' ? <CrownOutlined /> : undefined}
            >
              {getSubscriptionText(subscription)}
            </Tag>
            {subscription === 'trial' && record.trial_expiry && (
              <Tooltip title={`试用期到期时间: ${dayjs(record.trial_expiry).format('YYYY-MM-DD HH:mm')}`}>
                <Tag 
                  color={isTrialExpired ? 'red' : 'blue'} 
                  icon={<ClockCircleOutlined />}
                  size="small"
                >
                  {isTrialExpired ? '已过期' : `剩余${getTrialRemainingDays(record.trial_expiry)}天`}
                </Tag>
              </Tooltip>
            )}
          </Space>
        )
      },
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      width: 80,
      render: (role: string) => (
        <Tag color={role === 'admin' ? 'red' : 'blue'}>
          {role || 'user'}
        </Tag>
      ),
    },
    {
      title: '最后登录',
      dataIndex: 'last_login_at',
      key: 'last_login_at',
      width: 120,
      render: (date: string) => date ? dayjs(date).format('MM-DD HH:mm') : '从未登录',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 100,
      render: (date: string) => date ? dayjs(date).format('MM-DD') : '-',
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="link"
              icon={<UserOutlined />}
              onClick={() => handleViewDetail(record)}
            >
              详情
            </Button>
          </Tooltip>
          <Tooltip title="编辑用户">
            <Button
              type="link"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            >
              编辑
            </Button>
          </Tooltip>
          <Tooltip title="管理订阅">
            <Button
              type="link"
              icon={<SettingOutlined />}
              onClick={() => handleManageSubscription(record)}
            >
              订阅
            </Button>
          </Tooltip>
          <Popconfirm
            title="确定删除这个用户吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ]

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchText(value)
    setCurrentPage(1)
  }

  // 处理新建
  const handleCreate = () => {
    setEditingUser(null)
    form.resetFields()
    setModalVisible(true)
  }

  // 处理编辑
  const handleEdit = (user: User) => {
    setEditingUser(user)
    form.setFieldsValue({
      username: user.username,
      email: user.email,
    })
    setModalVisible(true)
  }

  // 处理删除
  const handleDelete = (id: number) => {
    deleteUserMutation.mutate(id)
  }

  // 处理查看详情
  const handleViewDetail = (user: User) => {
    setSelectedUser(user)
    setUserDetailDrawerVisible(true)
  }

  // 处理管理订阅
  const handleManageSubscription = (user: User) => {
    setSelectedUser(user)
    subscriptionForm.setFieldsValue({
      subscription_type: user.subscription === 'trial' ? 'premium' : user.subscription
    })
    setSubscriptionModalVisible(true)
  }

  // 处理订阅升级
  const handleSubscriptionSubmit = async (values: SubscriptionRequest) => {
    if (!selectedUser) return
    
    try {
      await upgradeSubscriptionMutation.mutateAsync(values)
      setSubscriptionModalVisible(false)
      subscriptionForm.resetFields()
      refetch() // 刷新用户列表
    } catch (error) {
      // 错误已在mutation中处理
    }
  }

  // 处理表单提交
  const handleSubmit = async (values: UserFormData) => {
    try {
      if (editingUser) {
        await updateUserMutation.mutateAsync({
          id: editingUser.id,
          data: values,
        })
      } else {
        await createUserMutation.mutateAsync(values)
      }
      setModalVisible(false)
      form.resetFields()
    } catch (error) {
      // 错误已在mutation中处理
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Title level={3} style={{ margin: 0 }}>
              用户管理
            </Title>
            <Space>
              <Search
                placeholder="搜索用户名或邮箱"
                allowClear
                onSearch={handleSearch}
                style={{ width: 250 }}
                enterButton={<SearchOutlined />}
              />
              <Button
                icon={<ReloadOutlined />}
                onClick={() => refetch()}
                loading={isLoading}
              >
                刷新
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleCreate}
              >
                新建用户
              </Button>
            </Space>
          </div>

          <Table
            columns={columns}
            dataSource={usersData?.items || []}
            rowKey="id"
            loading={isLoading}
            pagination={{
              current: currentPage,
              pageSize,
              total: usersData?.total || 0,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              onChange: (page, size) => {
                setCurrentPage(page)
                setPageSize(size || 10)
              },
            }}
          />
        </Space>
      </Card>

      {/* 用户表单模态框 */}
      <Modal
        title={editingUser ? '编辑用户' : '新建用户'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false)
          form.resetFields()
        }}
        footer={null}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="username"
            label="用户名"
            rules={[
              { required: true, message: '请输入用户名' },
              { min: 2, message: '用户名至少2位字符' },
            ]}
          >
            <Input placeholder="请输入用户名" />
          </Form.Item>

          <Form.Item
            name="email"
            label="邮箱"
            rules={[
              { required: true, message: '请输入邮箱' },
              { type: 'email', message: '请输入有效的邮箱地址' },
            ]}
          >
            <Input placeholder="请输入邮箱" />
          </Form.Item>

          <Form.Item
            name="car_plate"
            label="车牌号"
          >
            <Input placeholder="请输入车牌号" />
          </Form.Item>

          <Form.Item
            name="plate_region"
            label="车牌地区"
          >
            <Select placeholder="请选择车牌地区">
              <Select.Option value="京">京</Select.Option>
              <Select.Option value="津">津</Select.Option>
              <Select.Option value="沪">沪</Select.Option>
              <Select.Option value="渝">渝</Select.Option>
              <Select.Option value="冀">冀</Select.Option>
              <Select.Option value="豫">豫</Select.Option>
              <Select.Option value="云">云</Select.Option>
              <Select.Option value="辽">辽</Select.Option>
              <Select.Option value="黑">黑</Select.Option>
              <Select.Option value="湘">湘</Select.Option>
              <Select.Option value="皖">皖</Select.Option>
              <Select.Option value="鲁">鲁</Select.Option>
              <Select.Option value="新">新</Select.Option>
              <Select.Option value="苏">苏</Select.Option>
              <Select.Option value="浙">浙</Select.Option>
              <Select.Option value="赣">赣</Select.Option>
              <Select.Option value="鄂">鄂</Select.Option>
              <Select.Option value="桂">桂</Select.Option>
              <Select.Option value="甘">甘</Select.Option>
              <Select.Option value="晋">晋</Select.Option>
              <Select.Option value="蒙">蒙</Select.Option>
              <Select.Option value="陕">陕</Select.Option>
              <Select.Option value="吉">吉</Select.Option>
              <Select.Option value="闽">闽</Select.Option>
              <Select.Option value="贵">贵</Select.Option>
              <Select.Option value="粤">粤</Select.Option>
              <Select.Option value="青">青</Select.Option>
              <Select.Option value="藏">藏</Select.Option>
              <Select.Option value="川">川</Select.Option>
              <Select.Option value="宁">宁</Select.Option>
              <Select.Option value="琼">琼</Select.Option>
            </Select>
          </Form.Item>

          {!editingUser && (
            <Form.Item
              name="password"
              label="密码"
              rules={[
                { required: true, message: '请输入密码' },
                { min: 6, message: '密码至少6位字符' },
              ]}
            >
              <Input.Password placeholder="请输入密码" />
            </Form.Item>
          )}

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={createUserMutation.isPending || updateUserMutation.isPending}
              >
                {editingUser ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 订阅管理模态框 */}
      <Modal
        title="管理用户订阅"
        open={subscriptionModalVisible}
        onCancel={() => {
          setSubscriptionModalVisible(false)
          subscriptionForm.resetFields()
        }}
        footer={null}
        destroyOnClose
      >
        {selectedUser && (
          <div>
            <Descriptions
              title="用户信息"
              size="small"
              column={1}
              style={{ marginBottom: 24 }}
            >
              <Descriptions.Item label="用户名">{selectedUser.username}</Descriptions.Item>
              <Descriptions.Item label="邮箱">{selectedUser.email}</Descriptions.Item>
              <Descriptions.Item label="当前订阅">
                <Tag color={getSubscriptionColor(selectedUser.subscription || 'free')}>
                  {getSubscriptionText(selectedUser.subscription || 'free')}
                </Tag>
              </Descriptions.Item>
              {selectedUser.subscription === 'trial' && selectedUser.trial_expiry && (
                <Descriptions.Item label="试用期到期">
                  {dayjs(selectedUser.trial_expiry).format('YYYY-MM-DD HH:mm:ss')}
                </Descriptions.Item>
              )}
            </Descriptions>

            <Form
              form={subscriptionForm}
              layout="vertical"
              onFinish={handleSubscriptionSubmit}
            >
              <Form.Item
                name="subscription_type"
                label="订阅类型"
                rules={[{ required: true, message: '请选择订阅类型' }]}
              >
                <Select placeholder="请选择订阅类型">
                  <Select.Option value="free">
                    <Space>
                      <Tag color="default">免费版</Tag>
                      <span>仅可查看检查站信息</span>
                    </Space>
                  </Select.Option>
                  <Select.Option value="premium">
                    <Space>
                      <Tag color="gold" icon={<CrownOutlined />}>高级版</Tag>
                      <span>完整导航功能</span>
                    </Space>
                  </Select.Option>
                </Select>
              </Form.Item>

              <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
                <Space>
                  <Button onClick={() => setSubscriptionModalVisible(false)}>
                    取消
                  </Button>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={upgradeSubscriptionMutation.isPending}
                  >
                    更新订阅
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </div>
        )}
      </Modal>

      {/* 用户详情抽屉 */}
      <Drawer
        title="用户详情"
        placement="right"
        onClose={() => setUserDetailDrawerVisible(false)}
        open={userDetailDrawerVisible}
        width={600}
      >
        {selectedUser && (
          <div>
            <Descriptions
              title="基本信息"
              bordered
              column={1}
              style={{ marginBottom: 24 }}
            >
              <Descriptions.Item label="用户ID">{selectedUser.id}</Descriptions.Item>
              <Descriptions.Item label="用户名">{selectedUser.username}</Descriptions.Item>
              <Descriptions.Item label="邮箱">{selectedUser.email}</Descriptions.Item>
              <Descriptions.Item label="角色">
                <Tag color={selectedUser.role === 'admin' ? 'red' : 'blue'}>
                  {selectedUser.role || 'user'}
                </Tag>
              </Descriptions.Item>
            </Descriptions>

            <Descriptions
              title="车辆信息"
              bordered
              column={1}
              style={{ marginBottom: 24 }}
            >
              <Descriptions.Item label="车牌号">
                {selectedUser.car_plate || '未设置'}
              </Descriptions.Item>
              <Descriptions.Item label="车牌地区">
                {selectedUser.plate_region || '未设置'}
              </Descriptions.Item>
            </Descriptions>

            <Descriptions
              title="订阅信息"
              bordered
              column={1}
              style={{ marginBottom: 24 }}
            >
              <Descriptions.Item label="订阅状态">
                <Tag 
                  color={getSubscriptionColor(selectedUser.subscription || 'free')}
                  icon={selectedUser.subscription === 'premium' ? <CrownOutlined /> : undefined}
                >
                  {getSubscriptionText(selectedUser.subscription || 'free')}
                </Tag>
              </Descriptions.Item>
              {selectedUser.subscription === 'trial' && selectedUser.trial_expiry && (
                <>
                  <Descriptions.Item label="试用期到期">
                    {dayjs(selectedUser.trial_expiry).format('YYYY-MM-DD HH:mm:ss')}
                  </Descriptions.Item>
                  <Descriptions.Item label="剩余天数">
                    <Tag 
                      color={dayjs().isAfter(dayjs(selectedUser.trial_expiry)) ? 'red' : 'blue'}
                      icon={<ClockCircleOutlined />}
                    >
                      {dayjs().isAfter(dayjs(selectedUser.trial_expiry)) 
                        ? '已过期' 
                        : `${getTrialRemainingDays(selectedUser.trial_expiry)}天`
                      }
                    </Tag>
                  </Descriptions.Item>
                </>
              )}
            </Descriptions>

            <Descriptions
              title="时间信息"
              bordered
              column={1}
            >
              <Descriptions.Item label="注册时间">
                {selectedUser.created_at 
                  ? dayjs(selectedUser.created_at).format('YYYY-MM-DD HH:mm:ss')
                  : '未知'
                }
              </Descriptions.Item>
              <Descriptions.Item label="最后登录">
                {selectedUser.last_login_at 
                  ? dayjs(selectedUser.last_login_at).format('YYYY-MM-DD HH:mm:ss')
                  : '从未登录'
                }
              </Descriptions.Item>
            </Descriptions>

            {selectedUser.preferences && (
              <div style={{ marginTop: 24 }}>
                <Title level={5}>用户偏好设置</Title>
                <pre style={{ 
                  background: '#f5f5f5', 
                  padding: 12, 
                  borderRadius: 4,
                  fontSize: 12,
                  overflow: 'auto'
                }}>
                  {JSON.stringify(JSON.parse(selectedUser.preferences), null, 2)}
                </pre>
              </div>
            )}
          </div>
        )}
      </Drawer>
    </motion.div>
  )
}
