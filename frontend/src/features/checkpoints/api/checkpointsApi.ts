import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { message } from 'antd'
import apiClient from '@/shared/api/client'

// 检查站数据类型
export interface Checkpoint {
  id: string
  name: string
  latitude: number
  longitude: number
  address: string
  status: 'active' | 'inactive' | 'unknown'
  severity: number
  type: string
  province: string
  city: string
  district: string
  last_update: string
  source: string
  created_at: string
  updated_at: string
}

// 检查站查询参数
export interface CheckpointQueryParams {
  page?: number
  pageSize?: number
  province?: string
  city?: string
  district?: string
  status?: string
  type?: string
  keyword?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 检查站列表响应
export interface CheckpointListResponse {
  items: Checkpoint[]
  total: number
  page: number
  pageSize: number
}

// 检查站统计数据
export interface CheckpointStats {
  total: number
  active: number
  inactive: number
  unknown: number
  byProvince: Record<string, number>
  byType: Record<string, number>
  recentUpdates: number
}

// 数据源状态
export interface DataSourceStatus {
  name: string
  url: string
  status: 'online' | 'offline' | 'error'
  lastUpdate: string
  nextUpdate: string
  successRate: number
  errorMessage?: string
}

// 获取检查站列表
export const useCheckpoints = (params: CheckpointQueryParams) => {
  return useQuery({
    queryKey: ['checkpoints', params],
    queryFn: async (): Promise<CheckpointListResponse> => {
      const response = await apiClient.get('/v1/checkpoints', { params })
      return response
    },
    staleTime: 30000, // 30秒内不重新请求
  })
}

// 获取检查站详情
export const useCheckpoint = (id: string) => {
  return useQuery({
    queryKey: ['checkpoint', id],
    queryFn: async (): Promise<Checkpoint> => {
      const response = await apiClient.get(`/v1/checkpoints/${id}`)
      return response
    },
    enabled: !!id,
  })
}

// 获取附近检查站
export const useNearbyCheckpoints = (lat: number, lng: number, radius: number) => {
  return useQuery({
    queryKey: ['nearby-checkpoints', lat, lng, radius],
    queryFn: async (): Promise<Checkpoint[]> => {
      const response = await apiClient.get('/v1/checkpoints/nearby', {
        params: { lat, lng, radius }
      })
      return response
    },
    enabled: !!(lat && lng && radius),
  })
}

// 获取检查站统计
export const useCheckpointStats = () => {
  return useQuery({
    queryKey: ['checkpoint-stats'],
    queryFn: async (): Promise<CheckpointStats> => {
      const response = await apiClient.get('/v1/checkpoints/stats')
      return response
    },
    refetchInterval: 60000, // 每分钟刷新一次
  })
}

// 获取数据源状态
export const useDataSourceStatus = () => {
  return useQuery({
    queryKey: ['data-source-status'],
    queryFn: async (): Promise<DataSourceStatus[]> => {
      const response = await apiClient.get('/v1/admin/data-sources/status')
      return response
    },
    refetchInterval: 30000, // 每30秒刷新一次
  })
}

// 更新检查站
export const useUpdateCheckpoint = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<Checkpoint> }) => {
      const response = await apiClient.put(`/v1/checkpoints/${id}`, data)
      return response
    },
    onSuccess: () => {
      message.success('检查站更新成功')
      queryClient.invalidateQueries({ queryKey: ['checkpoints'] })
      queryClient.invalidateQueries({ queryKey: ['checkpoint-stats'] })
    },
    onError: () => {
      message.error('检查站更新失败')
    },
  })
}

// 删除检查站
export const useDeleteCheckpoint = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async (id: string) => {
      await apiClient.delete(`/v1/checkpoints/${id}`)
    },
    onSuccess: () => {
      message.success('检查站删除成功')
      queryClient.invalidateQueries({ queryKey: ['checkpoints'] })
      queryClient.invalidateQueries({ queryKey: ['checkpoint-stats'] })
    },
    onError: () => {
      message.error('检查站删除失败')
    },
  })
}

// 批量更新检查站状态
export const useBatchUpdateCheckpoints = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async ({ ids, data }: { ids: string[]; data: Partial<Checkpoint> }) => {
      const response = await apiClient.put('/v1/checkpoints/batch', { ids, data })
      return response
    },
    onSuccess: () => {
      message.success('批量更新成功')
      queryClient.invalidateQueries({ queryKey: ['checkpoints'] })
      queryClient.invalidateQueries({ queryKey: ['checkpoint-stats'] })
    },
    onError: () => {
      message.error('批量更新失败')
    },
  })
}

// 手动刷新数据源
export const useRefreshDataSource = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async (source: string) => {
      const response = await apiClient.post(`/v1/admin/data-sources/${source}/refresh`)
      return response
    },
    onSuccess: () => {
      message.success('数据源刷新成功')
      queryClient.invalidateQueries({ queryKey: ['checkpoints'] })
      queryClient.invalidateQueries({ queryKey: ['checkpoint-stats'] })
      queryClient.invalidateQueries({ queryKey: ['data-source-status'] })
    },
    onError: () => {
      message.error('数据源刷新失败')
    },
  })
}