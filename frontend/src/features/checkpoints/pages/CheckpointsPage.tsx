import { useState } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Tag,
  Popconfirm,
  Modal,
  Form,
  Typography,
  Select,
  Row,
  Col,
  Statistic,
  Badge,
  Tooltip,
  InputNumber,
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  ReloadOutlined,
  EnvironmentOutlined,
  Exclamation<PERSON>ircleOutlined,
  CheckCircleOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons'
import { motion } from 'framer-motion'
import {
  useCheckpoints,
  useCheckpointStats,
  useUpdateCheckpoint,
  useDeleteCheckpoint,
  useBatchUpdateCheckpoints,
  type Checkpoint,
  type CheckpointQueryParams,
} from '../api/checkpointsApi'
import type { ColumnsType } from 'antd/es/table'

const { Title, Text } = Typography
const { Search } = Input
const { Option } = Select

interface CheckpointFormData {
  name: string
  address: string
  status: 'active' | 'inactive' | 'unknown'
  severity: number
  type: string
  province: string
  city: string
  district: string
}

export default function CheckpointsPage() {
  const [searchText, setSearchText] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [modalVisible, setModalVisible] = useState(false)
  const [editingCheckpoint, setEditingCheckpoint] = useState<Checkpoint | null>(null)
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([])
  const [filters, setFilters] = useState<CheckpointQueryParams>({})
  const [form] = Form.useForm()

  // API hooks
  const queryParams: CheckpointQueryParams = {
    page: currentPage,
    pageSize,
    keyword: searchText,
    ...filters,
  }

  const { data: checkpointsData, isLoading, refetch } = useCheckpoints(queryParams)
  const { data: statsData } = useCheckpointStats()
  const updateCheckpointMutation = useUpdateCheckpoint()
  const deleteCheckpointMutation = useDeleteCheckpoint()
  const batchUpdateMutation = useBatchUpdateCheckpoints()

  // 状态标签渲染
  const renderStatus = (status: string) => {
    const statusConfig = {
      active: { color: 'red', icon: <ExclamationCircleOutlined />, text: '检查中' },
      inactive: { color: 'green', icon: <CheckCircleOutlined />, text: '未检查' },
      unknown: { color: 'default', icon: <QuestionCircleOutlined />, text: '状态未知' },
    }
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.unknown
    return (
      <Tag color={config.color} icon={config.icon}>
        {config.text}
      </Tag>
    )
  }

  // 严重程度渲染
  const renderSeverity = (severity: number) => {
    const colors = ['green', 'blue', 'orange', 'red', 'purple']
    return (
      <Tag color={colors[severity - 1] || 'default'}>
        等级 {severity}
      </Tag>
    )
  }

  // 表格列定义
  const columns: ColumnsType<Checkpoint> = [
    {
      title: '检查站名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
      render: (text, record) => (
        <Space>
          <EnvironmentOutlined />
          <Text strong>{text}</Text>
        </Space>
      ),
    },
    {
      title: '位置',
      key: 'location',
      render: (_, record) => (
        <Space direction="vertical" size={0}>
          <Text>{record.province} {record.city} {record.district}</Text>
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.latitude.toFixed(6)}, {record.longitude.toFixed(6)}
          </Text>
        </Space>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: renderStatus,
      filters: [
        { text: '检查中', value: 'active' },
        { text: '未检查', value: 'inactive' },
        { text: '状态未知', value: 'unknown' },
      ],
    },
    {
      title: '严重程度',
      dataIndex: 'severity',
      key: 'severity',
      render: renderSeverity,
      sorter: true,
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type) => <Tag>{type || '未知'}</Tag>,
    },
    {
      title: '最后更新',
      dataIndex: 'last_update',
      key: 'last_update',
      render: (date: string) => (
        <Tooltip title={new Date(date).toLocaleString()}>
          <Text type="secondary">
            {new Date(date).toLocaleDateString()}
          </Text>
        </Tooltip>
      ),
      sorter: true,
    },
    {
      title: '数据源',
      dataIndex: 'source',
      key: 'source',
      render: (source) => <Tag color="blue">{source}</Tag>,
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定删除这个检查站吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ]

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchText(value)
    setCurrentPage(1)
  }

  // 处理筛选
  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
    setCurrentPage(1)
  }

  // 处理编辑
  const handleEdit = (checkpoint: Checkpoint) => {
    setEditingCheckpoint(checkpoint)
    form.setFieldsValue({
      name: checkpoint.name,
      address: checkpoint.address,
      status: checkpoint.status,
      severity: checkpoint.severity,
      type: checkpoint.type,
      province: checkpoint.province,
      city: checkpoint.city,
      district: checkpoint.district,
    })
    setModalVisible(true)
  }

  // 处理删除
  const handleDelete = (id: string) => {
    deleteCheckpointMutation.mutate(id)
  }

  // 处理批量操作
  const handleBatchUpdate = (data: Partial<Checkpoint>) => {
    if (selectedRowKeys.length === 0) {
      return
    }
    batchUpdateMutation.mutate({ ids: selectedRowKeys, data })
    setSelectedRowKeys([])
  }

  // 处理表单提交
  const handleSubmit = async (values: CheckpointFormData) => {
    if (!editingCheckpoint) return
    
    try {
      await updateCheckpointMutation.mutateAsync({
        id: editingCheckpoint.id,
        data: values,
      })
      setModalVisible(false)
      form.resetFields()
    } catch (error) {
      // 错误已在mutation中处理
    }
  }

  // 表格行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (keys: React.Key[]) => {
      setSelectedRowKeys(keys as string[])
    },
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* 统计卡片 */}
        {statsData && (
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} lg={6}>
              <Card>
                <Statistic
                  title="总检查站数"
                  value={statsData.total}
                  prefix={<EnvironmentOutlined />}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card>
                <Statistic
                  title="检查中"
                  value={statsData.active}
                  valueStyle={{ color: '#cf1322' }}
                  prefix={<Badge status="error" />}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card>
                <Statistic
                  title="未检查"
                  value={statsData.inactive}
                  valueStyle={{ color: '#3f8600' }}
                  prefix={<Badge status="success" />}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card>
                <Statistic
                  title="状态未知"
                  value={statsData.unknown}
                  valueStyle={{ color: '#722ed1' }}
                  prefix={<Badge status="default" />}
                />
              </Card>
            </Col>
          </Row>
        )}

        {/* 主要内容 */}
        <Card>
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Title level={3} style={{ margin: 0 }}>
                检查站管理
              </Title>
              <Space>
                <Search
                  placeholder="搜索检查站名称或地址"
                  allowClear
                  onSearch={handleSearch}
                  style={{ width: 250 }}
                  enterButton={<SearchOutlined />}
                />
                <Button
                  icon={<ReloadOutlined />}
                  onClick={() => refetch()}
                  loading={isLoading}
                >
                  刷新
                </Button>
              </Space>
            </div>

            {/* 筛选器 */}
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={8} lg={6}>
                <Select
                  placeholder="选择省份"
                  allowClear
                  style={{ width: '100%' }}
                  onChange={(value) => handleFilterChange('province', value)}
                >
                  <Option value="北京市">北京市</Option>
                  <Option value="河北省">河北省</Option>
                  <Option value="天津市">天津市</Option>
                </Select>
              </Col>
              <Col xs={24} sm={8} lg={6}>
                <Select
                  placeholder="选择状态"
                  allowClear
                  style={{ width: '100%' }}
                  onChange={(value) => handleFilterChange('status', value)}
                >
                  <Option value="active">检查中</Option>
                  <Option value="inactive">未检查</Option>
                  <Option value="unknown">状态未知</Option>
                </Select>
              </Col>
              <Col xs={24} sm={8} lg={6}>
                <Select
                  placeholder="选择类型"
                  allowClear
                  style={{ width: '100%' }}
                  onChange={(value) => handleFilterChange('type', value)}
                >
                  <Option value="高速">高速</Option>
                  <Option value="国道">国道</Option>
                  <Option value="省道">省道</Option>
                </Select>
              </Col>
            </Row>

            {/* 批量操作 */}
            {selectedRowKeys.length > 0 && (
              <Card size="small" style={{ backgroundColor: '#f6ffed' }}>
                <Space>
                  <Text>已选择 {selectedRowKeys.length} 项</Text>
                  <Button
                    size="small"
                    onClick={() => handleBatchUpdate({ status: 'active' })}
                  >
                    批量设为检查中
                  </Button>
                  <Button
                    size="small"
                    onClick={() => handleBatchUpdate({ status: 'inactive' })}
                  >
                    批量设为未检查
                  </Button>
                  <Button
                    size="small"
                    onClick={() => setSelectedRowKeys([])}
                  >
                    取消选择
                  </Button>
                </Space>
              </Card>
            )}

            <Table
              columns={columns}
              dataSource={checkpointsData?.items || []}
              rowKey="id"
              loading={isLoading}
              rowSelection={rowSelection}
              pagination={{
                current: currentPage,
                pageSize,
                total: checkpointsData?.total || 0,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                onChange: (page, size) => {
                  setCurrentPage(page)
                  setPageSize(size || 10)
                },
              }}
            />
          </Space>
        </Card>
      </Space>

      {/* 编辑检查站模态框 */}
      <Modal
        title="编辑检查站"
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false)
          form.resetFields()
        }}
        footer={null}
        destroyOnClose
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="检查站名称"
                rules={[{ required: true, message: '请输入检查站名称' }]}
              >
                <Input placeholder="请输入检查站名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="type"
                label="检查站类型"
                rules={[{ required: true, message: '请选择检查站类型' }]}
              >
                <Select placeholder="请选择检查站类型">
                  <Option value="高速">高速</Option>
                  <Option value="国道">国道</Option>
                  <Option value="省道">省道</Option>
                  <Option value="其他">其他</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="address"
            label="详细地址"
            rules={[{ required: true, message: '请输入详细地址' }]}
          >
            <Input placeholder="请输入详细地址" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="province"
                label="省份"
                rules={[{ required: true, message: '请输入省份' }]}
              >
                <Input placeholder="请输入省份" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="city"
                label="城市"
                rules={[{ required: true, message: '请输入城市' }]}
              >
                <Input placeholder="请输入城市" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="district"
                label="区县"
              >
                <Input placeholder="请输入区县" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="status"
                label="检查状态"
                rules={[{ required: true, message: '请选择检查状态' }]}
              >
                <Select placeholder="请选择检查状态">
                  <Option value="active">检查中</Option>
                  <Option value="inactive">未检查</Option>
                  <Option value="unknown">状态未知</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="severity"
                label="严重程度"
                rules={[{ required: true, message: '请选择严重程度' }]}
              >
                <InputNumber
                  min={1}
                  max={5}
                  placeholder="1-5级"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={updateCheckpointMutation.isPending}
              >
                更新
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </motion.div>
  )
}