import { Routes, Route, Navigate } from 'react-router-dom'
import { Suspense, lazy } from 'react'
import { Spin } from 'antd'
import AuthGuard from '@/shared/components/AuthGuard'
import AdminGuard from '@/shared/components/AdminGuard'
import MainLayout from '@/shared/components/MainLayout'

// 懒加载页面组件
const LoginPage = lazy(() => import('@/features/auth/pages/LoginPage'))
const RegisterPage = lazy(() => import('@/features/auth/pages/RegisterPage'))
const DashboardPage = lazy(() => import('@/features/dashboard/pages/DashboardPage'))
const UsersPage = lazy(() => import('@/features/users/pages/UsersPage'))
const ProfilePage = lazy(() => import('@/features/users/pages/ProfilePage'))
const CheckpointsPage = lazy(() => import('@/features/checkpoints/pages/CheckpointsPage'))
const DataSourceMonitorPage = lazy(() => import('@/features/monitoring/pages/DataSourceMonitorPage'))

// 加载回退组件
function LoadingFallback() {
  return (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center', 
      height: '200px' 
    }}>
      <Spin size="large" />
    </div>
  )
}

export function AppRouter() {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <Routes>
        {/* 公开路由 */}
        <Route path="/login" element={<LoginPage />} />
        <Route path="/register" element={<RegisterPage />} />
        
        {/* 受保护的路由 */}
        <Route path="/" element={
          <AuthGuard>
            <MainLayout />
          </AuthGuard>
        }>
          <Route index element={<Navigate to="/dashboard" replace />} />
          <Route path="dashboard" element={<DashboardPage />} />
          <Route path="profile" element={<ProfilePage />} />
          
          {/* 管理员专用路由 */}
          <Route path="users" element={
            <AdminGuard>
              <UsersPage />
            </AdminGuard>
          } />
          <Route path="checkpoints" element={
            <AdminGuard>
              <CheckpointsPage />
            </AdminGuard>
          } />
          <Route path="monitoring" element={
            <AdminGuard>
              <DataSourceMonitorPage />
            </AdminGuard>
          } />
        </Route>
        
        {/* 404 重定向 - 根据认证状态重定向 */}
        <Route path="*" element={
          <AuthGuard>
            <Navigate to="/dashboard" replace />
          </AuthGuard>
        } />
      </Routes>
    </Suspense>
  )
}
