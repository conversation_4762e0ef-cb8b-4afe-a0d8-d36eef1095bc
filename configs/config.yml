# 统一配置文件 - 支持开发和生产环境
app:
  name: driving-navigation
  port: ${APP_PORT:8080}
  env: ${APP_ENV:production}

# 数据库配置 - PostgreSQL
database:
  # 优先使用完整的数据库URL
  url: ${DATABASE_URL:}
  
  # 分离字段配置（当URL为空时使用）
  host: ${DB_HOST:postgres-service}
  port: ${DB_PORT:5432}
  name: ${DB_NAME:driving_navigation}
  user: ${DB_USER:driving_nav_user}
  password: ${DB_PASSWORD:driving_nav_2024}
  ssl_mode: ${DB_SSL_MODE:disable}
  
  # 连接池配置
  pool:
    max_open: ${DB_MAX_OPEN:25}
    max_idle: ${DB_MAX_IDLE:5}
    max_lifetime: ${DB_MAX_LIFETIME:300s}
  
  type: postgres

# JWT配置
jwt:
  key: ${JWT_SECRET:driving_nav_jwt_secret_2024}

# Redis配置
redis:
  host: ${REDIS_HOST:redis-service}
  port: ${REDIS_PORT:6379}
  password: ${REDIS_PASSWORD:}
  db: ${REDIS_DB:0}

# 日志配置
log:
  level: ${LOG_LEVEL:info}
  format: ${LOG_FORMAT:json}
  output: ${LOG_OUTPUT:stdout}
  file_path: ${LOG_FILE_PATH:/var/log/app/app.log}
  max_size: ${LOG_MAX_SIZE:100}
  max_backups: ${LOG_MAX_BACKUPS:5}
  max_age: ${LOG_MAX_AGE:30}
  compress: ${LOG_COMPRESS:true}

# 高德地图API配置
amap:
  api_key: ${AMAP_API_KEY:9e471ba01ae18f216cd0fb84032ec7e2}
  web_service_key: ${AMAP_WEB_SERVICE_KEY:9e471ba01ae18f216cd0fb84032ec7e2}
  base_url: ${AMAP_BASE_URL:https://restapi.amap.com}
  timeout: ${AMAP_TIMEOUT:30s}
  rate_limit:
    requests_per_second: ${AMAP_RATE_LIMIT:10}
    burst: ${AMAP_BURST:20}

# 数据源配置
data_sources:
  checkpoint:
    url: ${CHECKPOINT_DATA_URL:https://jinjing365.com}
    timeout: ${CHECKPOINT_TIMEOUT:30s}
    retry_count: ${CHECKPOINT_RETRY:3}
    retry_delay: ${CHECKPOINT_RETRY_DELAY:5s}
    user_agent: ${CHECKPOINT_USER_AGENT:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36}
    
  update_interval: ${DATA_UPDATE_INTERVAL:5m}
  
  cache:
    ttl: ${DATA_CACHE_TTL:10m}
    max_size: ${DATA_CACHE_MAX_SIZE:1000}

# 定时任务配置
scheduler:
  enabled: ${SCHEDULER_ENABLED:true}
  timezone: ${SCHEDULER_TIMEZONE:Asia/Shanghai}
  
  jobs:
    checkpoint_update:
      enabled: ${JOB_CHECKPOINT_UPDATE:true}
      cron: "${JOB_CHECKPOINT_CRON:*/5 * * * *}"
      timeout: ${JOB_CHECKPOINT_TIMEOUT:120s}
      
    data_cleanup:
      enabled: ${JOB_DATA_CLEANUP:true}
      cron: "${JOB_DATA_CLEANUP_CRON:0 2 * * *}"
      timeout: ${JOB_DATA_CLEANUP_TIMEOUT:300s}
      
    trial_check:
      enabled: ${JOB_TRIAL_CHECK:true}
      cron: "${JOB_TRIAL_CHECK_CRON:0 */6 * * *}"
      timeout: ${JOB_TRIAL_CHECK_TIMEOUT:60s}
      
    push_cleanup:
      enabled: ${JOB_PUSH_CLEANUP:true}
      cron: "${JOB_PUSH_CLEANUP_CRON:0 3 * * *}"
      timeout: ${JOB_PUSH_CLEANUP_TIMEOUT:300s}

# WebSocket配置
websocket:
  enabled: ${WEBSOCKET_ENABLED:true}
  port: ${WEBSOCKET_PORT:8081}
  path: ${WEBSOCKET_PATH:/ws}
  max_connections: ${WS_MAX_CONNECTIONS:1000}
  read_buffer_size: ${WS_READ_BUFFER:1024}
  write_buffer_size: ${WS_WRITE_BUFFER:1024}
  ping_period: ${WS_PING_PERIOD:54s}
  pong_wait: ${WS_PONG_WAIT:60s}
  write_wait: ${WS_WRITE_WAIT:10s}
  max_message_size: ${WS_MAX_MESSAGE_SIZE:512}

# 推送通知配置
push:
  firebase:
    enabled: ${FIREBASE_ENABLED:false}
    credentials_file: ${FIREBASE_CREDENTIALS:./configs/firebase-credentials.json}
    
  apns:
    enabled: ${APNS_ENABLED:false}
    key_file: ${APNS_KEY_FILE:./configs/apns-key.p8}
    key_id: ${APNS_KEY_ID:}
    team_id: ${APNS_TEAM_ID:}
    bundle_id: ${APNS_BUNDLE_ID:com.example.beijingnavigation}
    production: ${APNS_PRODUCTION:false}
    
  miniprogram:
    enabled: ${MINIPROGRAM_PUSH_ENABLED:false}
    app_id: ${MINIPROGRAM_APP_ID:}
    app_secret: ${MINIPROGRAM_APP_SECRET:}

# 订阅和权限配置
subscription:
  trial:
    duration: ${TRIAL_DURATION:72h}
    
  features:
    free:
      - view_checkpoints
    trial:
      - view_checkpoints
      - navigation
      - route_planning
    premium:
      - view_checkpoints
      - navigation
      - route_planning
      - advanced_features
      - priority_support

# 监控配置
monitoring:
  metrics:
    enabled: ${METRICS_ENABLED:true}
    port: ${METRICS_PORT:9090}
    path: ${METRICS_PATH:/metrics}

  loki:
    enabled: ${LOKI_ENABLED:false}
    endpoint: ${LOKI_ENDPOINT:http://loki:3100}

  tracing:
    enabled: ${TRACING_ENABLED:false}
    jaeger_endpoint: ${JAEGER_ENDPOINT:http://jaeger:14268/api/traces}