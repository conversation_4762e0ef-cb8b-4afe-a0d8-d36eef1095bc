global:
  scrape_interval: 15s
  evaluation_interval: 15s

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

# Load rules once and periodically evaluate them
rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Go 应用监控配置
  - job_name: 'go-app'
    static_configs:
      - targets: ['app:8080']
    metrics_path: '/metrics'

  # Node Exporter 监控配置
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']

  # Prometheus 自监控配置
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
