// 小程序基础功能测试脚本
// 用于测试小程序的核心功能是否正常工作

const app = getApp()

// 测试结果记录
const testResults = {
  passed: 0,
  failed: 0,
  total: 0,
  details: []
}

// 测试工具函数
function logTest(testName, passed, error = null) {
  testResults.total++
  if (passed) {
    testResults.passed++
    console.log(`✅ ${testName} - 通过`)
  } else {
    testResults.failed++
    console.log(`❌ ${testName} - 失败`, error)
  }
  
  testResults.details.push({
    name: testName,
    passed: passed,
    error: error,
    timestamp: new Date().toISOString()
  })
}

// 延迟函数
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms))
}

// 模拟网络请求测试
async function testNetworkRequest() {
  try {
    const response = await app.request({
      url: '/api/health',
      method: 'GET'
    })
    logTest('后端健康检查', response && response.status === 'ok')
  } catch (error) {
    logTest('后端健康检查', false, error.message)
  }
}

// 测试用户认证功能
async function testUserAuth() {
  try {
    // 测试注册
    const registerData = {
      username: 'testuser_' + Date.now(),
      email: `test_${Date.now()}@example.com`,
      password: '123456',
      platform: 'miniprogram'
    }
    
    const registerResponse = await app.request({
      url: '/api/auth/register',
      method: 'POST',
      data: registerData
    })
    logTest('用户注册', !!registerResponse.id)
    
    // 测试登录
    const loginResponse = await app.request({
      url: '/api/auth/login',
      method: 'POST',
      data: {
        email: registerData.email,
        password: registerData.password,
        platform: 'miniprogram'
      }
    })
    logTest('用户登录', !!loginResponse.token)
    
    // 保存token用于后续测试
    if (loginResponse.token) {
      app.globalData.token = loginResponse.token
      app.globalData.userInfo = loginResponse.User
    }
    
  } catch (error) {
    logTest('用户认证', false, error.message)
  }
}

// 测试检查站API
async function testCheckpointAPI() {
  try {
    // 测试检查站列表
    const checkpointList = await app.request({
      url: '/api/checkpoints/',
      method: 'GET',
      data: { page: 1, pageSize: 5 }
    })
    logTest('检查站列表查询', checkpointList && typeof checkpointList.total === 'number')
    
    // 测试附近检查站
    const nearbyCheckpoints = await app.request({
      url: '/api/checkpoints/nearby',
      method: 'GET',
      data: {
        latitude: 39.9042,
        longitude: 116.4074,
        radius: 10
      }
    })
    logTest('附近检查站查询', nearbyCheckpoints && Array.isArray(nearbyCheckpoints.data))
    
  } catch (error) {
    logTest('检查站API', false, error.message)
  }
}

// 测试位置权限
async function testLocationPermission() {
  return new Promise((resolve) => {
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        logTest('位置权限获取', !!(res.latitude && res.longitude))
        resolve(true)
      },
      fail: (error) => {
        logTest('位置权限获取', false, error.errMsg)
        resolve(false)
      }
    })
  })
}

// 测试存储功能
async function testStorage() {
  try {
    const testKey = 'test_storage_key'
    const testValue = { test: 'data', timestamp: Date.now() }
    
    // 测试存储
    wx.setStorageSync(testKey, testValue)
    logTest('数据存储', true)
    
    // 测试读取
    const storedValue = wx.getStorageSync(testKey)
    logTest('数据读取', storedValue && storedValue.test === 'data')
    
    // 测试删除
    wx.removeStorageSync(testKey)
    const deletedValue = wx.getStorageSync(testKey)
    logTest('数据删除', !deletedValue)
    
  } catch (error) {
    logTest('存储功能', false, error.message)
  }
}

// 测试页面导航
async function testPageNavigation() {
  try {
    // 获取当前页面栈
    const pages = getCurrentPages()
    logTest('页面栈获取', pages && pages.length > 0)
    
    // 测试页面跳转（模拟）
    logTest('页面导航功能', true) // 在测试环境中模拟通过
    
  } catch (error) {
    logTest('页面导航', false, error.message)
  }
}

// 测试网络状态
async function testNetworkStatus() {
  return new Promise((resolve) => {
    wx.getNetworkType({
      success: (res) => {
        logTest('网络状态检测', res.networkType !== 'none')
        resolve(res.networkType !== 'none')
      },
      fail: (error) => {
        logTest('网络状态检测', false, error.errMsg)
        resolve(false)
      }
    })
  })
}

// 测试系统信息
async function testSystemInfo() {
  try {
    const systemInfo = wx.getSystemInfoSync()
    logTest('系统信息获取', !!(systemInfo.platform && systemInfo.version))
    
    // 检查关键系统信息
    logTest('微信版本检查', !!systemInfo.version)
    logTest('设备平台检查', !!systemInfo.platform)
    logTest('屏幕信息检查', !!(systemInfo.screenWidth && systemInfo.screenHeight))
    
  } catch (error) {
    logTest('系统信息', false, error.message)
  }
}

// 主测试函数
async function runBasicFunctionTests() {
  console.log('🚀 开始小程序基础功能测试...')
  
  // 重置测试结果
  testResults.passed = 0
  testResults.failed = 0
  testResults.total = 0
  testResults.details = []
  
  try {
    // 1. 系统基础功能测试
    console.log('📱 测试系统基础功能...')
    await testSystemInfo()
    await testStorage()
    await testPageNavigation()
    
    // 2. 网络相关测试
    console.log('🌐 测试网络功能...')
    const hasNetwork = await testNetworkStatus()
    if (hasNetwork) {
      await testNetworkRequest()
    }
    
    // 3. 权限相关测试
    console.log('🔐 测试权限功能...')
    await testLocationPermission()
    
    // 4. 用户认证测试
    if (hasNetwork) {
      console.log('👤 测试用户认证...')
      await testUserAuth()
      
      // 5. API功能测试
      console.log('🔌 测试API功能...')
      await testCheckpointAPI()
    }
    
  } catch (error) {
    console.error('测试过程中发生错误:', error)
  }
  
  // 输出测试结果
  console.log('\n📊 测试结果汇总:')
  console.log(`总测试数: ${testResults.total}`)
  console.log(`通过: ${testResults.passed}`)
  console.log(`失败: ${testResults.failed}`)
  console.log(`成功率: ${((testResults.passed / testResults.total) * 100).toFixed(2)}%`)
  
  // 显示详细结果
  if (testResults.failed > 0) {
    console.log('\n❌ 失败的测试:')
    testResults.details.filter(t => !t.passed).forEach(test => {
      console.log(`  - ${test.name}: ${test.error}`)
    })
  }
  
  return testResults
}

// 导出测试函数
module.exports = {
  runBasicFunctionTests,
  testResults
}
