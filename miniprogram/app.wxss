/**app.wxss**/
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 200rpx 0;
  box-sizing: border-box;
}

/* 全局样式 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}

/* 通用按钮样式 */
.btn-primary {
  background-color: #1976D2;
  color: white;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 32rpx;
  border: none;
}

.btn-primary:active {
  background-color: #1565C0;
}

.btn-secondary {
  background-color: #ffffff;
  color: #1976D2;
  border: 2rpx solid #1976D2;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 32rpx;
}

.btn-secondary:active {
  background-color: #f5f5f5;
}

/* 卡片样式 */
.card {
  background-color: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 列表项样式 */
.list-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  background-color: white;
  border-bottom: 1rpx solid #f0f0f0;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 24rpx;
}

.list-item-content {
  flex: 1;
}

.list-item-title {
  font-size: 32rpx;
  color: #333333;
  margin-bottom: 8rpx;
}

.list-item-subtitle {
  font-size: 28rpx;
  color: #666666;
}

.list-item-arrow {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.5;
}

/* 状态样式 */
.status-active {
  color: #f44336;
  background-color: #ffebee;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
}

.status-inactive {
  color: #4caf50;
  background-color: #e8f5e8;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
}

.status-unknown {
  color: #ff9800;
  background-color: #fff3e0;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
}

/* 加载状态 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx;
  color: #666666;
  font-size: 28rpx;
}

/* 空状态 */
.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 60rpx;
  color: #999999;
  font-size: 28rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.5;
}

/* 工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-1 {
  flex: 1;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.mt-16 {
  margin-top: 16rpx;
}

.mt-32 {
  margin-top: 32rpx;
}

.mb-16 {
  margin-bottom: 16rpx;
}

.mb-32 {
  margin-bottom: 32rpx;
}

.ml-16 {
  margin-left: 16rpx;
}

.mr-16 {
  margin-right: 16rpx;
}

.p-16 {
  padding: 16rpx;
}

.p-32 {
  padding: 32rpx;
}