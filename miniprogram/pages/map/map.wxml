<!--pages/map/map.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input-wrapper">
      <input 
        class="search-input" 
        placeholder="搜索地点" 
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        bindconfirm="doSearch"
      />
      <button class="search-btn" bindtap="doSearch">搜索</button>
    </view>
  </view>

  <!-- 搜索结果 -->
  <view wx:if="{{showSearch}}" class="search-results">
    <view 
      wx:for="{{searchResults}}" 
      wx:key="index" 
      class="search-result-item"
      data-index="{{index}}"
      bindtap="selectSearchResult"
    >
      <text class="result-name">{{item.name}}</text>
      <text class="result-address">{{item.address}}</text>
    </view>
  </view>

  <!-- 地图 -->
  <map
    id="map"
    class="map"
    latitude="{{latitude}}"
    longitude="{{longitude}}"
    scale="{{scale}}"
    markers="{{markers}}"
    polyline="{{polyline}}"
    show-location="{{true}}"
    bindtap="onMapTap"
    bindmarkertap="onMarkerTap"
  >
    <!-- 回到用户位置按钮 -->
    <cover-view class="location-btn" bindtap="backToUserLocation">
      <cover-image class="location-icon" src="/images/location.png"></cover-image>
    </cover-view>

    <!-- 清除路线按钮 -->
    <cover-view wx:if="{{showRoute}}" class="clear-btn" bindtap="clearRoute">
      <cover-image class="clear-icon" src="/images/clear.png"></cover-image>
    </cover-view>
  </map>

  <!-- 底部工具栏 -->
  <view class="toolbar">
    <view class="tool-item" bindtap="clearRoute">
      <image class="tool-icon" src="/images/clear.png" mode="aspectFit"></image>
      <text class="tool-text">清除</text>
    </view>
    
    <view class="tool-item" bindtap="getUserLocation">
      <image class="tool-icon" src="/images/refresh.png" mode="aspectFit"></image>
      <text class="tool-text">刷新</text>
    </view>
    
    <view class="tool-item" bindtap="loadCheckpoints">
      <image class="tool-icon" src="/images/checkpoint.png" mode="aspectFit"></image>
      <text class="tool-text">检查站</text>
    </view>
  </view>

  <!-- 路线信息面板 -->
  <view wx:if="{{routeInfo}}" class="route-panel">
    <view class="route-header">
      <text class="route-title">推荐路线</text>
      <text class="route-close" bindtap="clearRoute">×</text>
    </view>
    
    <view class="route-info">
      <view class="info-item">
        <text class="info-label">距离</text>
        <text class="info-value">{{(routeInfo.routes[0].distance / 1000).toFixed(1)}}公里</text>
      </view>
      
      <view class="info-item">
        <text class="info-label">时间</text>
        <text class="info-value">{{Math.round(routeInfo.routes[0].duration / 60)}}分钟</text>
      </view>
      
      <view class="info-item">
        <text class="info-label">避开</text>
        <text class="info-value">{{routeInfo.avoided_checkpoints ? routeInfo.avoided_checkpoints.length : 0}}个检查站</text>
      </view>
    </view>
    
    <button class="start-nav-btn btn-primary" bindtap="startNavigation">
      开始导航
    </button>
  </view>

  <!-- 加载提示 -->
  <view wx:if="{{loading}}" class="loading-overlay">
    <view class="loading-content">
      <text>规划路线中...</text>
    </view>
  </view>
</view>