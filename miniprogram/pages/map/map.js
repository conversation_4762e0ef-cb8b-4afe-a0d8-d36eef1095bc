// pages/map/map.js
const app = getApp()

Page({
  data: {
    latitude: 39.9042,
    longitude: 116.4074,
    scale: 13,
    markers: [],
    polyline: [],
    showRoute: false,
    startPoint: null,
    endPoint: null,
    routeInfo: null,
    loading: false,
    searchKeyword: '',
    searchResults: [],
    showSearch: false,
    userLocation: null,
    checkpoints: []
  },

  onLoad(options) {
    this.getUserLocation()
    this.loadCheckpoints()
    
    // 处理页面参数
    if (options) {
      this.handlePageOptions(options)
    }
  },

  onShow() {
    // 强制联网验证
    this.checkNetworkStatus().then((hasNetwork) => {
      if (!hasNetwork) {
        return
      }
      
      // 检查登录状态
      if (!app.globalData.token) {
        wx.showModal({
          title: '登录提示',
          content: '地图功能需要登录后使用',
          confirmText: '去登录',
          success: (res) => {
            if (res.confirm) {
              wx.navigateTo({
                url: '/pages/login/login'
              })
            } else {
              wx.switchTab({
                url: '/pages/index/index'
              })
            }
          }
        })
        return
      }
      
      // 验证token有效性
      this.validateUserToken()
    })
  },

  // 获取用户位置
  getUserLocation() {
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        this.setData({
          latitude: res.latitude,
          longitude: res.longitude,
          userLocation: {
            latitude: res.latitude,
            longitude: res.longitude
          }
        })
        
        // 添加用户位置标记
        this.addUserLocationMarker(res.latitude, res.longitude)
      },
      fail: () => {
        wx.showModal({
          title: '位置权限',
          content: '需要获取您的位置信息来提供导航服务',
          confirmText: '去设置',
          success: (res) => {
            if (res.confirm) {
              wx.openSetting()
            }
          }
        })
      }
    })
  },

  // 添加用户位置标记
  addUserLocationMarker(latitude, longitude) {
    const userMarker = {
      id: 'user',
      latitude: latitude,
      longitude: longitude,
      iconPath: '/images/user-location.png',
      width: 40,
      height: 40,
      title: '我的位置'
    }
    
    this.setData({
      markers: [userMarker, ...this.data.markers]
    })
  },

  // 加载检查站数据
  loadCheckpoints() {
    app.request({
      url: '/api/v1/checkpoints',
      method: 'GET',
      data: {
        page: 1,
        pageSize: 100
      }
    }).then((data) => {
      const checkpoints = data.checkpoints || []
      this.setData({ checkpoints })
      this.addCheckpointMarkers(checkpoints)
    }).catch((err) => {
      console.error('加载检查站失败:', err)
    })
  },

  // 添加检查站标记
  addCheckpointMarkers(checkpoints) {
    const checkpointMarkers = checkpoints.map((checkpoint, index) => ({
      id: `checkpoint_${checkpoint.id}`,
      latitude: checkpoint.latitude,
      longitude: checkpoint.longitude,
      iconPath: this.getCheckpointIcon(checkpoint.status),
      width: 32,
      height: 32,
      title: checkpoint.name,
      checkpoint: checkpoint
    }))
    
    // 保留用户位置标记
    const userMarker = this.data.markers.find(m => m.id === 'user')
    const markers = userMarker ? [userMarker, ...checkpointMarkers] : checkpointMarkers
    
    this.setData({ markers })
  },

  // 获取检查站图标
  getCheckpointIcon(status) {
    switch (status) {
      case 'active':
        return '/images/checkpoint-active.png'
      case 'inactive':
        return '/images/checkpoint-inactive.png'
      default:
        return '/images/checkpoint-unknown.png'
    }
  },

  // 地图点击事件
  onMapTap(e) {
    const { latitude, longitude } = e.detail
    
    if (!this.data.startPoint) {
      // 设置起点
      this.setStartPoint(latitude, longitude)
    } else if (!this.data.endPoint) {
      // 设置终点
      this.setEndPoint(latitude, longitude)
    } else {
      // 重新设置起点
      this.clearRoute()
      this.setStartPoint(latitude, longitude)
    }
  },

  // 标记点击事件
  onMarkerTap(e) {
    const markerId = e.detail.markerId
    const marker = this.data.markers.find(m => m.id === markerId)
    
    if (marker && marker.checkpoint) {
      this.showCheckpointDetail(marker.checkpoint)
    }
  },

  // 显示检查站详情
  showCheckpointDetail(checkpoint) {
    const statusText = this.getStatusText(checkpoint.status)
    
    wx.showModal({
      title: checkpoint.name,
      content: `位置：${checkpoint.location}\n状态：${statusText}`,
      confirmText: '导航到此',
      cancelText: '关闭',
      success: (res) => {
        if (res.confirm) {
          this.navigateToCheckpoint(checkpoint)
        }
      }
    })
  },

  // 导航到检查站
  navigateToCheckpoint(checkpoint) {
    if (!this.data.userLocation) {
      app.showToast('请先获取位置信息')
      return
    }
    
    this.setStartPoint(this.data.userLocation.latitude, this.data.userLocation.longitude)
    this.setEndPoint(checkpoint.latitude, checkpoint.longitude)
  },

  // 设置起点
  setStartPoint(latitude, longitude) {
    const startMarker = {
      id: 'start',
      latitude: latitude,
      longitude: longitude,
      iconPath: '/images/start-point.png',
      width: 36,
      height: 36,
      title: '起点'
    }
    
    // 移除之前的起点和终点标记
    let markers = this.data.markers.filter(m => m.id !== 'start' && m.id !== 'end')
    markers.push(startMarker)
    
    this.setData({
      startPoint: { latitude, longitude },
      endPoint: null,
      markers: markers,
      polyline: [],
      showRoute: false
    })
    
    app.showToast('起点已设置，请选择终点')
  },

  // 设置终点
  setEndPoint(latitude, longitude) {
    const endMarker = {
      id: 'end',
      latitude: latitude,
      longitude: longitude,
      iconPath: '/images/end-point.png',
      width: 36,
      height: 36,
      title: '终点'
    }
    
    let markers = this.data.markers.filter(m => m.id !== 'end')
    markers.push(endMarker)
    
    this.setData({
      endPoint: { latitude, longitude },
      markers: markers
    })
    
    // 开始路线规划
    this.planRoute()
  },

  // 路线规划
  planRoute() {
    if (!this.data.startPoint || !this.data.endPoint) {
      return
    }
    
    // 强制联网验证
    app.checkNetworkConnection().then((hasNetwork) => {
      if (!hasNetwork) return
      
      // 检查导航权限
      app.checkUserPermission('navigation').then((result) => {
        if (!result.hasPermission) {
          app.showPermissionDialog(result.reason, result.subscription)
          return
        }
        
        this.setData({ loading: true })
        app.showLoading('规划路线中...')
        
        const { startPoint, endPoint } = this.data
        
        app.request({
          url: '/api/v1/navigation/route',
          method: 'POST',
          data: {
            origin: {
              latitude: startPoint.latitude,
              longitude: startPoint.longitude
            },
            destination: {
              latitude: endPoint.latitude,
              longitude: endPoint.longitude
            },
            avoid_level: 2 // 中等避让级别
          }
        }).then((data) => {
          app.hideLoading()
          this.setData({ loading: false })
          
          if (data.routes && data.routes.length > 0) {
            this.showRoute(data.routes[0])
            this.setData({ routeInfo: data })
          } else {
            app.showToast('未找到合适路线')
          }
        }).catch((err) => {
          app.hideLoading()
          this.setData({ loading: false })
          app.showToast(err.message || '路线规划失败')
        })
      })
    })
  },

  // 显示路线
  showRoute(route) {
    if (!route.polyline) {
      return
    }
    
    const polyline = [{
      points: route.polyline,
      color: '#1976D2',
      width: 6,
      arrowLine: true
    }]
    
    this.setData({
      polyline: polyline,
      showRoute: true
    })
    
    // 显示路线信息
    this.showRouteInfo(route)
  },

  // 显示路线信息
  showRouteInfo(route) {
    const distance = (route.distance / 1000).toFixed(1)
    const duration = Math.round(route.duration / 60)
    const avoidedCount = route.avoided_checkpoints ? route.avoided_checkpoints.length : 0
    
    wx.showModal({
      title: '路线信息',
      content: `距离：${distance}公里\n预计时间：${duration}分钟\n避开检查站：${avoidedCount}个`,
      confirmText: '开始导航',
      cancelText: '重新规划',
      success: (res) => {
        if (res.confirm) {
          this.startNavigation()
        } else {
          this.clearRoute()
        }
      }
    })
  },

  // 开始导航
  startNavigation() {
    if (!this.data.routeInfo) {
      return
    }
    
    // 这里可以集成第三方导航应用
    wx.showActionSheet({
      itemList: ['高德地图', '腾讯地图', '百度地图'],
      success: (res) => {
        const { startPoint, endPoint } = this.data
        
        switch (res.tapIndex) {
          case 0: // 高德地图
            this.openAmapNavigation(startPoint, endPoint)
            break
          case 1: // 腾讯地图
            this.openTencentMapNavigation(startPoint, endPoint)
            break
          case 2: // 百度地图
            this.openBaiduMapNavigation(startPoint, endPoint)
            break
        }
      }
    })
  },

  // 打开高德地图导航
  openAmapNavigation(start, end) {
    const url = `amapuri://route/plan/?sid=&slat=${start.latitude}&slon=${start.longitude}&sname=起点&did=&dlat=${end.latitude}&dlon=${end.longitude}&dname=终点&dev=0&t=0`
    
    wx.navigateToMiniProgram({
      appId: 'wxd4b0a8a8e0c5c0a0', // 高德地图小程序appid
      path: `pages/index/index?url=${encodeURIComponent(url)}`,
      fail: () => {
        app.showToast('请先安装高德地图')
      }
    })
  },

  // 清除路线
  clearRoute() {
    // 移除起点和终点标记
    const markers = this.data.markers.filter(m => m.id !== 'start' && m.id !== 'end')
    
    this.setData({
      startPoint: null,
      endPoint: null,
      markers: markers,
      polyline: [],
      showRoute: false,
      routeInfo: null
    })
  },

  // 搜索地点
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    })
  },

  // 执行搜索
  doSearch() {
    const keyword = this.data.searchKeyword.trim()
    if (!keyword) {
      return
    }
    
    app.showLoading('搜索中...')
    
    // 这里可以调用地图搜索API
    setTimeout(() => {
      app.hideLoading()
      // 模拟搜索结果
      this.setData({
        searchResults: [
          { name: '北京站', address: '北京市东城区', latitude: 39.9042, longitude: 116.4074 },
          { name: '北京西站', address: '北京市丰台区', latitude: 39.8963, longitude: 116.3220 }
        ],
        showSearch: true
      })
    }, 1000)
  },

  // 选择搜索结果
  selectSearchResult(e) {
    const index = e.currentTarget.dataset.index
    const result = this.data.searchResults[index]
    
    this.setData({
      latitude: result.latitude,
      longitude: result.longitude,
      showSearch: false,
      searchKeyword: result.name
    })
  },

  // 获取状态文本
  getStatusText(status) {
    switch (status) {
      case 'active':
        return '检查中'
      case 'inactive':
        return '未检查'
      case 'unknown':
        return '状态未知'
      default:
        return '未知'
    }
  },

  // 回到用户位置
  backToUserLocation() {
    if (this.data.userLocation) {
      this.setData({
        latitude: this.data.userLocation.latitude,
        longitude: this.data.userLocation.longitude,
        scale: 15
      })
    } else {
      this.getUserLocation()
    }
  },

  // 检查导航权限
  checkNavigationPermission() {
    return new Promise((resolve) => {
      if (!app.globalData.token) {
        resolve(false)
        return
      }

      // 检查用户订阅状态
      app.request({
        url: '/api/v1/users/subscription',
        method: 'GET'
      }).then((data) => {
        const subscription = data.subscription
        if (!subscription) {
          resolve(false)
          return
        }

        const now = new Date()
        
        // 试用期用户可以使用导航功能
        if (subscription.type === 'trial') {
          const trialExpiry = new Date(subscription.trial_expiry)
          resolve(now < trialExpiry)
          return
        }
        
        // 高级版用户可以使用导航功能
        if (subscription.type === 'premium') {
          resolve(true)
          return
        }
        
        // 免费用户不能使用导航功能
        resolve(false)
      }).catch(() => {
        resolve(false)
      })
    })
  },

  // 显示权限不足提示
  showPermissionDenied() {
    wx.showModal({
      title: '功能受限',
      content: '导航功能仅限试用期和高级版用户使用。免费用户只能查看检查站信息。',
      confirmText: '升级会员',
      cancelText: '我知道了',
      success: (res) => {
        if (res.confirm) {
          wx.switchTab({
            url: '/pages/profile/profile'
          })
        }
      }
    })
  },

  // 检查联网状态
  checkNetworkStatus() {
    return new Promise((resolve) => {
      wx.getNetworkType({
        success: (res) => {
          if (res.networkType === 'none') {
            wx.showModal({
              title: '网络连接失败',
              content: '导航功能需要网络连接，请检查网络设置',
              showCancel: false
            })
            resolve(false)
          } else {
            resolve(true)
          }
        },
        fail: () => {
          resolve(false)
        }
      })
    })
  },

  // 打开腾讯地图导航
  openTencentMapNavigation(start, end) {
    const url = `qqmap://map/routeplan?type=drive&from=起点&fromcoord=${start.latitude},${start.longitude}&to=终点&tocoord=${end.latitude},${end.longitude}&policy=0`
    
    wx.navigateToMiniProgram({
      appId: 'wx5a3a7366fd07e119', // 腾讯地图小程序appid
      path: `pages/index/index?url=${encodeURIComponent(url)}`,
      fail: () => {
        app.showToast('请先安装腾讯地图')
      }
    })
  },

  // 打开百度地图导航
  openBaiduMapNavigation(start, end) {
    const url = `baidumap://map/direction?origin=${start.latitude},${start.longitude}&destination=${end.latitude},${end.longitude}&mode=driving`
    
    wx.navigateToMiniProgram({
      appId: 'wx3c4e3d8e8e8e8e8e', // 百度地图小程序appid（示例）
      path: `pages/index/index?url=${encodeURIComponent(url)}`,
      fail: () => {
        app.showToast('请先安装百度地图')
      }
    })
  },

  // 验证用户token
  validateUserToken() {
    if (!app.globalData.token) {
      return
    }

    app.request({
      url: '/api/v1/auth/validate',
      method: 'POST'
    }).then((data) => {
      // Token有效，更新用户信息
      if (data.user) {
        app.globalData.userInfo = data.user
      }
    }).catch((err) => {
      // Token无效，清除登录信息并跳转登录
      console.error('Token验证失败:', err)
      app.clearLoginInfo()
      wx.showModal({
        title: '登录已过期',
        content: '请重新登录后使用',
        confirmText: '去登录',
        showCancel: false,
        success: () => {
          wx.navigateTo({
            url: '/pages/login/login'
          })
        }
      })
    })
  },

  // 处理页面参数（用于分享和跳转）
  handlePageOptions(options) {
    // 处理收藏位置跳转
    if (options.favorite) {
      try {
        const favorite = JSON.parse(decodeURIComponent(options.favorite))
        this.setData({
          latitude: favorite.latitude,
          longitude: favorite.longitude,
          scale: 15
        })
        
        // 添加收藏位置标记
        this.addFavoriteMarker(favorite)
      } catch (e) {
        console.error('解析收藏位置失败:', e)
      }
    }
    
    // 处理路线规划跳转
    if (options.route) {
      try {
        const routeData = JSON.parse(decodeURIComponent(options.route))
        this.setStartPoint(routeData.start.latitude, routeData.start.longitude)
        this.setEndPoint(routeData.end.latitude, routeData.end.longitude)
      } catch (e) {
        console.error('解析路线数据失败:', e)
      }
    }
    
    // 处理分享位置
    if (options.shared) {
      try {
        const sharedLocation = JSON.parse(decodeURIComponent(options.shared))
        this.setData({
          latitude: sharedLocation.latitude,
          longitude: sharedLocation.longitude,
          scale: 15
        })
        
        // 显示分享位置信息
        wx.showModal({
          title: '分享位置',
          content: `${sharedLocation.name}\n${sharedLocation.address}`,
          confirmText: '导航到此',
          cancelText: '关闭',
          success: (res) => {
            if (res.confirm) {
              this.navigateToLocation(sharedLocation)
            }
          }
        })
      } catch (e) {
        console.error('解析分享位置失败:', e)
      }
    }
  },

  // 添加收藏位置标记
  addFavoriteMarker(favorite) {
    const favoriteMarker = {
      id: 'favorite',
      latitude: favorite.latitude,
      longitude: favorite.longitude,
      iconPath: '/images/favorite-marker.png',
      width: 36,
      height: 36,
      title: favorite.name
    }
    
    // 移除之前的收藏标记
    let markers = this.data.markers.filter(m => m.id !== 'favorite')
    markers.push(favoriteMarker)
    
    this.setData({ markers })
  },

  // 导航到指定位置
  navigateToLocation(location) {
    if (!this.data.userLocation) {
      app.showToast('请先获取位置信息')
      return
    }
    
    // 检查导航权限
    this.checkNavigationPermission().then((hasPermission) => {
      if (!hasPermission) {
        this.showPermissionDenied()
        return
      }
      
      this.setStartPoint(this.data.userLocation.latitude, this.data.userLocation.longitude)
      this.setEndPoint(location.latitude, location.longitude)
    })
  }
})