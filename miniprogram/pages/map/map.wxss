/* pages/map/map.wxss */
.container {
  height: 100vh;
  position: relative;
  display: flex;
  flex-direction: column;
}

/* 搜索栏 */
.search-bar {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  right: 20rpx;
  z-index: 10;
}

.search-input-wrapper {
  display: flex;
  background-color: white;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.search-input {
  flex: 1;
  height: 80rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  border: none;
}

.search-btn {
  width: 120rpx;
  height: 80rpx;
  background-color: #1976D2;
  color: white;
  font-size: 28rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 搜索结果 */
.search-results {
  position: absolute;
  top: 120rpx;
  left: 20rpx;
  right: 20rpx;
  background-color: white;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 9;
  max-height: 400rpx;
  overflow-y: auto;
}

.search-result-item {
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  flex-direction: column;
}

.search-result-item:last-child {
  border-bottom: none;
}

.result-name {
  font-size: 30rpx;
  color: #333333;
  margin-bottom: 8rpx;
}

.result-address {
  font-size: 26rpx;
  color: #666666;
}

/* 地图 */
.map {
  flex: 1;
  width: 100%;
}

/* 地图上的按钮 */
.location-btn {
  position: absolute;
  right: 20rpx;
  bottom: 200rpx;
  width: 80rpx;
  height: 80rpx;
  background-color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

.location-icon {
  width: 48rpx;
  height: 48rpx;
}

.clear-btn {
  position: absolute;
  right: 20rpx;
  bottom: 300rpx;
  width: 80rpx;
  height: 80rpx;
  background-color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

.clear-icon {
  width: 48rpx;
  height: 48rpx;
}

/* 底部工具栏 */
.toolbar {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: space-around;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
  padding-bottom: env(safe-area-inset-bottom);
}

.tool-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.tool-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 8rpx;
}

.tool-text {
  font-size: 24rpx;
  color: #666666;
}

/* 路线信息面板 */
.route-panel {
  position: absolute;
  bottom: 140rpx;
  left: 20rpx;
  right: 20rpx;
  background-color: white;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 8;
}

.route-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.route-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.route-close {
  font-size: 48rpx;
  color: #999999;
  line-height: 1;
}

.route-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.info-label {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 8rpx;
}

.info-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
}

.start-nav-btn {
  width: 100%;
  height: 80rpx;
  font-size: 30rpx;
}

/* 加载提示 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 20;
}

.loading-content {
  background-color: white;
  padding: 40rpx 60rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333333;
}