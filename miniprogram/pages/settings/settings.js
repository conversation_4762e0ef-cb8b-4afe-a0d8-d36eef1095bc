// pages/settings/settings.js
const app = getApp()

Page({
  data: {
    preferences: {
      defaultAvoidLevel: 2,
      autoUpdate: true,
      voiceNavigation: true,
      nightMode: false,
      mapStyle: 'standard',
      notificationTypes: ['checkpoint_update', 'route_change']
    },
    avoidLevelOptions: [
      { value: 1, label: '低避让 - 优先时间' },
      { value: 2, label: '中避让 - 平衡路线' },
      { value: 3, label: '高避让 - 优先安全' }
    ],
    mapStyleOptions: [
      { value: 'standard', label: '标准地图' },
      { value: 'satellite', label: '卫星地图' },
      { value: 'night', label: '夜间地图' }
    ],
    notificationOptions: [
      { key: 'checkpoint_update', label: '检查站状态更新', checked: true },
      { key: 'route_change', label: '路线变化提醒', checked: true },
      { key: 'system_alert', label: '系统通知', checked: false }
    ]
  },

  onLoad() {
    this.loadPreferences()
  },

  // 加载用户偏好设置
  loadPreferences() {
    if (!app.globalData.token) {
      wx.showModal({
        title: '登录提示',
        content: '请先登录后使用设置功能',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            })
          } else {
            wx.navigateBack()
          }
        }
      })
      return
    }

    app.request({
      url: '/api/v1/users/preferences',
      method: 'GET'
    }).then((data) => {
      if (data.preferences) {
        this.setData({
          preferences: {
            ...this.data.preferences,
            ...data.preferences
          }
        })
        
        // 更新通知选项的选中状态
        this.updateNotificationOptions()
      }
    }).catch((err) => {
      console.error('加载偏好设置失败:', err)
    })
  },

  // 更新通知选项的选中状态
  updateNotificationOptions() {
    const { notificationTypes } = this.data.preferences
    const notificationOptions = this.data.notificationOptions.map(option => ({
      ...option,
      checked: notificationTypes.includes(option.key)
    }))
    
    this.setData({ notificationOptions })
  },

  // 避让级别改变
  onAvoidLevelChange(e) {
    const value = parseInt(e.detail.value)
    this.setData({
      'preferences.defaultAvoidLevel': value
    })
    this.savePreferences()
  },

  // 开关切换
  onSwitchChange(e) {
    const { field } = e.currentTarget.dataset
    const value = e.detail.value
    
    this.setData({
      [`preferences.${field}`]: value
    })
    this.savePreferences()
  },

  // 地图样式改变
  onMapStyleChange(e) {
    const value = e.detail.value
    this.setData({
      'preferences.mapStyle': value
    })
    this.savePreferences()
  },

  // 通知类型改变
  onNotificationChange(e) {
    const index = e.currentTarget.dataset.index
    const checked = e.detail.value
    
    const notificationOptions = [...this.data.notificationOptions]
    notificationOptions[index].checked = checked
    
    // 更新通知类型数组
    const notificationTypes = notificationOptions
      .filter(option => option.checked)
      .map(option => option.key)
    
    this.setData({
      notificationOptions,
      'preferences.notificationTypes': notificationTypes
    })
    
    this.savePreferences()
  },

  // 保存偏好设置
  savePreferences() {
    if (!app.globalData.token) return
    
    app.request({
      url: '/api/v1/users/preferences',
      method: 'PUT',
      data: {
        preferences: this.data.preferences
      }
    }).then(() => {
      // 静默保存，不显示提示
    }).catch((err) => {
      console.error('保存偏好设置失败:', err)
      app.showToast('保存失败')
    })
  },

  // 清除缓存
  clearCache() {
    wx.showModal({
      title: '清除缓存',
      content: '确定要清除所有缓存数据吗？',
      success: (res) => {
        if (res.confirm) {
          wx.clearStorageSync()
          app.showToast('缓存已清除', 'success')
        }
      }
    })
  },

  // 检查更新
  checkUpdate() {
    app.showLoading('检查中...')
    
    // 检查小程序更新
    const updateManager = wx.getUpdateManager()
    
    updateManager.onCheckForUpdate((res) => {
      app.hideLoading()
      
      if (res.hasUpdate) {
        wx.showModal({
          title: '发现新版本',
          content: '发现新版本，是否立即更新？',
          success: (modalRes) => {
            if (modalRes.confirm) {
              app.showLoading('下载中...')
              
              updateManager.onUpdateReady(() => {
                app.hideLoading()
                wx.showModal({
                  title: '更新完成',
                  content: '新版本已下载完成，是否立即重启应用？',
                  success: (restartRes) => {
                    if (restartRes.confirm) {
                      updateManager.applyUpdate()
                    }
                  }
                })
              })
              
              updateManager.onUpdateFailed(() => {
                app.hideLoading()
                app.showToast('更新失败')
              })
            }
          }
        })
      } else {
        app.showToast('已是最新版本')
      }
    })
  },

  // 意见反馈
  feedback() {
    wx.navigateTo({
      url: '/pages/feedback/feedback'
    })
  },

  // 关于我们
  about() {
    wx.navigateTo({
      url: '/pages/about/about'
    })
  },

  // 联系客服
  contactService() {
    wx.showActionSheet({
      itemList: ['在线客服', '电话客服', '邮件反馈'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            // 在线客服
            wx.navigateToMiniProgram({
              appId: 'wxe5f52902cf4de896', // 客服小程序appid
              success: () => {
                app.showToast('正在连接客服...')
              },
              fail: () => {
                app.showToast('客服暂时不可用')
              }
            })
            break
          case 1:
            // 电话客服
            wx.makePhoneCall({
              phoneNumber: '************'
            })
            break
          case 2:
            // 邮件反馈
            wx.setClipboardData({
              data: '<EMAIL>',
              success: () => {
                app.showToast('邮箱地址已复制')
              }
            })
            break
        }
      }
    })
  },

  // 隐私设置
  privacySettings() {
    wx.showModal({
      title: '隐私设置',
      content: '您可以在系统设置中管理小程序的权限',
      confirmText: '去设置',
      success: (res) => {
        if (res.confirm) {
          wx.openSetting()
        }
      }
    })
  },

  // 获取避让级别文本
  getAvoidLevelText(level) {
    const option = this.data.avoidLevelOptions.find(opt => opt.value === level)
    return option ? option.label : '未知'
  },

  // 获取地图样式文本
  getMapStyleText(style) {
    const option = this.data.mapStyleOptions.find(opt => opt.value === style)
    return option ? option.label : '未知'
  }
})