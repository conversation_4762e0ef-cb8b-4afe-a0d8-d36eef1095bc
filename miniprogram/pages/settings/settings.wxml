<!--pages/settings/settings.wxml-->
<view class="container">
  <!-- 导航偏好 -->
  <view class="section">
    <text class="section-title">导航偏好</text>
    
    <view class="setting-item">
      <text class="setting-label">默认避让级别</text>
      <picker 
        range="{{avoidLevelOptions}}" 
        range-key="label"
        value="{{preferences.defaultAvoidLevel - 1}}"
        bindchange="onAvoidLevelChange"
      >
        <view class="picker-item">
          <text class="picker-text">{{getAvoidLevelText(preferences.defaultAvoidLevel)}}</text>
          <image class="picker-arrow" src="/images/arrow-right.png" mode="aspectFit"></image>
        </view>
      </picker>
    </view>
    
    <view class="setting-item">
      <text class="setting-label">自动更新数据</text>
      <switch 
        checked="{{preferences.autoUpdate}}" 
        data-field="autoUpdate"
        bindchange="onSwitchChange"
      />
    </view>
    
    <view class="setting-item">
      <text class="setting-label">语音导航</text>
      <switch 
        checked="{{preferences.voiceNavigation}}" 
        data-field="voiceNavigation"
        bindchange="onSwitchChange"
      />
    </view>
  </view>

  <!-- 地图设置 -->
  <view class="section">
    <text class="section-title">地图设置</text>
    
    <view class="setting-item">
      <text class="setting-label">地图样式</text>
      <picker 
        range="{{mapStyleOptions}}" 
        range-key="label"
        value="{{preferences.mapStyle}}"
        bindchange="onMapStyleChange"
      >
        <view class="picker-item">
          <text class="picker-text">{{getMapStyleText(preferences.mapStyle)}}</text>
          <image class="picker-arrow" src="/images/arrow-right.png" mode="aspectFit"></image>
        </view>
      </picker>
    </view>
    
    <view class="setting-item">
      <text class="setting-label">夜间模式</text>
      <switch 
        checked="{{preferences.nightMode}}" 
        data-field="nightMode"
        bindchange="onSwitchChange"
      />
    </view>
  </view>

  <!-- 通知设置 -->
  <view class="section">
    <text class="section-title">通知设置</text>
    
    <view 
      wx:for="{{notificationOptions}}" 
      wx:key="key" 
      class="setting-item"
    >
      <text class="setting-label">{{item.label}}</text>
      <switch 
        checked="{{item.checked}}" 
        data-index="{{index}}"
        bindchange="onNotificationChange"
      />
    </view>
  </view>

  <!-- 系统设置 -->
  <view class="section">
    <text class="section-title">系统设置</text>
    
    <view class="setting-item" bindtap="clearCache">
      <text class="setting-label">清除缓存</text>
      <view class="setting-action">
        <text class="action-text">释放存储空间</text>
        <image class="action-arrow" src="/images/arrow-right.png" mode="aspectFit"></image>
      </view>
    </view>
    
    <view class="setting-item" bindtap="checkUpdate">
      <text class="setting-label">检查更新</text>
      <view class="setting-action">
        <text class="action-text">当前版本 v1.0.0</text>
        <image class="action-arrow" src="/images/arrow-right.png" mode="aspectFit"></image>
      </view>
    </view>
    
    <view class="setting-item" bindtap="privacySettings">
      <text class="setting-label">隐私设置</text>
      <view class="setting-action">
        <text class="action-text">管理权限</text>
        <image class="action-arrow" src="/images/arrow-right.png" mode="aspectFit"></image>
      </view>
    </view>
  </view>

  <!-- 帮助与反馈 -->
  <view class="section">
    <text class="section-title">帮助与反馈</text>
    
    <view class="setting-item" bindtap="feedback">
      <text class="setting-label">意见反馈</text>
      <view class="setting-action">
        <text class="action-text">帮助我们改进</text>
        <image class="action-arrow" src="/images/arrow-right.png" mode="aspectFit"></image>
      </view>
    </view>
    
    <view class="setting-item" bindtap="contactService">
      <text class="setting-label">联系客服</text>
      <view class="setting-action">
        <text class="action-text">在线客服</text>
        <image class="action-arrow" src="/images/arrow-right.png" mode="aspectFit"></image>
      </view>
    </view>
    
    <view class="setting-item" bindtap="about">
      <text class="setting-label">关于我们</text>
      <view class="setting-action">
        <text class="action-text">了解更多</text>
        <image class="action-arrow" src="/images/arrow-right.png" mode="aspectFit"></image>
      </view>
    </view>
  </view>
</view>