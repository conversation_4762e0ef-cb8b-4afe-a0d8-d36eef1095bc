/* pages/settings/settings.wxss */
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 40rpx;
}

/* 设置分组 */
.section {
  margin-bottom: 32rpx;
}

.section-title {
  display: block;
  font-size: 28rpx;
  color: #666666;
  padding: 32rpx 32rpx 16rpx;
  font-weight: 500;
}

/* 设置项 */
.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  background-color: white;
  border-bottom: 1rpx solid #f0f0f0;
}

.setting-item:first-of-type {
  border-top-left-radius: 16rpx;
  border-top-right-radius: 16rpx;
}

.setting-item:last-of-type {
  border-bottom: none;
  border-bottom-left-radius: 16rpx;
  border-bottom-right-radius: 16rpx;
}

.setting-label {
  font-size: 30rpx;
  color: #333333;
  flex: 1;
}

/* 选择器样式 */
.picker-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.picker-text {
  font-size: 28rpx;
  color: #666666;
}

.picker-arrow {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.5;
}

/* 开关样式 */
switch {
  transform: scale(0.8);
}

/* 操作项样式 */
.setting-action {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.action-text {
  font-size: 28rpx;
  color: #666666;
}

.action-arrow {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.5;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .setting-item {
    padding: 28rpx 24rpx;
  }
  
  .setting-label {
    font-size: 28rpx;
  }
  
  .picker-text, .action-text {
    font-size: 26rpx;
  }
}