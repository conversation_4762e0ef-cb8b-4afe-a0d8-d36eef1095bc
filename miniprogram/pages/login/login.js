// pages/login/login.js
const app = getApp()

Page({
  data: {
    phone: '',
    code: '',
    countdown: 0,
    canGetCode: true,
    loading: false,
    loginType: 'phone' // phone 或 wechat
  },

  onLoad() {
    // 检查是否已登录
    if (app.globalData.token) {
      wx.navigateBack()
    }
  },

  // 输入手机号
  onPhoneInput(e) {
    this.setData({
      phone: e.detail.value
    })
  },

  // 输入验证码
  onCodeInput(e) {
    this.setData({
      code: e.detail.value
    })
  },

  // 获取验证码
  getVerificationCode() {
    const { phone } = this.data
    
    if (!this.validatePhone(phone)) {
      app.showToast('请输入正确的手机号')
      return
    }

    if (!this.data.canGetCode) {
      return
    }

    app.showLoading('发送中...')
    
    app.request({
      url: '/api/v1/auth/send-code',
      method: 'POST',
      data: { phone }
    }).then(() => {
      app.hideLoading()
      app.showToast('验证码已发送', 'success')
      this.startCountdown()
    }).catch((err) => {
      app.hideLoading()
      app.showToast(err.message || '发送失败')
    })
  },

  // 开始倒计时
  startCountdown() {
    this.setData({
      countdown: 60,
      canGetCode: false
    })

    const timer = setInterval(() => {
      const countdown = this.data.countdown - 1
      
      if (countdown <= 0) {
        clearInterval(timer)
        this.setData({
          countdown: 0,
          canGetCode: true
        })
      } else {
        this.setData({
          countdown
        })
      }
    }, 1000)
  },

  // 手机号登录
  loginWithPhone() {
    const { phone, code } = this.data
    
    if (!this.validatePhone(phone)) {
      app.showToast('请输入正确的手机号')
      return
    }

    if (!code || code.length !== 6) {
      app.showToast('请输入6位验证码')
      return
    }

    this.setData({ loading: true })
    app.showLoading('登录中...')

    app.request({
      url: '/api/v1/auth/login',
      method: 'POST',
      data: {
        phone,
        code,
        type: 'miniprogram'
      }
    }).then((data) => {
      app.hideLoading()
      this.handleLoginSuccess(data)
    }).catch((err) => {
      app.hideLoading()
      this.setData({ loading: false })
      app.showToast(err.message || '登录失败')
    })
  },

  // 微信登录
  loginWithWechat() {
    wx.login({
      success: (res) => {
        if (res.code) {
          this.wechatLogin(res.code)
        } else {
          app.showToast('获取微信授权失败')
        }
      },
      fail: () => {
        app.showToast('微信登录失败')
      }
    })
  },

  // 微信登录请求
  wechatLogin(code) {
    this.setData({ loading: true })
    app.showLoading('登录中...')

    // 获取用户信息
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (userRes) => {
        app.request({
          url: '/api/v1/auth/wechat-login',
          method: 'POST',
          data: {
            code,
            userInfo: userRes.userInfo,
            type: 'miniprogram'
          }
        }).then((data) => {
          app.hideLoading()
          this.handleLoginSuccess(data)
        }).catch((err) => {
          app.hideLoading()
          this.setData({ loading: false })
          app.showToast(err.message || '登录失败')
        })
      },
      fail: () => {
        app.hideLoading()
        this.setData({ loading: false })
        app.showToast('需要授权用户信息')
      }
    })
  },

  // 登录成功处理
  handleLoginSuccess(data) {
    const { token, user } = data
    
    // 保存登录信息
    app.globalData.token = token
    app.globalData.userInfo = user
    
    wx.setStorageSync('token', token)
    wx.setStorageSync('userInfo', user)
    
    app.showToast('登录成功', 'success')
    
    this.setData({ loading: false })
    
    // 延迟跳转，让用户看到成功提示
    setTimeout(() => {
      wx.navigateBack()
    }, 1500)
  },

  // 切换登录方式
  switchLoginType() {
    const loginType = this.data.loginType === 'phone' ? 'wechat' : 'phone'
    this.setData({ loginType })
  },

  // 验证手机号
  validatePhone(phone) {
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(phone)
  },

  // 跳转到注册页面
  goToRegister() {
    // 这里可以跳转到注册页面，或者使用同一个页面的不同模式
    app.showToast('请使用手机号验证码登录')
  },

  // 返回首页
  goBack() {
    wx.navigateBack()
  }
})