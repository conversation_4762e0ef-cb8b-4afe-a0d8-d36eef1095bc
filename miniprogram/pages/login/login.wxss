/* pages/login/login.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1976D2 0%, #42A5F5 100%);
  padding: 60rpx 40rpx 40rpx;
  display: flex;
  flex-direction: column;
}

/* 头部 */
.header {
  text-align: center;
  margin-bottom: 80rpx;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 32rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 16rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 登录表单 */
.login-form {
  background-color: white;
  border-radius: 24rpx;
  padding: 48rpx 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 40rpx;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.input {
  width: 100%;
  height: 88rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 32rpx;
  box-sizing: border-box;
}

.input:focus {
  border-color: #1976D2;
}

.code-input-group {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.code-input {
  flex: 1;
}

.code-btn {
  width: 200rpx;
  height: 88rpx;
  border: 2rpx solid #1976D2;
  border-radius: 12rpx;
  background-color: white;
  color: #1976D2;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.code-btn.active {
  background-color: #1976D2;
  color: white;
}

.code-btn.disabled {
  border-color: #e0e0e0;
  color: #999999;
  background-color: #f5f5f5;
}

.login-btn {
  width: 100%;
  height: 96rpx;
  font-size: 32rpx;
  margin-top: 32rpx;
}

/* 微信登录 */
.wechat-login {
  text-align: center;
}

.wechat-btn {
  width: 100%;
  height: 96rpx;
  background-color: #07C160;
  color: white;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  font-size: 32rpx;
  border: none;
}

.wechat-btn:active {
  background-color: #06AD56;
}

.wechat-icon {
  width: 48rpx;
  height: 48rpx;
}

/* 切换登录方式 */
.switch-login {
  text-align: center;
  margin-top: 32rpx;
}

.switch-text {
  color: #1976D2;
  font-size: 28rpx;
  text-decoration: underline;
}

/* 协议说明 */
.agreement {
  text-align: center;
  margin-bottom: 60rpx;
}

.agreement-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

.link {
  color: white;
  text-decoration: underline;
}

/* 功能说明 */
.features {
  display: flex;
  justify-content: space-around;
  margin-top: auto;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.feature-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 16rpx;
  opacity: 0.8;
}

.feature-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
}