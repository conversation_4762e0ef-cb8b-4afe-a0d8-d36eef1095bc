<!--pages/login/login.wxml-->
<view class="container">
  <!-- 头部 -->
  <view class="header">
    <image class="logo" src="/images/logo.png" mode="aspectFit"></image>
    <text class="title">进京导航助手</text>
    <text class="subtitle">智能避让检查站，安全便捷出行</text>
  </view>

  <!-- 登录表单 -->
  <view class="login-form">
    <!-- 手机号登录 -->
    <view wx:if="{{loginType === 'phone'}}" class="phone-login">
      <view class="form-group">
        <text class="label">手机号</text>
        <input 
          class="input" 
          type="number" 
          placeholder="请输入手机号" 
          value="{{phone}}"
          bindinput="onPhoneInput"
          maxlength="11"
        />
      </view>
      
      <view class="form-group">
        <text class="label">验证码</text>
        <view class="code-input-group">
          <input 
            class="input code-input" 
            type="number" 
            placeholder="请输入验证码" 
            value="{{code}}"
            bindinput="onCodeInput"
            maxlength="6"
          />
          <button 
            class="code-btn {{canGetCode ? 'active' : 'disabled'}}" 
            bindtap="getVerificationCode"
            disabled="{{!canGetCode}}"
          >
            {{canGetCode ? '获取验证码' : countdown + 's'}}
          </button>
        </view>
      </view>
      
      <button 
        class="login-btn btn-primary" 
        bindtap="loginWithPhone"
        disabled="{{loading}}"
      >
        {{loading ? '登录中...' : '登录'}}
      </button>
    </view>

    <!-- 微信登录 -->
    <view wx:else class="wechat-login">
      <button 
        class="wechat-btn" 
        bindtap="loginWithWechat"
        disabled="{{loading}}"
      >
        <image class="wechat-icon" src="/images/wechat.png" mode="aspectFit"></image>
        <text>{{loading ? '登录中...' : '微信快速登录'}}</text>
      </button>
    </view>

    <!-- 切换登录方式 -->
    <view class="switch-login">
      <text class="switch-text" bindtap="switchLoginType">
        {{loginType === 'phone' ? '使用微信登录' : '使用手机号登录'}}
      </text>
    </view>
  </view>

  <!-- 协议说明 -->
  <view class="agreement">
    <text class="agreement-text">
      登录即表示同意
      <text class="link">《用户协议》</text>
      和
      <text class="link">《隐私政策》</text>
    </text>
  </view>

  <!-- 功能说明 -->
  <view class="features">
    <view class="feature-item">
      <image class="feature-icon" src="/images/shield.png" mode="aspectFit"></image>
      <text class="feature-text">3天免费试用</text>
    </view>
    <view class="feature-item">
      <image class="feature-icon" src="/images/realtime.png" mode="aspectFit"></image>
      <text class="feature-text">实时数据更新</text>
    </view>
    <view class="feature-item">
      <image class="feature-icon" src="/images/smart.png" mode="aspectFit"></image>
      <text class="feature-text">智能路线规划</text>
    </view>
  </view>
</view>