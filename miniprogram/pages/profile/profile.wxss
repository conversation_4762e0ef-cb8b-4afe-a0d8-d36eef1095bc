/* pages/profile/profile.wxss */
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 40rpx;
}

/* 用户信息区域 */
.user-section {
  background: linear-gradient(135deg, #1976D2 0%, #42A5F5 100%);
  padding: 60rpx 32rpx 40rpx;
  margin-bottom: 32rpx;
}

.login-prompt {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.avatar-placeholder {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-bottom: 24rpx;
  opacity: 0.8;
}

.login-text {
  font-size: 32rpx;
  color: white;
  margin-bottom: 32rpx;
}

.login-btn {
  width: 200rpx;
  height: 72rpx;
  font-size: 28rpx;
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 32rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.user-details {
  flex: 1;
}

.username {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 16rpx;
}

.subscription-badge {
  display: inline-block;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  color: white;
}

.subscription-trial {
  background-color: #ff9800;
}

.subscription-premium {
  background-color: #4caf50;
}

.subscription-free {
  background-color: rgba(255, 255, 255, 0.3);
}

.logout-btn {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: underline;
}

/* 会员信息 */
.subscription-section {
  margin: 0 16rpx 32rpx;
}

.subscription-card {
  background-color: white;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.subscription-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.subscription-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.upgrade-btn {
  background-color: #1976D2;
  color: white;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.trial-info {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.trial-text {
  font-size: 28rpx;
  color: #ff9800;
}

.trial-progress {
  width: 100%;
}

.premium-info, .free-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.premium-text, .free-text {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.expire-text, .free-desc {
  font-size: 24rpx;
  color: #666666;
}

/* 使用统计 */
.stats-section {
  margin: 0 16rpx 32rpx;
}

.stats-card {
  background-color: white;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.stats-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 24rpx;
}

.stats-grid {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #1976D2;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666666;
}

/* 功能菜单 */
.menu-section {
  background-color: white;
  margin: 0 16rpx 32rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 24rpx;
}

.menu-content {
  flex: 1;
}

.menu-title {
  display: block;
  font-size: 30rpx;
  color: #333333;
  margin-bottom: 8rpx;
}

.menu-subtitle {
  font-size: 26rpx;
  color: #666666;
}

.menu-arrow {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.5;
}

/* 其他操作 */
.other-section {
  background-color: white;
  margin: 0 16rpx 32rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.other-item, .share-btn {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background-color: transparent;
  border: none;
  width: 100%;
  text-align: left;
  font-size: 30rpx;
  color: #333333;
}

.share-btn {
  border-bottom: none;
}

.other-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 24rpx;
}

.other-text {
  font-size: 30rpx;
  color: #333333;
}

/* 版本信息 */
.version-info {
  text-align: center;
  padding: 32rpx;
}

.version-text {
  font-size: 24rpx;
  color: #999999;
}