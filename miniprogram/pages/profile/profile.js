// pages/profile/profile.js
const app = getApp()

Page({
  data: {
    userInfo: null,
    hasUserInfo: false,
    subscription: null,
    stats: null,
    menuItems: [
      {
        icon: '/images/settings.png',
        title: '设置',
        subtitle: '个人偏好设置',
        url: '/pages/settings/settings'
      },
      {
        icon: '/images/subscription.png',
        title: '会员服务',
        subtitle: '升级高级版',
        url: '/pages/subscription/subscription'
      },
      {
        icon: '/images/history.png',
        title: '使用记录',
        subtitle: '查看历史路线',
        url: '/pages/history/history'
      },
      {
        icon: '/images/feedback.png',
        title: '意见反馈',
        subtitle: '帮助我们改进',
        url: '/pages/feedback/feedback'
      },
      {
        icon: '/images/about.png',
        title: '关于我们',
        subtitle: '了解更多信息',
        url: '/pages/about/about'
      }
    ]
  },

  onLoad() {
    this.getUserInfo()
  },

  onShow() {
    this.getUserInfo()
    this.loadUserStats()
    this.loadSubscription()
  },

  // 获取用户信息
  getUserInfo() {
    if (app.globalData.userInfo) {
      this.setData({
        userInfo: app.globalData.userInfo,
        hasUserInfo: true
      })
    } else {
      this.setData({
        hasUserInfo: false
      })
    }
  },

  // 加载用户统计
  loadUserStats() {
    if (!app.globalData.token) return
    
    app.request({
      url: '/api/v1/users/stats',
      method: 'GET'
    }).then((data) => {
      this.setData({
        stats: data.stats
      })
    }).catch((err) => {
      console.error('加载用户统计失败:', err)
    })
  },

  // 加载订阅信息
  loadSubscription() {
    if (!app.globalData.token) return
    
    app.request({
      url: '/api/v1/users/subscription',
      method: 'GET'
    }).then((data) => {
      this.setData({
        subscription: data.subscription
      })
    }).catch((err) => {
      console.error('加载订阅信息失败:', err)
    })
  },

  // 登录
  goToLogin() {
    wx.navigateTo({
      url: '/pages/login/login'
    })
  },

  // 菜单项点击
  onMenuTap(e) {
    const index = e.currentTarget.dataset.index
    const item = this.data.menuItems[index]
    
    if (!this.data.hasUserInfo && item.url !== '/pages/about/about') {
      this.showLoginTip()
      return
    }
    
    // 检查页面是否存在，如果不存在则显示开发中提示
    if (this.isPageExists(item.url)) {
      wx.navigateTo({
        url: item.url
      })
    } else {
      app.showToast('功能开发中，敬请期待')
    }
  },

  // 检查页面是否存在
  isPageExists(url) {
    const existingPages = [
      '/pages/settings/settings'
    ]
    
    return existingPages.includes(url)
  },

  // 显示登录提示
  showLoginTip() {
    wx.showModal({
      title: '登录提示',
      content: '请先登录后使用此功能',
      confirmText: '去登录',
      success: (res) => {
        if (res.confirm) {
          this.goToLogin()
        }
      }
    })
  },

  // 升级会员
  upgradeSubscription() {
    if (!this.data.hasUserInfo) {
      this.showLoginTip()
      return
    }
    
    // 强制联网验证
    app.checkNetworkConnection().then((hasNetwork) => {
      if (!hasNetwork) return
      
      const subscription = this.data.subscription
      let content = '升级高级版可享受完整的导航功能和更多服务'
      
      if (subscription && subscription.type === 'trial') {
        const remainingDays = this.calculateRemainingDays(subscription.trial_expiry)
        if (remainingDays > 0) {
          content = `您的试用期还剩${remainingDays}天，升级高级版享受完整功能`
        } else {
          content = '您的试用期已过期，升级高级版继续使用导航功能'
        }
      }
      
      wx.showModal({
        title: '升级高级版',
        content: content,
        confirmText: '立即升级',
        success: (res) => {
          if (res.confirm) {
            this.showUpgradeOptions()
          }
        }
      })
    })
  },

  // 显示升级选项
  showUpgradeOptions() {
    wx.showActionSheet({
      itemList: ['月度会员 ¥19.9', '季度会员 ¥49.9', '年度会员 ¥159.9'],
      success: (res) => {
        const plans = ['monthly', 'quarterly', 'yearly']
        const plan = plans[res.tapIndex]
        this.purchaseSubscription(plan)
      }
    })
  },

  // 购买订阅
  purchaseSubscription(plan) {
    app.showLoading('处理中...')
    
    app.request({
      url: '/api/v1/users/subscribe',
      method: 'POST',
      data: { plan }
    }).then((data) => {
      app.hideLoading()
      
      if (data.payment_url) {
        // 跳转到支付页面
        wx.navigateToMiniProgram({
          appId: data.payment_appid,
          path: data.payment_path,
          success: () => {
            app.showToast('请完成支付')
          },
          fail: () => {
            app.showToast('支付失败')
          }
        })
      } else {
        app.showToast('订阅成功', 'success')
        this.loadSubscription()
      }
    }).catch((err) => {
      app.hideLoading()
      app.showToast(err.message || '订阅失败')
    })
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          app.clearLoginInfo()
          this.setData({
            userInfo: null,
            hasUserInfo: false,
            subscription: null,
            stats: null
          })
          app.showToast('已退出登录')
        }
      }
    })
  },

  // 获取订阅类型文本
  getSubscriptionTypeText(type) {
    switch (type) {
      case 'trial':
        return '试用版'
      case 'premium':
        return '高级版'
      case 'free':
        return '免费版'
      default:
        return '未知'
    }
  },

  // 获取订阅状态样式
  getSubscriptionClass(type) {
    switch (type) {
      case 'trial':
        return 'subscription-trial'
      case 'premium':
        return 'subscription-premium'
      case 'free':
        return 'subscription-free'
      default:
        return 'subscription-free'
    }
  },

  // 分享小程序
  onShareAppMessage() {
    return {
      title: '进京导航助手 - 智能避让检查站',
      path: '/pages/index/index',
      imageUrl: '/images/share.png'
    }
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '进京导航助手 - 智能避让检查站，安全便捷出行',
      imageUrl: '/images/share.png'
    }
  },

  // 计算剩余天数
  calculateRemainingDays(expiryDate) {
    if (!expiryDate) return 0
    
    const now = new Date()
    const expiry = new Date(expiryDate)
    const diffTime = expiry - now
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    return Math.max(0, diffDays)
  },

  // 格式化到期时间
  formatExpiryDate(dateStr) {
    if (!dateStr) return '未知'
    
    const date = new Date(dateStr)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    
    return `${year}-${month}-${day}`
  }
})