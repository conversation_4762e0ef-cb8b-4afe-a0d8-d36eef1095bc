<!--pages/profile/profile.wxml-->
<view class="container">
  <!-- 用户信息区域 -->
  <view class="user-section">
    <view wx:if="{{!hasUserInfo}}" class="login-prompt">
      <image class="avatar-placeholder" src="/images/avatar-placeholder.png" mode="aspectFit"></image>
      <text class="login-text">点击登录</text>
      <button class="login-btn btn-primary" bindtap="goToLogin">立即登录</button>
    </view>
    
    <view wx:else class="user-info">
      <image class="avatar" src="{{userInfo.avatarUrl || '/images/avatar-placeholder.png'}}" mode="aspectFit"></image>
      <view class="user-details">
        <text class="username">{{userInfo.nickName || '用户'}}</text>
        <view wx:if="{{subscription}}" class="subscription-badge {{getSubscriptionClass(subscription.type)}}">
          <text>{{getSubscriptionTypeText(subscription.type)}}</text>
        </view>
      </view>
      <text class="logout-btn" bindtap="logout">退出</text>
    </view>
  </view>

  <!-- 会员信息 -->
  <view wx:if="{{hasUserInfo && subscription}}" class="subscription-section">
    <view class="subscription-card">
      <view class="subscription-header">
        <text class="subscription-title">会员服务</text>
        <view wx:if="{{subscription.type !== 'premium'}}" class="upgrade-btn" bindtap="upgradeSubscription">
          <text>升级</text>
        </view>
      </view>
      
      <view class="subscription-content">
        <view wx:if="{{subscription.type === 'trial'}}" class="trial-info">
          <text class="trial-text">试用期剩余：{{calculateRemainingDays(subscription.trial_expiry)}}天</text>
          <progress class="trial-progress" percent="{{(3 - calculateRemainingDays(subscription.trial_expiry)) / 3 * 100}}" stroke-width="6" activeColor="#1976D2"></progress>
        </view>
        
        <view wx:elif="{{subscription.type === 'premium'}}" class="premium-info">
          <text class="premium-text">高级版会员</text>
          <text class="expire-text">到期时间：{{formatExpiryDate(subscription.expire_date)}}</text>
        </view>
        
        <view wx:else class="free-info">
          <text class="free-text">免费版用户</text>
          <text class="free-desc">升级高级版享受更多功能</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 使用统计 -->
  <view wx:if="{{hasUserInfo && stats}}" class="stats-section">
    <view class="stats-card">
      <text class="stats-title">使用统计</text>
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-number">{{stats.routeCount || 0}}</text>
          <text class="stat-label">路线规划</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{stats.checkpointCount || 0}}</text>
          <text class="stat-label">检查站查询</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{stats.savedTime || 0}}</text>
          <text class="stat-label">节省时间(分钟)</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <view 
      wx:for="{{menuItems}}" 
      wx:key="title" 
      class="menu-item"
      data-index="{{index}}"
      bindtap="onMenuTap"
    >
      <image class="menu-icon" src="{{item.icon}}" mode="aspectFit"></image>
      <view class="menu-content">
        <text class="menu-title">{{item.title}}</text>
        <text class="menu-subtitle">{{item.subtitle}}</text>
      </view>
      <image class="menu-arrow" src="/images/arrow-right.png" mode="aspectFit"></image>
    </view>
  </view>

  <!-- 其他操作 -->
  <view class="other-section">
    <view class="other-item" bindtap="contactService">
      <image class="other-icon" src="/images/service.png" mode="aspectFit"></image>
      <text class="other-text">联系客服</text>
    </view>
    
    <button class="share-btn" open-type="share">
      <image class="other-icon" src="/images/share.png" mode="aspectFit"></image>
      <text class="other-text">分享给朋友</text>
    </button>
  </view>

  <!-- 版本信息 -->
  <view class="version-info">
    <text class="version-text">进京导航助手 v1.0.0</text>
  </view>
</view>