<!--pages/favorites/favorites.wxml-->
<view class="container">
  <!-- 头部 -->
  <view class="header">
    <text class="title">我的收藏</text>
    <view class="header-actions">
      <view class="action-btn" bindtap="showSortOptions">
        <image class="action-icon" src="/images/sort.png" mode="aspectFit"></image>
      </view>
      <view class="action-btn" bindtap="showFilterOptions">
        <image class="action-icon" src="/images/filter.png" mode="aspectFit"></image>
      </view>
    </view>
  </view>

  <!-- 收藏列表 -->
  <scroll-view 
    class="favorites-list" 
    scroll-y="{{true}}"
    enable-back-to-top="{{true}}"
    refresher-enabled="{{true}}"
    refresher-triggered="{{refreshing}}"
    bindrefresherrefresh="refreshData"
  >
    <view wx:if="{{loading && favorites.length === 0}}" class="loading">
      <text>加载中...</text>
    </view>
    
    <view wx:elif="{{favorites.length === 0}}" class="empty">
      <image class="empty-icon" src="/images/empty-favorites.png" mode="aspectFit"></image>
      <text class="empty-text">暂无收藏的位置</text>
      <text class="empty-hint">在地图页面可以收藏常用位置</text>
      <button class="btn-primary" bindtap="goToMap">去地图看看</button>
    </view>
    
    <view wx:else class="favorites-content">
      <view wx:for="{{favorites}}" wx:key="id" class="favorite-item" bindtap="onFavoriteTap" data-index="{{index}}">
        <view class="favorite-info">
          <view class="favorite-header">
            <text class="favorite-name">{{item.name}}</text>
            <view class="favorite-type">{{item.type}}</view>
          </view>
          <text class="favorite-address">{{item.address}}</text>
          <view class="favorite-meta">
            <text class="favorite-time">{{formatTime(item.created_at)}}</text>
            <text class="favorite-distance" wx:if="{{item.distance}}">距离 {{item.distance}}km</text>
          </view>
        </view>
        <view class="favorite-actions">
          <view class="action-btn" bindtap="navigateToFavorite" data-index="{{index}}" catchtap="true">
            <image class="action-icon" src="/images/navigation.png" mode="aspectFit"></image>
          </view>
          <view class="action-btn" bindtap="deleteFavorite" data-index="{{index}}" catchtap="true">
            <image class="action-icon" src="/images/delete.png" mode="aspectFit"></image>
          </view>
        </view>
      </view>
    </view>
  </scroll-view>

  <!-- 排序选项弹窗 -->
  <view wx:if="{{showSortModal}}" class="modal-overlay" bindtap="hideSortOptions">
    <view class="modal-content" catchtap="true">
      <view class="modal-header">
        <text class="modal-title">排序方式</text>
        <view class="modal-close" bindtap="hideSortOptions">×</view>
      </view>
      <view class="sort-options">
        <view wx:for="{{sortOptions}}" wx:key="value" 
              class="sort-option {{sortBy === item.value ? 'active' : ''}}"
              bindtap="onSortChange" data-value="{{item.value}}">
          <text>{{item.label}}</text>
          <image wx:if="{{sortBy === item.value}}" class="check-icon" src="/images/check.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>
  </view>

  <!-- 筛选选项弹窗 -->
  <view wx:if="{{showFilterModal}}" class="modal-overlay" bindtap="hideFilterOptions">
    <view class="modal-content" catchtap="true">
      <view class="modal-header">
        <text class="modal-title">筛选条件</text>
        <view class="modal-close" bindtap="hideFilterOptions">×</view>
      </view>
      <view class="filter-options">
        <view class="filter-group">
          <text class="filter-label">位置类型</text>
          <view class="filter-items">
            <view wx:for="{{typeOptions}}" wx:key="value" 
                  class="filter-item {{filterType === item.value ? 'active' : ''}}"
                  bindtap="onTypeFilterChange" data-value="{{item.value}}">
              {{item.label}}
            </view>
          </view>
        </view>
      </view>
      <view class="filter-actions">
        <button class="btn-secondary" bindtap="resetFilter">重置</button>
        <button class="btn-primary" bindtap="applyFilter">确定</button>
      </view>
    </view>
  </view>
</view>
