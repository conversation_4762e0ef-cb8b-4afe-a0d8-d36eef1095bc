/* pages/favorites/favorites.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  background: white;
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #eee;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 50%;
}

.action-icon {
  width: 40rpx;
  height: 40rpx;
}

.favorites-list {
  height: calc(100vh - 120rpx);
}

.loading, .empty {
  text-align: center;
  padding: 100rpx 40rpx;
  color: #666;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  display: block;
  font-size: 32rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.empty-hint {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.favorites-content {
  padding: 20rpx;
}

.favorite-item {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.favorite-info {
  flex: 1;
  margin-right: 20rpx;
}

.favorite-header {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.favorite-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-right: 20rpx;
}

.favorite-type {
  background: #1976D2;
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.favorite-address {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.favorite-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.favorite-time, .favorite-distance {
  font-size: 24rpx;
  color: #999;
}

.favorite-actions {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 20rpx;
  width: 80%;
  max-height: 70vh;
  overflow: hidden;
}

.modal-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sort-options, .filter-options {
  padding: 20rpx;
  max-height: 400rpx;
  overflow-y: auto;
}

.sort-option {
  padding: 25rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 10rpx;
  margin-bottom: 10rpx;
}

.sort-option.active {
  background: #e3f2fd;
  color: #1976D2;
}

.check-icon {
  width: 40rpx;
  height: 40rpx;
}

.filter-group {
  margin-bottom: 30rpx;
}

.filter-label {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.filter-items {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.filter-item {
  padding: 20rpx 30rpx;
  background: #f5f5f5;
  border-radius: 50rpx;
  font-size: 28rpx;
  color: #666;
}

.filter-item.active {
  background: #1976D2;
  color: white;
}

.filter-actions {
  padding: 30rpx;
  border-top: 1rpx solid #eee;
  display: flex;
  gap: 20rpx;
}

.btn-primary, .btn-secondary {
  flex: 1;
  padding: 25rpx;
  border-radius: 50rpx;
  font-size: 32rpx;
  border: none;
  text-align: center;
}

.btn-primary {
  background: #1976D2;
  color: white;
}

.btn-secondary {
  background: white;
  color: #1976D2;
  border: 2rpx solid #1976D2;
}
