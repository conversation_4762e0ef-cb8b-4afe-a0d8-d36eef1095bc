// pages/favorites/favorites.js
const app = getApp()

Page({
  data: {
    favorites: [],
    loading: false,
    showAddDialog: false,
    newFavorite: {
      name: '',
      address: '',
      latitude: null,
      longitude: null
    }
  },

  onLoad() {
    this.loadFavorites()
  },

  onShow() {
    this.loadFavorites()
  },

  // 加载收藏的位置
  loadFavorites() {
    if (!app.globalData.token) {
      wx.showModal({
        title: '登录提示',
        content: '请先登录后查看收藏',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            })
          } else {
            wx.navigateBack()
          }
        }
      })
      return
    }

    this.setData({ loading: true })

    app.request({
      url: '/api/v1/users/favorites',
      method: 'GET'
    }).then((data) => {
      this.setData({
        favorites: data.favorites || [],
        loading: false
      })
    }).catch((err) => {
      console.error('加载收藏失败:', err)
      this.setData({ loading: false })
      app.showToast('加载收藏失败')
    })
  },

  // 显示添加收藏对话框
  showAddFavorite() {
    this.setData({ showAddDialog: true })
  },

  // 隐藏添加收藏对话框
  hideAddDialog() {
    this.setData({ 
      showAddDialog: false,
      newFavorite: {
        name: '',
        address: '',
        latitude: null,
        longitude: null
      }
    })
  },

  // 输入收藏名称
  onNameInput(e) {
    this.setData({
      'newFavorite.name': e.detail.value
    })
  },

  // 选择位置
  chooseLocation() {
    wx.chooseLocation({
      success: (res) => {
        this.setData({
          'newFavorite.address': res.address,
          'newFavorite.latitude': res.latitude,
          'newFavorite.longitude': res.longitude
        })
      },
      fail: () => {
        wx.showModal({
          title: '位置权限',
          content: '需要位置权限来选择地点',
          confirmText: '去设置',
          success: (res) => {
            if (res.confirm) {
              wx.openSetting()
            }
          }
        })
      }
    })
  },

  // 添加收藏
  addFavorite() {
    const { name, address, latitude, longitude } = this.data.newFavorite
    
    if (!name.trim()) {
      app.showToast('请输入收藏名称')
      return
    }
    
    if (!latitude || !longitude) {
      app.showToast('请选择位置')
      return
    }

    app.request({
      url: '/api/v1/users/favorites',
      method: 'POST',
      data: {
        name: name.trim(),
        address: address,
        latitude: latitude,
        longitude: longitude
      }
    }).then(() => {
      app.showToast('添加成功', 'success')
      this.hideAddDialog()
      this.loadFavorites()
    }).catch((err) => {
      app.showToast(err.message || '添加失败')
    })
  },

  // 收藏项点击
  onFavoriteTap(e) {
    const index = e.currentTarget.dataset.index
    const favorite = this.data.favorites[index]
    
    wx.showActionSheet({
      itemList: ['在地图中查看', '导航到此', '分享位置', '删除收藏'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.viewOnMap(favorite)
            break
          case 1:
            this.navigateToFavorite(favorite)
            break
          case 2:
            this.shareLocation(favorite)
            break
          case 3:
            this.deleteFavorite(favorite)
            break
        }
      }
    })
  },

  // 在地图中查看
  viewOnMap(favorite) {
    const favoriteData = JSON.stringify({
      latitude: favorite.latitude,
      longitude: favorite.longitude,
      name: favorite.name,
      address: favorite.address
    })
    
    wx.switchTab({
      url: `/pages/map/map?favorite=${encodeURIComponent(favoriteData)}`
    })
  },

  // 导航到收藏位置
  navigateToFavorite(favorite) {
    // 检查导航权限
    this.checkNavigationPermission().then((hasPermission) => {
      if (!hasPermission) {
        this.showNavigationPermissionDenied()
        return
      }
      
      // 获取当前位置
      wx.getLocation({
        type: 'gcj02',
        success: (res) => {
          // 跳转到地图页面并开始导航
          const routeData = JSON.stringify({
            start: {
              latitude: res.latitude,
              longitude: res.longitude,
              name: '我的位置'
            },
            end: {
              latitude: favorite.latitude,
              longitude: favorite.longitude,
              name: favorite.name
            }
          })
          
          wx.switchTab({
            url: `/pages/map/map?route=${encodeURIComponent(routeData)}`
          })
        },
        fail: () => {
          app.showToast('获取位置失败')
        }
      })
    })
  },

  // 分享位置
  shareLocation(favorite) {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
    
    // 设置分享内容
    this.shareData = {
      title: `${favorite.name} - 位置分享`,
      path: `/pages/map/map?shared=${encodeURIComponent(JSON.stringify(favorite))}`,
      imageUrl: '/images/share-location.png'
    }
    
    app.showToast('请点击右上角分享')
  },

  // 删除收藏
  deleteFavorite(favorite) {
    wx.showModal({
      title: '确认删除',
      content: `确定要删除收藏"${favorite.name}"吗？`,
      success: (res) => {
        if (res.confirm) {
          app.request({
            url: `/api/v1/users/favorites/${favorite.id}`,
            method: 'DELETE'
          }).then(() => {
            app.showToast('删除成功', 'success')
            this.loadFavorites()
          }).catch((err) => {
            app.showToast(err.message || '删除失败')
          })
        }
      }
    })
  },

  // 检查导航权限
  checkNavigationPermission() {
    return new Promise((resolve) => {
      if (!app.globalData.token) {
        resolve(false)
        return
      }

      app.request({
        url: '/api/v1/users/subscription',
        method: 'GET'
      }).then((data) => {
        const subscription = data.subscription
        if (!subscription) {
          resolve(false)
          return
        }

        const now = new Date()
        
        // 试用期用户可以使用导航功能
        if (subscription.type === 'trial') {
          const trialExpiry = new Date(subscription.trial_expiry)
          resolve(now < trialExpiry)
          return
        }
        
        // 高级版用户可以使用导航功能
        if (subscription.type === 'premium') {
          resolve(true)
          return
        }
        
        // 免费用户不能使用导航功能
        resolve(false)
      }).catch(() => {
        resolve(false)
      })
    })
  },

  // 显示导航权限不足提示
  showNavigationPermissionDenied() {
    wx.showModal({
      title: '功能受限',
      content: '导航功能仅限试用期和高级版用户使用。',
      confirmText: '升级会员',
      cancelText: '我知道了',
      success: (res) => {
        if (res.confirm) {
          wx.switchTab({
            url: '/pages/profile/profile'
          })
        }
      }
    })
  },

  // 分享给朋友
  onShareAppMessage() {
    if (this.shareData) {
      return this.shareData
    }
    
    return {
      title: '我的位置收藏 - 进京导航助手',
      path: '/pages/index/index',
      imageUrl: '/images/share.png'
    }
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '进京导航助手 - 智能位置收藏',
      imageUrl: '/images/share.png'
    }
  }
})