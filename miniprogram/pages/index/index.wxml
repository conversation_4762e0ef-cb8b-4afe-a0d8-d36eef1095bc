<!--pages/index/index.wxml-->
<view class="container">
  <!-- 用户信息区域 -->
  <view class="user-section">
    <view wx:if="{{!hasUserInfo}}" class="user-login">
      <view class="login-card card">
        <view class="login-content">
          <text class="login-title">欢迎使用进京导航助手</text>
          <text class="login-subtitle">为外地车主提供智能避让检查站的导航服务</text>
          <button wx:if="{{canIUseGetUserProfile}}" class="btn-primary" bindtap="getUserProfile">
            授权登录
          </button>
          <button wx:else class="btn-primary" bindtap="goToLogin">
            立即登录
          </button>
        </view>
      </view>
    </view>

    <view wx:else class="user-info">
      <view class="welcome-card card">
        <view class="welcome-content">
          <text class="welcome-text">欢迎回来，{{userInfo.nickName || '用户'}}</text>
          <view wx:if="{{subscription}}" class="subscription-info">
            <text class="subscription-text">{{subscription.type === 'trial' ? '试用版' : subscription.type === 'premium' ? '高级版' : '免费版'}}</text>
            <text wx:if="{{subscription.type === 'trial'}}" class="trial-info">
              试用期剩余：{{subscription.remainingDays}}天
            </text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 快捷功能区域 -->
  <view class="quick-actions">
    <view class="actions-title">快捷功能</view>
    <view class="actions-grid">
      <view class="action-item" bindtap="goToMap">
        <image class="action-icon" src="/images/navigation.png" mode="aspectFit"></image>
        <text class="action-text">智能导航</text>
      </view>
      <view class="action-item" bindtap="goToCheckpoints">
        <image class="action-icon" src="/images/checkpoint.png" mode="aspectFit"></image>
        <text class="action-text">检查站查询</text>
      </view>
      <view class="action-item" bindtap="onRefresh">
        <image class="action-icon" src="/images/refresh.png" mode="aspectFit"></image>
        <text class="action-text">刷新数据</text>
      </view>
      <view class="action-item" bindtap="goToTest">
        <image class="action-icon" src="/images/test.png" mode="aspectFit"></image>
        <text class="action-text">功能测试</text>
      </view>
    </view>
  </view>

  <!-- 附近检查站 -->
  <view wx:if="{{hasUserInfo}}" class="nearby-section">
    <view class="section-header">
      <text class="section-title">附近检查站</text>
      <text class="section-more" bindtap="goToCheckpoints">查看更多</text>
    </view>

    <view wx:if="{{loading}}" class="loading">
      <text>加载中...</text>
    </view>

    <view wx:elif="{{nearbyCheckpoints.length === 0}}" class="empty">
      <image class="empty-icon" src="/images/empty.png" mode="aspectFit"></image>
      <text>附近暂无检查站信息</text>
    </view>

    <view wx:else class="checkpoints-list">
      <view wx:for="{{nearbyCheckpoints}}" wx:key="id" class="checkpoint-item card">
        <view class="checkpoint-header">
          <text class="checkpoint-name">{{item.name}}</text>
          <text class="checkpoint-status {{getStatusClass(item.status)}}">
            {{getStatusText(item.status)}}
          </text>
        </view>
        <view class="checkpoint-info">
          <text class="checkpoint-location">{{item.location}}</text>
          <text class="checkpoint-distance">距离：{{item.distance}}km</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 功能介绍 -->
  <view class="features-section">
    <view class="section-title">功能特色</view>
    <view class="features-list">
      <view class="feature-item">
        <image class="feature-icon" src="/images/realtime.png" mode="aspectFit"></image>
        <view class="feature-content">
          <text class="feature-title">实时数据</text>
          <text class="feature-desc">每5分钟更新检查站状态</text>
        </view>
      </view>
      <view class="feature-item">
        <image class="feature-icon" src="/images/smart.png" mode="aspectFit"></image>
        <view class="feature-content">
          <text class="feature-title">智能避让</text>
          <text class="feature-desc">自动规划绕行路线</text>
        </view>
      </view>
      <view class="feature-item">
        <image class="feature-icon" src="/images/notification.png" mode="aspectFit"></image>
        <view class="feature-content">
          <text class="feature-title">实时提醒</text>
          <text class="feature-desc">检查站状态变化推送</text>
        </view>
      </view>
    </view>
  </view>
</view>