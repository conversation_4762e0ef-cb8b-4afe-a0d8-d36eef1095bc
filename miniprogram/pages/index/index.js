// pages/index/index.js
const app = getApp()

Page({
  data: {
    userInfo: null,
    hasUserInfo: false,
    canIUseGetUserProfile: false,
    nearbyCheckpoints: [],
    loading: false,
    location: null,
    subscription: null
  },

  onLoad() {
    // 检查是否支持getUserProfile
    if (wx.getUserProfile) {
      this.setData({
        canIUseGetUserProfile: true
      })
    }

    // 获取用户信息
    this.getUserInfo()

    // 获取位置信息
    this.getLocation()
  },

  onShow() {
    // 刷新数据
    if (this.data.hasUserInfo) {
      this.loadNearbyCheckpoints()
      this.loadUserSubscription()
    }
  },

  // 获取用户信息
  getUserInfo() {
    if (app.globalData.userInfo) {
      this.setData({
        userInfo: app.globalData.userInfo,
        hasUserInfo: true
      })
    }
  },

  // 获取用户位置
  getLocation() {
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        this.setData({
          location: {
            latitude: res.latitude,
            longitude: res.longitude
          }
        })

        // 如果已登录，加载附近检查站
        if (this.data.hasUserInfo) {
          this.loadNearbyCheckpoints()
        }
      },
      fail: () => {
        wx.showModal({
          title: '位置权限',
          content: '需要获取您的位置信息来提供精准的导航服务，请在设置中开启位置权限',
          confirmText: '去设置',
          success: (res) => {
            if (res.confirm) {
              wx.openSetting()
            }
          }
        })
      }
    })
  },

  // 加载附近检查站
  loadNearbyCheckpoints() {
    if (!this.data.location) return

    this.setData({ loading: true })

    const { latitude, longitude } = this.data.location

    app.request({
      url: `/api/v1/checkpoints/nearby?latitude=${latitude}&longitude=${longitude}&radius=5000&limit=5`,
      method: 'GET'
    }).then((data) => {
      this.setData({
        nearbyCheckpoints: data.checkpoints || [],
        loading: false
      })
    }).catch((err) => {
      console.error('加载附近检查站失败:', err)
      this.setData({ loading: false })
      app.showToast('加载检查站信息失败')
    })
  },

  // 加载用户订阅信息
  loadUserSubscription() {
    app.request({
      url: '/api/v1/users/subscription',
      method: 'GET'
    }).then((data) => {
      this.setData({
        subscription: data.subscription
      })
    }).catch((err) => {
      console.error('加载订阅信息失败:', err)
    })
  },

  // 获取用户授权
  getUserProfile() {
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        this.setData({
          userInfo: res.userInfo,
          hasUserInfo: true
        })

        // 跳转到登录页面
        wx.navigateTo({
          url: '/pages/login/login'
        })
      }
    })
  },

  // 跳转到地图页面
  goToMap() {
    // 强制联网验证
    app.checkNetworkConnection().then((hasNetwork) => {
      if (!hasNetwork) return

      // 检查导航权限
      app.checkUserPermission('navigation').then((result) => {
        if (result.hasPermission) {
          wx.switchTab({
            url: '/pages/map/map'
          })
        } else {
          app.showPermissionDialog(result.reason, result.subscription)
        }
      })
    })
  },

  // 跳转到检查站列表
  goToCheckpoints() {
    wx.switchTab({
      url: '/pages/checkpoints/checkpoints'
    })
  },

  // 跳转到登录页面
  goToLogin() {
    wx.navigateTo({
      url: '/pages/login/login'
    })
  },

  // 跳转到测试页面
  goToTest() {
    wx.navigateTo({
      url: '/pages/test/test'
    })
  },

  // 显示登录提示
  showLoginTip() {
    wx.showModal({
      title: '登录提示',
      content: '请先登录后使用导航功能',
      confirmText: '去登录',
      success: (res) => {
        if (res.confirm) {
          this.goToLogin()
        }
      }
    })
  },

  // 刷新数据
  onRefresh() {
    this.getLocation()
    if (this.data.hasUserInfo) {
      this.loadNearbyCheckpoints()
      this.loadUserSubscription()
    }
  },

  // 获取检查站状态文本
  getStatusText(status) {
    switch (status) {
      case 'active':
        return '检查中'
      case 'inactive':
        return '未检查'
      case 'unknown':
        return '状态未知'
      default:
        return '未知'
    }
  },

  // 获取检查站状态样式
  getStatusClass(status) {
    switch (status) {
      case 'active':
        return 'status-active'
      case 'inactive':
        return 'status-inactive'
      case 'unknown':
        return 'status-unknown'
      default:
        return 'status-unknown'
    }
  },

  // 分享给朋友
  onShareAppMessage() {
    app.reportAnalytics('share', { from: 'index' })

    const subscription = this.data.subscription
    let title = '进京导航助手 - 智能避让检查站'

    if (subscription && subscription.type === 'trial') {
      title = '我在用进京导航助手，3天免费试用很好用！'
    } else if (subscription && subscription.type === 'premium') {
      title = '推荐一个超好用的进京导航助手，再也不怕检查站了！'
    }

    return {
      title: title,
      path: '/pages/index/index?from=share',
      imageUrl: '/images/share.png'
    }
  },

  // 分享到朋友圈
  onShareTimeline() {
    app.reportAnalytics('share_timeline', { from: 'index' })

    return {
      title: '进京导航助手 - 外地车主必备，智能避让检查站',
      imageUrl: '/images/share-timeline.png'
    }
  },

  // 检查导航权限
  checkNavigationPermission() {
    return new Promise((resolve) => {
      if (!app.globalData.token) {
        resolve(false)
        return
      }

      // 使用已加载的订阅信息
      const subscription = this.data.subscription
      if (!subscription) {
        resolve(false)
        return
      }

      const now = new Date()

      // 试用期用户可以使用导航功能
      if (subscription.type === 'trial') {
        const trialExpiry = new Date(subscription.trial_expiry)
        resolve(now < trialExpiry)
        return
      }

      // 高级版用户可以使用导航功能
      if (subscription.type === 'premium') {
        resolve(true)
        return
      }

      // 免费用户不能使用导航功能
      resolve(false)
    })
  },

  // 显示导航权限不足提示
  showNavigationPermissionDenied() {
    const subscription = this.data.subscription
    let content = '导航功能仅限试用期和高级版用户使用。'

    if (subscription && subscription.type === 'trial') {
      content = `您的试用期已过期。升级高级版继续使用导航功能。`
    } else if (!subscription || subscription.type === 'free') {
      content = '免费用户只能查看检查站信息。升级高级版解锁导航功能。'
    }

    wx.showModal({
      title: '功能受限',
      content: content,
      confirmText: '升级会员',
      cancelText: '查看检查站',
      success: (res) => {
        if (res.confirm) {
          wx.switchTab({
            url: '/pages/profile/profile'
          })
        } else {
          wx.switchTab({
            url: '/pages/checkpoints/checkpoints'
          })
        }
      }
    })
  },

  // 强制联网验证
  checkNetworkAndAuth() {
    return new Promise((resolve) => {
      // 检查网络状态
      wx.getNetworkType({
        success: (res) => {
          if (res.networkType === 'none') {
            wx.showModal({
              title: '网络连接失败',
              content: '此功能需要网络连接，请检查网络设置',
              showCancel: false
            })
            resolve(false)
            return
          }

          // 检查登录状态
          if (!app.globalData.token) {
            this.showLoginTip()
            resolve(false)
            return
          }

          // 验证token有效性
          app.validateToken(app.globalData.token)
          resolve(true)
        },
        fail: () => {
          wx.showToast('网络检查失败')
          resolve(false)
        }
      })
    })
  }
})