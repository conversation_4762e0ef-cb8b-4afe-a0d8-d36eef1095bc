/* pages/index/index.wxss */
.container {
  padding: 0;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 用户信息区域 */
.user-section {
  margin-bottom: 32rpx;
}

.login-card {
  margin: 32rpx 16rpx;
  text-align: center;
}

.login-content {
  padding: 40rpx 32rpx;
}

.login-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 16rpx;
}

.login-subtitle {
  display: block;
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.welcome-card {
  margin: 32rpx 16rpx;
}

.welcome-content {
  display: flex;
  flex-direction: column;
}

.welcome-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 16rpx;
}

.subscription-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.subscription-text {
  background-color: #1976D2;
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
}

.trial-info {
  font-size: 24rpx;
  color: #ff9800;
}

/* 快捷功能区域 */
.quick-actions {
  margin: 0 16rpx 32rpx;
}

.actions-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 24rpx;
  padding: 0 16rpx;
}

.actions-grid {
  display: flex;
  justify-content: space-around;
  background-color: white;
  border-radius: 16rpx;
  padding: 40rpx 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.action-icon {
  width: 64rpx;
  height: 64rpx;
  margin-bottom: 16rpx;
}

.action-text {
  font-size: 28rpx;
  color: #333333;
}

/* 附近检查站区域 */
.nearby-section {
  margin: 0 16rpx 32rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding: 0 16rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.section-more {
  font-size: 28rpx;
  color: #1976D2;
}

.checkpoints-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.checkpoint-item {
  margin: 0;
}

.checkpoint-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.checkpoint-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  flex: 1;
}

.checkpoint-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.checkpoint-location {
  font-size: 28rpx;
  color: #666666;
  flex: 1;
}

.checkpoint-distance {
  font-size: 24rpx;
  color: #999999;
}

/* 功能介绍区域 */
.features-section {
  margin: 0 16rpx 32rpx;
}

.features-list {
  background-color: white;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.feature-item:last-child {
  margin-bottom: 0;
}

.feature-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 24rpx;
}

.feature-content {
  flex: 1;
}

.feature-title {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
}

.feature-desc {
  font-size: 26rpx;
  color: #666666;
}