// pages/permission/permission.js
const app = getApp()

Page({
  data: {
    permissionType: 'navigation', // navigation, view_checkpoints
    subscription: null,
    features: {
      free: [
        { name: '查看检查站', icon: '/images/checkpoint.png', available: true },
        { name: '基础地图', icon: '/images/map.png', available: true },
        { name: '位置搜索', icon: '/images/search.png', available: true }
      ],
      trial: [
        { name: '智能导航', icon: '/images/navigation.png', available: true },
        { name: '路线规划', icon: '/images/route.png', available: true },
        { name: '实时避让', icon: '/images/avoid.png', available: true },
        { name: '位置收藏', icon: '/images/favorite.png', available: true }
      ],
      premium: [
        { name: '高级路线优化', icon: '/images/optimize.png', available: true },
        { name: '多路线对比', icon: '/images/compare.png', available: true },
        { name: '历史记录', icon: '/images/history.png', available: true },
        { name: '优先客服', icon: '/images/vip-service.png', available: true }
      ]
    }
  },

  onLoad(options) {
    if (options.type) {
      this.setData({
        permissionType: options.type
      })
    }
    
    this.loadSubscription()
  },

  // 加载订阅信息
  loadSubscription() {
    if (!app.globalData.token) {
      return
    }

    app.request({
      url: '/api/v1/users/subscription',
      method: 'GET'
    }).then((data) => {
      this.setData({
        subscription: data.subscription
      })
    }).catch((err) => {
      console.error('加载订阅信息失败:', err)
    })
  },

  // 升级会员
  upgradeSubscription() {
    wx.showActionSheet({
      itemList: ['月度会员 ¥19.9', '季度会员 ¥49.9', '年度会员 ¥159.9'],
      success: (res) => {
        const plans = ['monthly', 'quarterly', 'yearly']
        const plan = plans[res.tapIndex]
        this.purchaseSubscription(plan)
      }
    })
  },

  // 购买订阅
  purchaseSubscription(plan) {
    app.showLoading('处理中...')
    
    app.request({
      url: '/api/v1/users/subscribe',
      method: 'POST',
      data: { plan }
    }).then((data) => {
      app.hideLoading()
      
      if (data.payment_url) {
        // 模拟支付成功
        setTimeout(() => {
          app.showToast('订阅成功', 'success')
          this.loadSubscription()
        }, 2000)
      } else {
        app.showToast('订阅成功', 'success')
        this.loadSubscription()
      }
    }).catch((err) => {
      app.hideLoading()
      app.showToast(err.message || '订阅失败')
    })
  },

  // 返回上一页
  goBack() {
    wx.navigateBack()
  },

  // 跳转到个人中心
  goToProfile() {
    wx.switchTab({
      url: '/pages/profile/profile'
    })
  },

  // 获取订阅类型文本
  getSubscriptionTypeText(type) {
    switch (type) {
      case 'trial':
        return '试用版'
      case 'premium':
        return '高级版'
      case 'free':
        return '免费版'
      default:
        return '未知'
    }
  },

  // 计算剩余天数
  calculateRemainingDays(expiryDate) {
    if (!expiryDate) return 0
    
    const now = new Date()
    const expiry = new Date(expiryDate)
    const diffTime = expiry - now
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    return Math.max(0, diffDays)
  }
})