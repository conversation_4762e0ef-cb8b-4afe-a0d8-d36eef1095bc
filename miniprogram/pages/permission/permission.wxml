<!--pages/permission/permission.wxml-->
<view class="container">
  <!-- 权限说明头部 -->
  <view class="permission-header">
    <image class="permission-icon" src="/images/permission.png" mode="aspectFit"></image>
    <text class="permission-title">功能权限说明</text>
    <text class="permission-subtitle">了解不同版本的功能权限</text>
  </view>

  <!-- 当前订阅状态 -->
  <view wx:if="{{subscription}}" class="subscription-status">
    <view class="status-card">
      <view class="status-info">
        <text class="status-type">{{getSubscriptionTypeText(subscription.type)}}</text>
        <text wx:if="{{subscription.type === 'trial'}}" class="status-expiry">
          剩余 {{calculateRemainingDays(subscription.trial_expiry)}} 天
        </text>
        <text wx:elif="{{subscription.type === 'premium'}}" class="status-expiry">
          有效期至 {{subscription.expiry_date}}
        </text>
      </view>
      <view wx:if="{{subscription.type !== 'premium'}}" class="upgrade-btn" bindtap="upgradeSubscription">
        升级会员
      </view>
    </view>
  </view>

  <!-- 功能对比 -->
  <view class="features-comparison">
    <!-- 免费版功能 -->
    <view class="feature-section">
      <view class="section-title">免费版功能</view>
      <view class="feature-list">
        <view wx:for="{{features.free}}" wx:key="name" class="feature-item">
          <image class="feature-icon" src="{{item.icon}}" mode="aspectFit"></image>
          <text class="feature-name">{{item.name}}</text>
          <image class="feature-status" src="/images/check.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>

    <!-- 试用版功能 -->
    <view class="feature-section">
      <view class="section-title">试用版功能</view>
      <view class="feature-list">
        <view wx:for="{{features.trial}}" wx:key="name" class="feature-item">
          <image class="feature-icon" src="{{item.icon}}" mode="aspectFit"></image>
          <text class="feature-name">{{item.name}}</text>
          <image class="feature-status" src="/images/check.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>

    <!-- 高级版功能 -->
    <view class="feature-section">
      <view class="section-title">高级版功能</view>
      <view class="feature-list">
        <view wx:for="{{features.premium}}" wx:key="name" class="feature-item">
          <image class="feature-icon" src="{{item.icon}}" mode="aspectFit"></image>
          <text class="feature-name">{{item.name}}</text>
          <image class="feature-status" src="/images/check.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-buttons">
    <button wx:if="{{subscription && subscription.type !== 'premium'}}" 
            class="btn-primary" 
            bindtap="upgradeSubscription">
      升级高级版
    </button>
    <button class="btn-secondary" bindtap="goToProfile">
      个人中心
    </button>
    <button class="btn-secondary" bindtap="goBack">
      返回
    </button>
  </view>
</view>
