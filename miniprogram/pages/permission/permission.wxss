/* pages/permission/permission.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.permission-header {
  text-align: center;
  padding: 40rpx 20rpx;
  background: white;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
}

.permission-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.permission-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.permission-subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
}

.subscription-status {
  margin-bottom: 20rpx;
}

.status-card {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-info {
  flex: 1;
}

.status-type {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #1976D2;
  margin-bottom: 10rpx;
}

.status-expiry {
  display: block;
  font-size: 26rpx;
  color: #666;
}

.upgrade-btn {
  background: #1976D2;
  color: white;
  padding: 20rpx 30rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
}

.features-comparison {
  margin-bottom: 40rpx;
}

.feature-section {
  background: white;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.section-title {
  background: #1976D2;
  color: white;
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
}

.feature-list {
  padding: 20rpx;
}

.feature-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.feature-item:last-child {
  border-bottom: none;
}

.feature-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
}

.feature-name {
  flex: 1;
  font-size: 30rpx;
  color: #333;
}

.feature-status {
  width: 40rpx;
  height: 40rpx;
}

.action-buttons {
  padding: 20rpx;
}

.btn-primary, .btn-secondary {
  width: 100%;
  margin-bottom: 20rpx;
  padding: 25rpx;
  border-radius: 50rpx;
  font-size: 32rpx;
  border: none;
}

.btn-primary {
  background: #1976D2;
  color: white;
}

.btn-secondary {
  background: white;
  color: #1976D2;
  border: 2rpx solid #1976D2;
}

.btn-secondary:last-child {
  margin-bottom: 0;
}
