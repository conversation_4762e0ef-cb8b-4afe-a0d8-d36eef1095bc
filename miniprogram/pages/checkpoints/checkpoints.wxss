/* pages/checkpoints/checkpoints.wxss */
.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

/* 筛选栏 */
.filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  background-color: white;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx 24rpx;
  background-color: #f5f5f5;
  border-radius: 20rpx;
  font-size: 28rpx;
  color: #666666;
}

.filter-icon {
  width: 32rpx;
  height: 32rpx;
}

.total-count {
  font-size: 26rpx;
  color: #999999;
}

/* 检查站列表 */
.checkpoint-list {
  flex: 1;
  padding: 16rpx;
}

.checkpoint-item {
  background-color: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.checkpoint-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24rpx;
}

.checkpoint-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  flex: 1;
  margin-right: 16rpx;
}

.checkpoint-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.report-btn {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.6;
}

.checkpoint-info {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.info-row {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.info-icon {
  width: 28rpx;
  height: 28rpx;
  opacity: 0.6;
}

.info-text {
  font-size: 28rpx;
  color: #666666;
  flex: 1;
}

/* 加载状态 */
.load-more, .no-more {
  text-align: center;
  padding: 40rpx;
  font-size: 28rpx;
  color: #999999;
}

/* 筛选面板 */
.filter-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 100;
  display: flex;
  align-items: flex-end;
}

.filter-panel {
  width: 100%;
  background-color: white;
  border-radius: 32rpx 32rpx 0 0;
  padding: 32rpx;
  max-height: 80vh;
  overflow-y: auto;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
  padding-bottom: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.filter-close {
  font-size: 48rpx;
  color: #999999;
  line-height: 1;
}

.filter-content {
  margin-bottom: 32rpx;
}

.filter-group {
  margin-bottom: 32rpx;
}

.filter-label {
  display: block;
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.picker-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  font-size: 30rpx;
  color: #333333;
}

.picker-arrow {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.5;
}

.filter-actions {
  display: flex;
  gap: 24rpx;
}

.filter-reset, .filter-apply {
  flex: 1;
  height: 88rpx;
  font-size: 32rpx;
}