<!--pages/checkpoints/checkpoints.wxml-->
<view class="container">
  <!-- 筛选栏 -->
  <view class="filter-bar">
    <view class="filter-btn" bindtap="showFilterPanel">
      <image class="filter-icon" src="/images/filter.png" mode="aspectFit"></image>
      <text>筛选</text>
    </view>
    <view class="total-count">
      <text>共{{total}}个检查站</text>
    </view>
  </view>

  <!-- 检查站列表 -->
  <scroll-view 
    class="checkpoint-list" 
    scroll-y="{{true}}"
    enable-back-to-top="{{true}}"
    refresher-enabled="{{true}}"
    refresher-triggered="{{refreshing}}"
    bindrefresherrefresh="refreshData"
    bindscrolltolower="loadMore"
  >
    <view wx:if="{{loading && checkpoints.length === 0}}" class="loading">
      <text>加载中...</text>
    </view>
    
    <view wx:elif="{{checkpoints.length === 0}}" class="empty">
      <image class="empty-icon" src="/images/empty.png" mode="aspectFit"></image>
      <text>暂无检查站信息</text>
    </view>
    
    <view wx:else>
      <view 
        wx:for="{{checkpoints}}" 
        wx:key="id" 
        class="checkpoint-item"
        data-index="{{index}}"
        bindtap="onCheckpointTap"
      >
        <view class="checkpoint-header">
          <text class="checkpoint-name">{{item.name}}</text>
          <view class="checkpoint-actions">
            <text class="checkpoint-status {{getStatusClass(item.status)}}">
              {{getStatusText(item.status)}}
            </text>
            <image 
              class="report-btn" 
              src="/images/report.png" 
              mode="aspectFit"
              data-index="{{index}}"
              bindtap="reportCheckpoint"
            ></image>
          </view>
        </view>
        
        <view class="checkpoint-info">
          <view class="info-row">
            <image class="info-icon" src="/images/location.png" mode="aspectFit"></image>
            <text class="info-text">{{item.location}}</text>
          </view>
          
          <view class="info-row">
            <image class="info-icon" src="/images/road.png" mode="aspectFit"></image>
            <text class="info-text">{{item.road || '未知道路'}}</text>
          </view>
          
          <view class="info-row">
            <image class="info-icon" src="/images/time.png" mode="aspectFit"></image>
            <text class="info-text">更新于 {{formatTime(item.updated_at)}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 加载更多提示 -->
    <view wx:if="{{loading && checkpoints.length > 0}}" class="load-more">
      <text>加载中...</text>
    </view>
    
    <view wx:elif="{{!hasMore && checkpoints.length > 0}}" class="no-more">
      <text>没有更多数据了</text>
    </view>
  </scroll-view>

  <!-- 筛选面板 -->
  <view wx:if="{{showFilter}}" class="filter-overlay" bindtap="hideFilterPanel">
    <view class="filter-panel" catchtap="">
      <view class="filter-header">
        <text class="filter-title">筛选条件</text>
        <text class="filter-close" bindtap="hideFilterPanel">×</text>
      </view>
      
      <view class="filter-content">
        <!-- 省份筛选 -->
        <view class="filter-group">
          <text class="filter-label">省份</text>
          <picker 
            range="{{provinces}}" 
            value="{{filters.province}}"
            data-field="province"
            bindchange="onFilterChange"
          >
            <view class="picker-item">
              <text>{{filters.province || '请选择省份'}}</text>
              <image class="picker-arrow" src="/images/arrow-right.png" mode="aspectFit"></image>
            </view>
          </picker>
        </view>
        
        <!-- 城市筛选 -->
        <view wx:if="{{cities.length > 0}}" class="filter-group">
          <text class="filter-label">城市</text>
          <picker 
            range="{{cities}}" 
            value="{{filters.city}}"
            data-field="city"
            bindchange="onFilterChange"
          >
            <view class="picker-item">
              <text>{{filters.city || '请选择城市'}}</text>
              <image class="picker-arrow" src="/images/arrow-right.png" mode="aspectFit"></image>
            </view>
          </picker>
        </view>
        
        <!-- 状态筛选 -->
        <view class="filter-group">
          <text class="filter-label">状态</text>
          <picker 
            range="{{statusOptions}}" 
            range-key="label"
            value="{{filters.status}}"
            data-field="status"
            bindchange="onFilterChange"
          >
            <view class="picker-item">
              <text>{{filters.status ? (statusOptions.find(item => item.value === filters.status)).label : '全部状态'}}</text>
              <image class="picker-arrow" src="/images/arrow-right.png" mode="aspectFit"></image>
            </view>
          </picker>
        </view>
        
        <!-- 类型筛选 -->
        <view class="filter-group">
          <text class="filter-label">类型</text>
          <picker 
            range="{{typeOptions}}" 
            range-key="label"
            value="{{filters.type}}"
            data-field="type"
            bindchange="onFilterChange"
          >
            <view class="picker-item">
              <text>{{filters.type ? (typeOptions.find(item => item.value === filters.type)).label : '全部类型'}}</text>
              <image class="picker-arrow" src="/images/arrow-right.png" mode="aspectFit"></image>
            </view>
          </picker>
        </view>
      </view>
      
      <view class="filter-actions">
        <button class="filter-reset btn-secondary" bindtap="resetFilter">重置</button>
        <button class="filter-apply btn-primary" bindtap="applyFilter">应用</button>
      </view>
    </view>
  </view>
</view>