// pages/checkpoints/checkpoints.js
const app = getApp()

Page({
  data: {
    checkpoints: [],
    loading: false,
    refreshing: false,
    hasMore: true,
    page: 1,
    pageSize: 20,
    total: 0,
    filters: {
      province: '',
      city: '',
      status: '',
      type: ''
    },
    showFilter: false,
    provinces: ['北京市', '天津市', '河北省', '山西省', '内蒙古自治区'],
    cities: [],
    statusOptions: [
      { value: '', label: '全部状态' },
      { value: 'active', label: '检查中' },
      { value: 'inactive', label: '未检查' },
      { value: 'unknown', label: '状态未知' }
    ],
    typeOptions: [
      { value: '', label: '全部类型' },
      { value: 'checkpoint', label: '检查站' },
      { value: 'inspection', label: '检查点' }
    ]
  },

  onLoad() {
    this.loadCheckpoints()
  },

  onShow() {
    // 刷新数据
    this.refreshData()
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.refreshData()
  },

  // 上拉加载更多
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMore()
    }
  },

  // 加载检查站列表
  loadCheckpoints(isRefresh = false) {
    if (this.data.loading) return
    
    this.setData({ 
      loading: true,
      refreshing: isRefresh
    })

    const { page, pageSize, filters } = this.data
    const params = {
      page: isRefresh ? 1 : page,
      pageSize: pageSize,
      ...filters
    }

    // 移除空值参数
    Object.keys(params).forEach(key => {
      if (params[key] === '') {
        delete params[key]
      }
    })

    app.request({
      url: '/api/v1/checkpoints',
      method: 'GET',
      data: params
    }).then((data) => {
      const newCheckpoints = data.checkpoints || []
      const checkpoints = isRefresh ? newCheckpoints : [...this.data.checkpoints, ...newCheckpoints]
      
      this.setData({
        checkpoints: checkpoints,
        total: data.total || 0,
        page: isRefresh ? 2 : this.data.page + 1,
        hasMore: newCheckpoints.length === pageSize,
        loading: false,
        refreshing: false
      })

      if (isRefresh) {
        wx.stopPullDownRefresh()
      }
    }).catch((err) => {
      console.error('加载检查站失败:', err)
      this.setData({ 
        loading: false,
        refreshing: false
      })
      
      if (isRefresh) {
        wx.stopPullDownRefresh()
      }
      
      app.showToast(err.message || '加载失败')
    })
  },

  // 刷新数据
  refreshData() {
    this.setData({
      page: 1,
      hasMore: true
    })
    this.loadCheckpoints(true)
  },

  // 加载更多
  loadMore() {
    this.loadCheckpoints(false)
  },

  // 显示筛选面板
  showFilterPanel() {
    this.setData({ showFilter: true })
  },

  // 隐藏筛选面板
  hideFilterPanel() {
    this.setData({ showFilter: false })
  },

  // 筛选条件改变
  onFilterChange(e) {
    const { field } = e.currentTarget.dataset
    const value = e.detail.value
    
    this.setData({
      [`filters.${field}`]: value
    })

    // 如果是省份改变，重置城市
    if (field === 'province') {
      this.setData({
        'filters.city': '',
        cities: this.getCitiesByProvince(value)
      })
    }
  },

  // 根据省份获取城市列表
  getCitiesByProvince(province) {
    const cityMap = {
      '北京市': ['东城区', '西城区', '朝阳区', '丰台区', '石景山区', '海淀区', '门头沟区', '房山区', '通州区', '顺义区', '昌平区', '大兴区', '怀柔区', '平谷区', '密云区', '延庆区'],
      '天津市': ['和平区', '河东区', '河西区', '南开区', '河北区', '红桥区', '东丽区', '西青区', '津南区', '北辰区', '武清区', '宝坻区', '滨海新区', '宁河区', '静海区', '蓟州区'],
      '河北省': ['石家庄市', '唐山市', '秦皇岛市', '邯郸市', '邢台市', '保定市', '张家口市', '承德市', '沧州市', '廊坊市', '衡水市']
    }
    
    return cityMap[province] || []
  },

  // 应用筛选
  applyFilter() {
    this.hideFilterPanel()
    this.refreshData()
  },

  // 重置筛选
  resetFilter() {
    this.setData({
      filters: {
        province: '',
        city: '',
        status: '',
        type: ''
      },
      cities: []
    })
  },

  // 检查站项点击
  onCheckpointTap(e) {
    const index = e.currentTarget.dataset.index
    const checkpoint = this.data.checkpoints[index]
    
    this.showCheckpointDetail(checkpoint)
  },

  // 显示检查站详情
  showCheckpointDetail(checkpoint) {
    const statusText = this.getStatusText(checkpoint.status)
    const content = `位置：${checkpoint.location}\n状态：${statusText}\n类型：${checkpoint.type}\n更新时间：${this.formatTime(checkpoint.updated_at)}`
    
    wx.showModal({
      title: checkpoint.name,
      content: content,
      confirmText: '在地图中查看',
      cancelText: '关闭',
      success: (res) => {
        if (res.confirm) {
          this.viewOnMap(checkpoint)
        }
      }
    })
  },

  // 在地图中查看
  viewOnMap(checkpoint) {
    // 将检查站信息传递给地图页面
    const checkpointData = JSON.stringify({
      latitude: checkpoint.latitude,
      longitude: checkpoint.longitude,
      name: checkpoint.name,
      status: checkpoint.status
    })
    
    wx.switchTab({
      url: `/pages/map/map?checkpoint=${encodeURIComponent(checkpointData)}`
    })
  },

  // 举报检查站
  reportCheckpoint(e) {
    e.stopPropagation()
    
    const index = e.currentTarget.dataset.index
    const checkpoint = this.data.checkpoints[index]
    
    wx.showActionSheet({
      itemList: ['状态有误', '位置错误', '已不存在', '其他问题'],
      success: (res) => {
        this.submitReport(checkpoint, res.tapIndex)
      }
    })
  },

  // 提交举报
  submitReport(checkpoint, typeIndex) {
    const reportTypes = ['status_error', 'location_error', 'not_exist', 'other']
    const reportType = reportTypes[typeIndex]
    
    app.request({
      url: '/api/v1/checkpoints/report',
      method: 'POST',
      data: {
        checkpoint_id: checkpoint.id,
        type: reportType,
        description: '用户举报'
      }
    }).then(() => {
      app.showToast('举报成功，感谢您的反馈', 'success')
    }).catch((err) => {
      app.showToast(err.message || '举报失败')
    })
  },

  // 获取状态文本
  getStatusText(status) {
    switch (status) {
      case 'active':
        return '检查中'
      case 'inactive':
        return '未检查'
      case 'unknown':
        return '状态未知'
      default:
        return '未知'
    }
  },

  // 获取状态样式
  getStatusClass(status) {
    switch (status) {
      case 'active':
        return 'status-active'
      case 'inactive':
        return 'status-inactive'
      case 'unknown':
        return 'status-unknown'
      default:
        return 'status-unknown'
    }
  },

  // 格式化时间
  formatTime(timeStr) {
    if (!timeStr) return '未知'
    
    const date = new Date(timeStr)
    const now = new Date()
    const diff = now - date
    
    if (diff < 60000) { // 1分钟内
      return '刚刚'
    } else if (diff < 3600000) { // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`
    } else if (diff < 86400000) { // 1天内
      return `${Math.floor(diff / 3600000)}小时前`
    } else {
      return `${date.getMonth() + 1}月${date.getDate()}日`
    }
  }
})