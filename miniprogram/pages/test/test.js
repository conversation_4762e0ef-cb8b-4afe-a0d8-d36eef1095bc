// pages/test/test.js
const testModule = require('../../test/basic-function-test.js')

Page({
  data: {
    testing: false,
    testResults: null,
    logs: []
  },

  onLoad() {
    console.log('测试页面加载')
  },

  // 开始测试
  async startTest() {
    this.setData({ 
      testing: true, 
      testResults: null,
      logs: []
    })

    try {
      // 重写console.log来捕获日志
      const originalLog = console.log
      const logs = []
      
      console.log = (...args) => {
        const message = args.join(' ')
        logs.push({
          message: message,
          timestamp: new Date().toLocaleTimeString()
        })
        this.setData({ logs: logs })
        originalLog.apply(console, args)
      }

      // 运行测试
      const results = await testModule.runBasicFunctionTests()
      
      // 恢复console.log
      console.log = originalLog
      
      this.setData({
        testing: false,
        testResults: results
      })

      // 显示测试完成提示
      wx.showToast({
        title: `测试完成: ${results.passed}/${results.total}`,
        icon: results.failed === 0 ? 'success' : 'none',
        duration: 2000
      })

    } catch (error) {
      console.error('测试执行失败:', error)
      this.setData({ testing: false })
      wx.showToast({
        title: '测试执行失败',
        icon: 'error'
      })
    }
  },

  // 清除日志
  clearLogs() {
    this.setData({ logs: [] })
  },

  // 导出测试结果
  exportResults() {
    if (!this.data.testResults) {
      wx.showToast({
        title: '没有测试结果',
        icon: 'none'
      })
      return
    }

    const results = this.data.testResults
    const reportText = `小程序基础功能测试报告
生成时间: ${new Date().toLocaleString()}
总测试数: ${results.total}
通过: ${results.passed}
失败: ${results.failed}
成功率: ${((results.passed / results.total) * 100).toFixed(2)}%

详细结果:
${results.details.map(test => 
  `${test.passed ? '✅' : '❌'} ${test.name}${test.error ? ` - ${test.error}` : ''}`
).join('\n')}
`

    // 复制到剪贴板
    wx.setClipboardData({
      data: reportText,
      success: () => {
        wx.showToast({
          title: '报告已复制到剪贴板',
          icon: 'success'
        })
      }
    })
  },

  // 查看测试详情
  viewTestDetail(e) {
    const index = e.currentTarget.dataset.index
    const test = this.data.testResults.details[index]
    
    wx.showModal({
      title: test.name,
      content: `状态: ${test.passed ? '通过' : '失败'}
时间: ${new Date(test.timestamp).toLocaleString()}
${test.error ? `错误: ${test.error}` : ''}`,
      showCancel: false
    })
  },

  // 返回首页
  goHome() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  }
})
