<!--pages/test/test.wxml-->
<view class="container">
  <!-- 测试头部 -->
  <view class="test-header">
    <text class="title">小程序基础功能测试</text>
    <text class="subtitle">测试小程序核心功能是否正常工作</text>
  </view>

  <!-- 测试控制区域 -->
  <view class="test-controls">
    <button 
      class="btn-primary" 
      bindtap="startTest" 
      disabled="{{testing}}"
      loading="{{testing}}"
    >
      {{testing ? '测试中...' : '开始测试'}}
    </button>
    
    <view class="control-buttons">
      <button class="btn-secondary" bindtap="clearLogs">清除日志</button>
      <button 
        class="btn-secondary" 
        bindtap="exportResults"
        disabled="{{!testResults}}"
      >
        导出结果
      </button>
    </view>
  </view>

  <!-- 测试结果概览 -->
  <view wx:if="{{testResults}}" class="test-summary">
    <view class="summary-card">
      <view class="summary-item">
        <text class="summary-label">总测试数</text>
        <text class="summary-value">{{testResults.total}}</text>
      </view>
      <view class="summary-item success">
        <text class="summary-label">通过</text>
        <text class="summary-value">{{testResults.passed}}</text>
      </view>
      <view class="summary-item {{testResults.failed > 0 ? 'failed' : 'success'}}">
        <text class="summary-label">失败</text>
        <text class="summary-value">{{testResults.failed}}</text>
      </view>
      <view class="summary-item">
        <text class="summary-label">成功率</text>
        <text class="summary-value">{{((testResults.passed / testResults.total) * 100).toFixed(2)}}%</text>
      </view>
    </view>
  </view>

  <!-- 测试详细结果 -->
  <view wx:if="{{testResults && testResults.details.length > 0}}" class="test-details">
    <view class="section-title">测试详情</view>
    <scroll-view class="details-list" scroll-y="true">
      <view 
        wx:for="{{testResults.details}}" 
        wx:key="name" 
        class="detail-item {{item.passed ? 'passed' : 'failed'}}"
        bindtap="viewTestDetail"
        data-index="{{index}}"
      >
        <view class="detail-header">
          <text class="detail-icon">{{item.passed ? '✅' : '❌'}}</text>
          <text class="detail-name">{{item.name}}</text>
        </view>
        <text class="detail-time">{{item.timestamp}}</text>
        <text wx:if="{{item.error}}" class="detail-error">{{item.error}}</text>
      </view>
    </scroll-view>
  </view>

  <!-- 实时日志 -->
  <view class="test-logs">
    <view class="section-title">
      <text>实时日志</text>
      <text class="log-count">({{logs.length}})</text>
    </view>
    <scroll-view class="logs-container" scroll-y="true" scroll-top="{{9999999}}">
      <view wx:if="{{logs.length === 0}}" class="empty-logs">
        <text>暂无日志</text>
      </view>
      <view wx:for="{{logs}}" wx:key="timestamp" class="log-item">
        <text class="log-time">{{item.timestamp}}</text>
        <text class="log-message">{{item.message}}</text>
      </view>
    </scroll-view>
  </view>

  <!-- 底部操作 -->
  <view class="bottom-actions">
    <button class="btn-secondary" bindtap="goHome">返回首页</button>
  </view>
</view>
