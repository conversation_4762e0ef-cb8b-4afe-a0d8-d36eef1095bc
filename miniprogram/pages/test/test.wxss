/* pages/test/test.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.test-header {
  text-align: center;
  padding: 40rpx 20rpx;
  background: white;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
}

.title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
}

.test-controls {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.control-buttons {
  display: flex;
  gap: 20rpx;
  margin-top: 20rpx;
}

.btn-primary, .btn-secondary {
  width: 100%;
  padding: 25rpx;
  border-radius: 50rpx;
  font-size: 32rpx;
  border: none;
}

.btn-primary {
  background: #1976D2;
  color: white;
}

.btn-secondary {
  background: white;
  color: #1976D2;
  border: 2rpx solid #1976D2;
  flex: 1;
}

.btn-primary[disabled] {
  background: #ccc;
  color: #999;
}

.btn-secondary[disabled] {
  background: #f5f5f5;
  color: #ccc;
  border-color: #ccc;
}

.test-summary {
  margin-bottom: 20rpx;
}

.summary-card {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
}

.summary-item {
  text-align: center;
  flex: 1;
}

.summary-label {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.summary-value {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.summary-item.success .summary-value {
  color: #4CAF50;
}

.summary-item.failed .summary-value {
  color: #F44336;
}

.test-details {
  background: white;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.section-title {
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  border-bottom: 1rpx solid #eee;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.log-count {
  font-size: 24rpx;
  color: #666;
  font-weight: normal;
}

.details-list {
  max-height: 400rpx;
}

.detail-item {
  padding: 25rpx 30rpx;
  border-bottom: 1rpx solid #eee;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-item.passed {
  background: #f8fff8;
}

.detail-item.failed {
  background: #fff8f8;
}

.detail-header {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.detail-icon {
  margin-right: 15rpx;
  font-size: 32rpx;
}

.detail-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.detail-time {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 5rpx;
}

.detail-error {
  display: block;
  font-size: 26rpx;
  color: #F44336;
  background: #ffebee;
  padding: 10rpx;
  border-radius: 8rpx;
  margin-top: 10rpx;
}

.test-logs {
  background: white;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.logs-container {
  height: 300rpx;
  padding: 0 30rpx;
}

.empty-logs {
  text-align: center;
  padding: 60rpx 20rpx;
  color: #999;
}

.log-item {
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  align-items: flex-start;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  font-size: 22rpx;
  color: #999;
  margin-right: 20rpx;
  min-width: 120rpx;
}

.log-message {
  font-size: 26rpx;
  color: #333;
  flex: 1;
  word-break: break-all;
}

.bottom-actions {
  padding: 20rpx 0;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6rpx;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3rpx;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3rpx;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
