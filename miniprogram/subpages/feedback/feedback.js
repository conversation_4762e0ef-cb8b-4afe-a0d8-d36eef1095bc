// subpages/feedback/feedback.js
const app = getApp()

Page({
  data: {
    feedbackType: 'bug', // bug, suggestion, other
    content: '',
    contact: '',
    images: [],
    submitting: false,
    typeOptions: [
      { value: 'bug', label: '问题反馈', icon: '/images/bug.png' },
      { value: 'suggestion', label: '功能建议', icon: '/images/suggestion.png' },
      { value: 'other', label: '其他问题', icon: '/images/other.png' }
    ]
  },

  onLoad() {
    this.reportPageView()
  },

  // 页面访问统计
  reportPageView() {
    app.reportAnalytics('page_view', { page: 'feedback' })
  },

  // 选择反馈类型
  onTypeChange(e) {
    const index = e.detail.value
    const type = this.data.typeOptions[index].value
    this.setData({
      feedbackType: type
    })
  },

  // 输入反馈内容
  onContentInput(e) {
    this.setData({
      content: e.detail.value
    })
  },

  // 输入联系方式
  onContactInput(e) {
    this.setData({
      contact: e.detail.value
    })
  },

  // 选择图片
  chooseImage() {
    const maxImages = 3
    const currentCount = this.data.images.length
    
    if (currentCount >= maxImages) {
      app.showToast(`最多只能上传${maxImages}张图片`)
      return
    }

    wx.chooseImage({
      count: maxImages - currentCount,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const images = [...this.data.images, ...res.tempFilePaths]
        this.setData({ images })
      }
    })
  },

  // 预览图片
  previewImage(e) {
    const index = e.currentTarget.dataset.index
    const images = this.data.images
    
    wx.previewImage({
      current: images[index],
      urls: images
    })
  },

  // 删除图片
  deleteImage(e) {
    const index = e.currentTarget.dataset.index
    const images = this.data.images
    images.splice(index, 1)
    this.setData({ images })
  },

  // 提交反馈
  submitFeedback() {
    const { feedbackType, content, contact, images } = this.data
    
    // 验证输入
    if (!content.trim()) {
      app.showToast('请输入反馈内容')
      return
    }
    
    if (content.trim().length < 10) {
      app.showToast('反馈内容至少10个字符')
      return
    }

    this.setData({ submitting: true })
    app.showLoading('提交中...')

    // 先上传图片（如果有）
    this.uploadImages().then((imageUrls) => {
      // 提交反馈
      return app.request({
        url: '/api/v1/feedback',
        method: 'POST',
        data: {
          type: feedbackType,
          content: content.trim(),
          contact: contact.trim(),
          images: imageUrls,
          system_info: app.globalData.systemInfo,
          app_version: '1.0.0'
        }
      })
    }).then(() => {
      app.hideLoading()
      this.setData({ submitting: false })
      
      // 统计反馈提交
      app.reportAnalytics('feedback_submit', { type: feedbackType })
      
      wx.showModal({
        title: '提交成功',
        content: '感谢您的反馈，我们会认真处理您的建议！',
        showCancel: false,
        success: () => {
          // 清空表单
          this.resetForm()
        }
      })
    }).catch((err) => {
      app.hideLoading()
      this.setData({ submitting: false })
      app.showToast(err.message || '提交失败，请重试')
    })
  },

  // 上传图片
  uploadImages() {
    const { images } = this.data
    
    if (images.length === 0) {
      return Promise.resolve([])
    }

    const uploadPromises = images.map((imagePath) => {
      return new Promise((resolve, reject) => {
        wx.uploadFile({
          url: `${app.globalData.baseUrl}/api/v1/upload/image`,
          filePath: imagePath,
          name: 'file',
          header: {
            'Authorization': `Bearer ${app.globalData.token}`
          },
          success: (res) => {
            try {
              const data = JSON.parse(res.data)
              if (data.url) {
                resolve(data.url)
              } else {
                reject(new Error('上传失败'))
              }
            } catch (e) {
              reject(e)
            }
          },
          fail: reject
        })
      })
    })

    return Promise.all(uploadPromises).catch((err) => {
      console.error('图片上传失败:', err)
      return [] // 图片上传失败不影响反馈提交
    })
  },

  // 重置表单
  resetForm() {
    this.setData({
      feedbackType: 'bug',
      content: '',
      contact: '',
      images: []
    })
  },

  // 获取反馈类型文本
  getTypeText(type) {
    const option = this.data.typeOptions.find(opt => opt.value === type)
    return option ? option.label : '其他问题'
  }
})