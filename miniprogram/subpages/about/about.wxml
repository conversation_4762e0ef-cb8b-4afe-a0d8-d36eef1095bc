<!--subpages/about/about.wxml-->
<view class="container">
  <!-- 应用信息 -->
  <view class="app-info">
    <image class="app-logo" src="/images/logo.png" mode="aspectFit"></image>
    <text class="app-name">{{appInfo.name}}</text>
    <text class="app-version">版本 {{appInfo.version}}</text>
    <text class="app-description">{{appInfo.description}}</text>
  </view>

  <!-- 功能特色 -->
  <view class="features-section">
    <text class="section-title">功能特色</text>
    <view class="features-list">
      <view wx:for="{{appInfo.features}}" wx:key="index" class="feature-item">
        <image class="feature-icon" src="/images/check.png" mode="aspectFit"></image>
        <text class="feature-text">{{item}}</text>
      </view>
    </view>
  </view>

  <!-- 联系我们 -->
  <view class="contact-section">
    <text class="section-title">联系我们</text>
    <view class="contact-list">
      <view class="contact-item" bindtap="contactService">
        <image class="contact-icon" src="/images/service.png" mode="aspectFit"></image>
        <view class="contact-content">
          <text class="contact-title">客服支持</text>
          <text class="contact-subtitle">{{appInfo.contact.phone}}</text>
        </view>
        <image class="contact-arrow" src="/images/arrow-right.png" mode="aspectFit"></image>
      </view>
      
      <view class="contact-item" bindtap="contactService">
        <image class="contact-icon" src="/images/email.png" mode="aspectFit"></image>
        <view class="contact-content">
          <text class="contact-title">邮件反馈</text>
          <text class="contact-subtitle">{{appInfo.contact.email}}</text>
        </view>
        <image class="contact-arrow" src="/images/arrow-right.png" mode="aspectFit"></image>
      </view>
    </view>
  </view>

  <!-- 系统信息 -->
  <view wx:if="{{systemInfo}}" class="system-section">
    <text class="section-title">系统信息</text>
    <view class="system-list">
      <view class="system-item">
        <text class="system-label">设备型号</text>
        <text class="system-value">{{systemInfo.model}}</text>
      </view>
      <view class="system-item">
        <text class="system-label">系统版本</text>
        <text class="system-value">{{systemInfo.system}}</text>
      </view>
      <view class="system-item">
        <text class="system-label">微信版本</text>
        <text class="system-value">{{systemInfo.version}}</text>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="actions-section">
    <button class="action-btn btn-secondary" bindtap="checkUpdate">
      检查更新
    </button>
    <button class="action-btn btn-primary" open-type="share">
      分享应用
    </button>
  </view>

  <!-- 法律信息 -->
  <view class="legal-section">
    <text class="section-title">法律信息</text>
    <view class="legal-list">
      <text class="legal-link" bindtap="viewPrivacyPolicy">{{appInfo.legal.privacy}}</text>
      <text class="legal-link" bindtap="viewTermsOfService">{{appInfo.legal.terms}}</text>
    </view>
  </view>

  <!-- 版权信息 -->
  <view class="copyright">
    <text class="copyright-text">© 2024 进京导航助手 版权所有</text>
  </view>
</view>