/* subpages/about/about.wxss */
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 40rpx;
}

/* 应用信息 */
.app-info {
  background-color: white;
  padding: 60rpx 40rpx;
  text-align: center;
  margin-bottom: 32rpx;
}

.app-logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 24rpx;
}

.app-name {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 16rpx;
}

.app-version {
  display: block;
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 32rpx;
}

.app-description {
  display: block;
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
  text-align: left;
}

/* 功能特色 */
.features-section {
  background-color: white;
  margin: 0 16rpx 32rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 24rpx;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.feature-item {
  display: flex;
  align-items: center;
}

.feature-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
}

.feature-text {
  font-size: 28rpx;
  color: #333333;
}

/* 联系我们 */
.contact-section {
  background-color: white;
  margin: 0 16rpx 32rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.contact-list {
  display: flex;
  flex-direction: column;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.contact-item:last-child {
  border-bottom: none;
}

.contact-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 24rpx;
}

.contact-content {
  flex: 1;
}

.contact-title {
  display: block;
  font-size: 30rpx;
  color: #333333;
  margin-bottom: 8rpx;
}

.contact-subtitle {
  font-size: 26rpx;
  color: #666666;
}

.contact-arrow {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.5;
}

/* 系统信息 */
.system-section {
  background-color: white;
  margin: 0 16rpx 32rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.system-list {
  display: flex;
  flex-direction: column;
}

.system-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.system-item:last-child {
  border-bottom: none;
}

.system-label {
  font-size: 28rpx;
  color: #333333;
}

.system-value {
  font-size: 28rpx;
  color: #666666;
}

/* 操作按钮 */
.actions-section {
  margin: 0 16rpx 32rpx;
  display: flex;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  font-size: 30rpx;
}

/* 法律信息 */
.legal-section {
  background-color: white;
  margin: 0 16rpx 32rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.legal-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.legal-link {
  font-size: 28rpx;
  color: #1976D2;
  text-decoration: underline;
}

/* 版权信息 */
.copyright {
  text-align: center;
  padding: 32rpx;
}

.copyright-text {
  font-size: 24rpx;
  color: #999999;
}