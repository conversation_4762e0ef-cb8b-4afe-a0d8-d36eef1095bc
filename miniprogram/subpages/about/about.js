// subpages/about/about.js
const app = getApp()

Page({
  data: {
    appInfo: {
      name: '进京导航助手',
      version: '1.0.0',
      description: '专为外地车主设计的智能导航平台，通过实时获取进京检查站信息，提供避开限行区域的最优路线规划。',
      features: [
        '实时检查站数据更新',
        '智能路线规划避让',
        '多端同步使用',
        '位置收藏分享',
        '会员服务体系'
      ],
      contact: {
        email: '<EMAIL>',
        phone: '************',
        website: 'https://www.beijingnavigation.com'
      },
      legal: {
        privacy: '《隐私政策》',
        terms: '《用户协议》',
        license: '《软件许可协议》'
      }
    },
    systemInfo: null
  },

  onLoad() {
    this.getSystemInfo()
    this.reportPageView()
  },

  // 获取系统信息
  getSystemInfo() {
    this.setData({
      systemInfo: app.globalData.systemInfo
    })
  },

  // 页面访问统计
  reportPageView() {
    app.reportAnalytics('page_view', { page: 'about' })
  },

  // 联系客服
  contactService() {
    wx.showActionSheet({
      itemList: ['拨打客服电话', '发送邮件', '访问官网'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            wx.makePhoneCall({
              phoneNumber: this.data.appInfo.contact.phone
            })
            break
          case 1:
            wx.setClipboardData({
              data: this.data.appInfo.contact.email,
              success: () => {
                app.showToast('邮箱地址已复制')
              }
            })
            break
          case 2:
            wx.setClipboardData({
              data: this.data.appInfo.contact.website,
              success: () => {
                app.showToast('网址已复制')
              }
            })
            break
        }
      }
    })
  },

  // 查看隐私政策
  viewPrivacyPolicy() {
    wx.showModal({
      title: '隐私政策',
      content: '我们重视您的隐私保护。详细的隐私政策请访问我们的官网查看。',
      confirmText: '访问官网',
      success: (res) => {
        if (res.confirm) {
          wx.setClipboardData({
            data: this.data.appInfo.contact.website + '/privacy',
            success: () => {
              app.showToast('链接已复制')
            }
          })
        }
      }
    })
  },

  // 查看用户协议
  viewTermsOfService() {
    wx.showModal({
      title: '用户协议',
      content: '使用本应用即表示您同意我们的用户协议。详细条款请访问我们的官网查看。',
      confirmText: '访问官网',
      success: (res) => {
        if (res.confirm) {
          wx.setClipboardData({
            data: this.data.appInfo.contact.website + '/terms',
            success: () => {
              app.showToast('链接已复制')
            }
          })
        }
      }
    })
  },

  // 检查更新
  checkUpdate() {
    app.showLoading('检查中...')
    
    if (wx.getUpdateManager) {
      const updateManager = wx.getUpdateManager()
      
      updateManager.onCheckForUpdate((res) => {
        app.hideLoading()
        
        if (res.hasUpdate) {
          wx.showModal({
            title: '发现新版本',
            content: '发现新版本，是否立即更新？',
            success: (modalRes) => {
              if (modalRes.confirm) {
                app.showLoading('下载中...')
                
                updateManager.onUpdateReady(() => {
                  app.hideLoading()
                  wx.showModal({
                    title: '更新完成',
                    content: '新版本已下载完成，是否立即重启应用？',
                    success: (restartRes) => {
                      if (restartRes.confirm) {
                        updateManager.applyUpdate()
                      }
                    }
                  })
                })
                
                updateManager.onUpdateFailed(() => {
                  app.hideLoading()
                  app.showToast('更新失败')
                })
              }
            }
          })
        } else {
          app.showToast('已是最新版本')
        }
      })
    } else {
      app.hideLoading()
      app.showToast('当前微信版本过低，无法检查更新')
    }
  },

  // 分享应用
  onShareAppMessage() {
    app.reportAnalytics('share', { from: 'about' })
    
    return {
      title: '进京导航助手 - 智能避让检查站',
      path: '/pages/index/index',
      imageUrl: '/images/share.png'
    }
  },

  // 分享到朋友圈
  onShareTimeline() {
    app.reportAnalytics('share_timeline', { from: 'about' })
    
    return {
      title: '进京导航助手 - 外地车主的智能导航伴侣',
      imageUrl: '/images/share.png'
    }
  }
})