name: CI/CD

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: password
          POSTGRES_DB: ddd_example_test
        ports:
          - 5432:5432
      redis:
        image: redis:6
        ports:
          - 6379:6379
          
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up Go
      uses: actions/setup-go@v2
      with:
        go-version: '1.21'
        
    - name: Install dependencies
      run: go mod download
      
    - name: Run tests
      run: go test -v ./...
      env:
        DATABASE_URL: postgres://postgres:password@localhost:5432/ddd_example_test
        REDIS_URL: redis://localhost:6379
        
    - name: Build
      run: go build -v ./...
      
  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Build and push Docker image
      uses: docker/build-push-action@v2
      with:
        push: true
        tags: your-registry/your-image:latest
        
    - name: Deploy to production
      run: |
        # Add your deployment commands here
        echo "Deploying to production..."
