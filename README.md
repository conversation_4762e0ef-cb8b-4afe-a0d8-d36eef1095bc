# 驾驶导航避让系统

外地车进京导航避让系统，基于领域驱动设计（DDD）的全栈应用程序，支持Web管理后台和微信小程序。

## ✨ 项目特点

- 🚗 **智能避让**: 实时检查站数据，智能路线规划
- 📱 **多端支持**: Web管理后台 + 微信小程序
- 🏗️ **领域驱动设计**: 清晰的DDD架构，分层设计
- 🌐 **全栈集成**: Go后端 + React前端 + 微信小程序
- ⚡ **实时更新**: WebSocket实时推送检查站状态变化
- 🚀 **云原生**: 支持k3s/Kubernetes部署，容器化架构

## 🚀 快速开始

### 开发环境
```bash
# 启动完整开发环境（推荐）
./deploy/development/start.sh

# 仅启动后端调试
./deploy/development/debug.sh
```

### 本地k3s部署
```bash
# 安装k3s
./deploy/local-k3s/install-k3s.sh

# 构建并部署
./deploy/local-k3s/build.sh
./deploy/local-k3s/deploy.sh
```

### 远程部署
```bash
# 简单部署（推荐）
./deploy/remote/simple-deploy.sh -H ************* --all

# Ansible部署（功能更强大）
./deploy/remote/deploy.sh --all
```

## 🌐 访问地址

| 服务 | 地址 | 说明 |
|------|------|------|
| 后端API | http://localhost:8080 | Go后端服务 |
| WebSocket | ws://localhost:8081/ws | 实时通信 |
| 前端管理后台 | http://localhost:3000 | React Web界面 |
| 小程序 | 微信开发者工具 | 导入 `./miniprogram` 目录 |

## 📁 项目结构

```
.
├── cmd/                    # 应用程序入口
├── configs/                # 统一配置文件
├── frontend/               # React Web管理后台
├── miniprogram/            # 微信小程序
├── internal/               # 内部包 (DDD架构)
│   ├── application/        # 应用层
│   ├── domain/            # 领域层
│   ├── infrastructure/    # 基础设施层
│   └── interfaces/        # 接口层
├── deploy/                 # 部署配置（重构后）
│   ├── development/       # 开发环境部署
│   ├── local-k3s/         # 本地k3s部署
│   └── remote/            # 远程部署
├── docs/                  # 项目文档
└── test/                  # 测试文件
```

## 🔧 技术栈

### 后端
- **语言**: Go 1.24+
- **框架**: Gin (HTTP) + GORM (ORM)
- **数据库**: PostgreSQL 13+
- **缓存**: Redis
- **架构**: 领域驱动设计 (DDD)

### 前端
- **Web管理后台**: React + TypeScript + Vite
- **小程序**: 微信小程序原生开发
- **UI组件**: Ant Design / 微信小程序组件

### 部署
- **容器化**: Docker
- **编排**: Kubernetes / k3s
- **监控**: Prometheus + Grafana

## 📚 文档

- [快速开始指南](docs/development/QUICK_START.md) - 本地开发环境搭建
- [远程部署指南](docs/deployment/REMOTE_DEPLOYMENT.md) - 生产环境部署
- [配置指南](docs/configuration/CONFIG_GUIDE.md) - 完整配置说明
- [部署重构总结](DEPLOY_REFACTOR_SUMMARY.md) - 最新部署结构变更

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

MIT