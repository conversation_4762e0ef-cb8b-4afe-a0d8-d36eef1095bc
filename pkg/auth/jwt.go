package auth

import (
	"errors"
	"github.com/golang-jwt/jwt"
	"time"
)

type JWTManager struct {
	secretKey string
}

func NewJWTManager(secretKey string) *JWTManager {
	return &JWTManager{secretKey: secretKey}
}

type Claims struct {
	UserID    uint   `json:"user_id"`
	Email     string `json:"email"`
	Role      string `json:"role"`
	DeviceID  string `json:"device_id"`
	Platform  string `json:"platform"` // web, mobile, car, miniprogram
	SessionID string `json:"session_id"`
	jwt.StandardClaims
}

func (m *JWTManager) GenerateToken(userID uint, email, role string) (string, error) {
	return m.GenerateTokenWithDevice(userID, email, role, "", "web", "")
}

func (m *JWTManager) GenerateTokenWithDevice(userID uint, email, role, deviceID, platform, sessionID string) (string, error) {
	claims := &Claims{
		UserID:    userID,
		Email:     email,
		Role:      role,
		DeviceID:  deviceID,
		Platform:  platform,
		SessionID: sessionID,
		StandardClaims: jwt.StandardClaims{
			ExpiresAt: time.Now().Add(8 * time.Hour).Unix(),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(m.secretKey))
}

func (m *JWTManager) ValidateToken(tokenStr string) (*Claims, error) {
	token, err := jwt.ParseWithClaims(
		tokenStr,
		&Claims{},
		func(token *jwt.Token) (interface{}, error) {
			return []byte(m.secretKey), nil
		},
	)

	if err != nil {
		return nil, err
	}

	claims, ok := token.Claims.(*Claims)
	if !ok {
		return nil, errors.New("invalid token claims")
	}

	return claims, nil
}
