package main

import (
	"fmt"
	"io"
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"time"
)

// CameraData 摄像头数据结构
type CameraData struct {
	Name     string
	Lat      float64
	Lng      float64
	Status   string
	Time     string
	District string
	Road     string
}

func main() {
	fmt.Println("=== 简化版进京365摄像头爬虫测试 ===")

	// 1. 获取网页内容
	fmt.Println("\n1. 获取网页内容...")
	client := &http.Client{Timeout: 30 * time.Second}

	req, err := http.NewRequest("GET", "https://jinjing365.com/index.asp", nil)
	if err != nil {
		fmt.Printf("创建请求失败: %v\n", err)
		return
	}

	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")

	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("请求失败: %v\n", err)
		return
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("读取响应失败: %v\n", err)
		return
	}

	htmlContent := string(body)
	fmt.Printf("✓ 获取HTML内容成功，大小: %d 字节\n", len(htmlContent))

	// 2. 查找LabelsData
	fmt.Println("\n2. 查找LabelsData...")

	// 使用多种正则表达式尝试匹配，添加多行模式
	patterns := []string{
		`(?s)var\s+LabelsData\s*=\s*\[(.*?)\];`,
		`(?s)LabelsData\s*=\s*\[(.*?)\];`,
		`(?s)var LabelsData = \[(.*?)\];`,
	}

	var labelsDataStr string
	var found bool

	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern)
		if matches := re.FindStringSubmatch(htmlContent); len(matches) > 1 {
			labelsDataStr = matches[1]
			found = true
			fmt.Printf("✓ 使用模式 '%s' 找到LabelsData\n", pattern)
			break
		}
	}

	if !found {
		fmt.Println("❌ 未找到LabelsData")
		// 尝试查找是否存在LabelsData关键词
		if strings.Contains(htmlContent, "LabelsData") {
			fmt.Println("HTML中包含LabelsData关键词，但正则匹配失败")
			// 显示LabelsData周围的内容
			index := strings.Index(htmlContent, "LabelsData")
			start := index - 100
			if start < 0 {
				start = 0
			}
			end := index + 500
			if end > len(htmlContent) {
				end = len(htmlContent)
			}
			fmt.Printf("LabelsData周围的内容:\n%s\n", htmlContent[start:end])
		}
		return
	}

	fmt.Printf("LabelsData内容长度: %d\n", len(labelsDataStr))

	// 3. 解析摄像头数据
	fmt.Println("\n3. 解析摄像头数据...")

	cameras := parseCameras(labelsDataStr)
	fmt.Printf("✓ 解析出 %d 个摄像头\n", len(cameras))

	// 4. 显示前10个摄像头
	fmt.Println("\n4. 摄像头数据样例:")
	count := 10
	if len(cameras) < count {
		count = len(cameras)
	}

	for i := 0; i < count; i++ {
		camera := cameras[i]
		fmt.Printf("摄像头 %d:\n", i+1)
		fmt.Printf("  名称: %s\n", camera.Name)
		fmt.Printf("  位置: (%.6f, %.6f)\n", camera.Lat, camera.Lng)
		fmt.Printf("  区域: %s\n", camera.District)
		fmt.Printf("  道路: %s\n", camera.Road)
		fmt.Printf("  状态: %s\n", camera.Status)
		fmt.Printf("  更新时间: %s\n", camera.Time)
		fmt.Println()
	}

	// 5. 统计信息
	fmt.Println("5. 统计信息:")

	districtCount := make(map[string]int)
	activeCount := 0

	for _, camera := range cameras {
		if camera.District != "" {
			districtCount[camera.District]++
		}
		if camera.Status == "active" {
			activeCount++
		}
	}

	fmt.Printf("  总摄像头数: %d\n", len(cameras))
	fmt.Printf("  活跃摄像头: %d\n", activeCount)
	fmt.Printf("  活跃率: %.1f%%\n", float64(activeCount)/float64(len(cameras))*100)

	fmt.Println("\n  按区域分布:")
	for district, count := range districtCount {
		fmt.Printf("    %s: %d个\n", district, count)
	}

	fmt.Println("\n=== 测试完成 ===")
}

// parseCameras 解析摄像头数据
func parseCameras(labelsDataStr string) []CameraData {
	var cameras []CameraData

	// 使用正则表达式匹配每个摄像头对象
	objectRe := regexp.MustCompile(`\{[^}]*\}`)
	objects := objectRe.FindAllString(labelsDataStr, -1)

	for _, obj := range objects {
		camera := parseCameraObject(obj)
		if camera.Name != "" && camera.Lat != 0 && camera.Lng != 0 {
			cameras = append(cameras, camera)
		}
	}

	return cameras
}

// parseCameraObject 解析单个摄像头对象
func parseCameraObject(objStr string) CameraData {
	var camera CameraData

	// 提取name
	nameRe := regexp.MustCompile(`name:\s*'([^']+)'`)
	if matches := nameRe.FindStringSubmatch(objStr); len(matches) > 1 {
		camera.Name = matches[1]
	}

	// 提取position
	positionRe := regexp.MustCompile(`position:\s*\[([^\]]+)\]`)
	if matches := positionRe.FindStringSubmatch(objStr); len(matches) > 1 {
		coords := strings.Split(matches[1], ",")
		if len(coords) >= 2 {
			if lng, err := strconv.ParseFloat(strings.TrimSpace(coords[0]), 64); err == nil {
				if lat, err := strconv.ParseFloat(strings.TrimSpace(coords[1]), 64); err == nil {
					camera.Lng = lng
					camera.Lat = lat
				}
			}
		}
	}

	// 提取time
	timeRe := regexp.MustCompile(`time:\s*'([^']+)'`)
	if matches := timeRe.FindStringSubmatch(objStr); len(matches) > 1 {
		camera.Time = matches[1]
	}

	// 解析位置信息
	camera.District = parseDistrict(camera.Name)
	camera.Road = parseRoad(camera.Name)

	// 确定状态
	if camera.Time != "" {
		if updateTime, err := time.Parse("2006-01-02", camera.Time); err == nil {
			if time.Since(updateTime) <= 7*24*time.Hour {
				camera.Status = "active"
			} else {
				camera.Status = "inactive"
			}
		}
	} else {
		camera.Status = "unknown"
	}

	return camera
}

// parseDistrict 从名称中解析区域
func parseDistrict(name string) string {
	districts := []string{"朝阳区", "海淀区", "丰台区", "西城区", "东城区", "石景山区",
		"门头沟区", "房山区", "通州区", "顺义区", "昌平区", "大兴区", "怀柔区", "平谷区", "密云区", "延庆区"}

	for _, district := range districts {
		if strings.Contains(name, strings.TrimSuffix(district, "区")) {
			return district
		}
	}

	// 特殊处理
	if strings.Contains(name, "开发区") {
		return "大兴区"
	}

	return ""
}

// parseRoad 从名称中解析道路
func parseRoad(name string) string {
	roadPatterns := []string{
		`([^区\s]+路)`,
		`([^区\s]+街)`,
		`([^区\s]+大街)`,
		`([^区\s]+桥)`,
	}

	for _, pattern := range roadPatterns {
		re := regexp.MustCompile(pattern)
		if matches := re.FindStringSubmatch(name); len(matches) > 1 {
			return matches[1]
		}
	}

	return ""
}
