# 实施计划

## 阶段零：项目重构和清理

- [x] 0. 现有代码清理和重构
  - [x] 0.1 移除无关业务模块
    - 删除图书管理相关的所有代码（entities/book.go, book_service.go, book_handler.go等）
    - 移除图书相关的数据库表和迁移文件
    - 清理图书相关的API路由和DTO
    - _需求: 代码整洁_

  - [x] 0.2 重构用户管理模块
    - 保留基础用户认证功能（注册、登录、JWT）
    - 移除不必要的用户管理功能
    - 重构用户模型，专注于导航相关字段
    - 更新用户相关的API接口，去掉无关功能
    - _需求: 1.1, 4.1_

  - [x] 0.3 清理前端无关功能
    - 删除图书管理相关的前端页面和组件
    - 移除无关的路由和状态管理
    - 保留基础的认证和用户设置功能
    - 清理无用的依赖和资源文件
    - _需求: 代码整洁_

  - [x] 0.4 数据库结构优化
    - 删除books表和相关索引
    - 保留users表但移除无关字段
    - 创建数据库备份和迁移脚本
    - 更新数据库初始化和种子数据
    - _需求: 数据整洁_

## 阶段一：后端核心功能开发

- [x] 1. 项目基础架构搭建
  - 扩展现有配置文件，添加高德地图API和数据源配置
  - 创建导航相关的数据库表结构和迁移文件
  - 设置定时任务框架和WebSocket支持
  - _需求: 1.1, 2.1, 6.1_

- [x] 2. 数据采集服务开发
  - [x] 2.1 实现检查站数据爬虫
    - 创建HTTP客户端用于访问jinjing365.com
    - 实现HTML解析器提取检查站信息
    - 编写数据验证和清洗逻辑
    - _需求: 1.1, 2.1, 6.1_

  - [x] 2.2 建立数据存储和缓存机制
    - 实现检查站数据的CRUD操作
    - 设置Redis缓存策略提升查询性能
    - 创建数据更新的增量同步机制
    - _需求: 1.2, 2.1, 6.2_

  - [x] 2.3 实现定时数据更新任务
    - 配置cron定时任务每5分钟更新数据
    - 实现失败重试和错误处理机制
    - 添加数据更新状态监控和日志记录
    - _需求: 2.1, 6.1, 6.5_

- [x] 3. 高德地图API集成
  - [x] 3.1 创建高德地图客户端服务
    - 实现高德地图API的HTTP客户端封装
    - 添加API调用限制和错误处理
    - 创建地理编码和路线规划接口
    - _需求: 1.1, 1.4, 6.2_

  - [x] 3.2 实现路线规划核心算法
    - 开发基础路线查询功能
    - 实现检查站位置与路线的交叉检测
    - 创建避让算法，重新规划绕行路线
    - 添加路线风险评估和评分机制
    - _需求: 1.1, 1.4, 4.2_

- [x] 4. 用户管理和认证扩展
  - [x] 4.1 扩展用户模型支持导航功能
    - 添加车牌号、地区等导航相关字段
    - 实现用户偏好设置的存储和管理
    - 创建订阅状态和权限管理（试用期3天、免费、付费）
    - 添加试用期到期时间和权限检查逻辑
    - _需求: 4.1, 7.1, 7.2_

  - [x] 4.2 实现多端认证适配
    - 扩展JWT认证支持多端访问
    - 添加设备标识和会话管理
    - 实现管理员权限和角色控制
    - _需求: 5.1, 6.1_

## 阶段二：API接口开发

- [ ] 5. 核心业务API实现
  - [x] 5.1 检查站相关API
    - 实现检查站列表查询接口
    - 创建附近检查站搜索功能
    - 添加用户举报检查站状态接口
    - _需求: 1.2, 3.1, 5.1_

  - [x] 5.2 路线规划API（带权限控制）
    - 实现路线规划请求处理，集成用户权限验证
    - 创建路线优化和重新计算接口，仅付费用户可用
    - 添加历史路线查询和管理，区分免费和付费功能
    - 实现试用期检查和功能降级逻辑
    - _需求: 1.1, 1.4, 3.1, 7.1_

  - [x] 5.3 用户偏好和订阅API
    - 实现用户偏好设置的CRUD操作
    - 创建订阅服务的购买和管理接口
    - 添加用户使用统计和分析
    - _需求: 4.1, 7.1, 7.4_

  - [x] 5.4 权限控制和会员策略API
    - 实现用户注册时自动分配3天试用期
    - 创建权限验证中间件，检查用户功能访问权限
    - 实现试用期到期后功能降级（仅可查看检查站，不可导航）
    - 添加强制联网验证，防止离线破解使用
    - 创建会员购买、续费和权限升级接口
    - 实现设备绑定和多设备登录限制
    - _需求: 7.1, 7.2, 7.3, 7.4_

- [ ] 6. 实时通知系统
  - [x] 6.1 WebSocket服务实现
    - 创建WebSocket连接管理和用户订阅
    - 实现检查站状态变化的实时推送
    - 添加路线更新通知和用户提醒
    - _需求: 2.1, 2.2, 4.4_

  - [x] 6.2 移动端推送集成
    - 集成Firebase/APNs推送服务
    - 实现推送消息的多端适配
    - 添加推送统计和效果追踪
    - _需求: 2.2, 4.4_

## 阶段三：管理后台开发

- [ ] 7. 管理后台Web应用
  - [x] 7.1 数据管理界面
    - 创建检查站数据的查看和编辑界面
    - 实现数据源监控和状态展示
    - 添加数据统计和分析图表
    - _需求: 6.1, 6.3, 6.4_

  - [-] 7.2 用户和订阅管理
    - 实现用户列表和详情管理
    - 创建订阅服务和支付管理
    - 添加用户行为分析和报表
    - _需求: 6.1, 6.4, 7.3_

  - [x] 7.3 系统监控和运维
    - 创建系统性能监控面板
    - 实现日志查看和错误追踪
    - 添加API调用统计和限制管理
    - _需求: 6.4, 6.5_

## 阶段四：移动端App开发

- [ ] 8. React Native手机App
  - [ ] 8.1 项目初始化和基础配置
    - 创建React Native项目结构
    - 配置导航和状态管理
    - 集成高德地图React Native SDK
    - _需求: 1.1, 4.1_

  - [ ] 8.2 核心导航功能
    - 实现地图显示和位置服务
    - 创建路线规划和导航界面
    - 添加检查站提醒和语音播报
    - _需求: 1.1, 1.4, 2.2_

  - [ ] 8.3 用户功能和权限控制
    - 实现强制登录机制，无法离线使用
    - 集成推送通知服务
    - 实现试用期倒计时和到期提醒
    - 添加功能权限检查，试用期后仅可查看检查站
    - 创建会员购买和续费界面
    - _需求: 2.2, 4.1, 4.4, 7.1_

  - [ ] 8.4 手机App打包和发布
    - 配置Android和iOS打包环境
    - 实现应用签名和发布流程
    - 添加应用更新和版本管理
    - _需求: 1.1_

- [ ] 9. 安卓车机适配App
  - [ ] 9.1 车机系统适配
    - 创建适配车机屏幕尺寸的UI布局
    - 优化触控操作适应车载环境
    - 集成Android Auto兼容性
    - _需求: 1.1, 1.4_

  - [ ] 9.2 车载导航优化（带权限控制）
    - 实现大屏幕地图显示和导航，集成用户权限验证
    - 添加语音指令和播报功能，试用期后导航功能受限
    - 优化夜间模式和亮度自适应
    - 创建简化的操作界面减少驾驶干扰
    - 实现强制联网验证，防止离线破解
    - _需求: 1.1, 1.4, 2.2, 7.1_

  - [ ] 9.3 车机专属功能
    - 集成车辆信息和OBD数据
    - 实现一键导航和快捷设置
    - 添加停车位置记录和寻车功能
    - 支持蓝牙和车载系统集成
    - _需求: 1.1, 4.1_

  - [ ] 9.4 车机App测试和发布
    - 在不同品牌车机上测试兼容性
    - 优化性能和内存使用
    - 创建车机专用的安装包和分发渠道
    - _需求: 1.1_

## 阶段五：微信小程序开发

- [ ] 10. 微信小程序实现
  - [x] 10.1 小程序基础框架
    - 创建小程序项目结构和页面
    - 配置微信小程序地图组件
    - 实现用户授权和登录流程
    - _需求: 1.1, 4.1_

  - [x] 10.2 轻量导航功能（带权限控制）
    - 实现简化版的路线规划功能，集成用户权限验证
    - 创建检查站查询和展示，试用期后仅可查看不可导航
    - 添加位置分享和收藏功能
    - 实现强制登录和联网验证机制
    - _需求: 1.1, 1.4, 4.1, 7.1_

  - [x] 10.3 小程序优化和发布
    - 优化小程序性能和加载速度
    - 实现小程序审核和发布流程
    - 添加小程序分享和推广功能
    - _需求: 1.1, 4.1_

## 阶段六：测试和部署

- [ ] 11. 系统测试
  - [x] 11.1 单元测试和集成测试
    - 编写后端服务的单元测试
    - 实现API接口的集成测试
    - 添加数据爬虫和算法的测试用例
    - _需求: 6.5_

  - [x] 11.2 多端功能测试
    - 测试管理后台的功能完整性
    - 验证手机App和车机App的性能和稳定性
    - 检查小程序的兼容性和用户体验
    - 进行车载环境的实地测试
    - _需求: 1.1, 1.4_

- [ ] 12. 部署和上线
  - [ ] 12.1 生产环境部署
    - 配置生产环境的服务器和数据库
    - 部署后端服务和定时任务
    - 设置监控和日志收集系统
    - _需求: 6.4, 6.5_

  - [ ] 12.2 多端应用发布
    - 发布管理后台到生产环境
    - 提交手机App到应用商店
    - 发布车机App到相关渠道
    - 发布微信小程序到微信平台
    - _需求: 1.1_