# 设计文档

## 概述

外地车进京导航避让系统是一个基于Go后端和React前端的Web应用，通过集成高德地图API和实时数据爬取，为外地车主提供智能的进京路线规划服务。系统采用微服务架构，支持高并发访问和实时数据更新。

## 架构设计

### 系统架构图

```mermaid
graph TB
    subgraph "多端前端层"
        A1[React Web管理端] --> B1[数据管理界面]
        A1 --> C1[用户管理界面]
        A2[React Native手机App] --> B2[移动地图组件]
        A2 --> C2[推送通知]
        A3[安卓车机App] --> B3[车载导航界面]
        A3 --> C3[语音播报]
        A4[微信小程序] --> B4[小程序地图]
        A4 --> C4[轻量导航]
    end
    
    subgraph "API网关层"
        E[Gin HTTP Router] --> F[认证中间件]
        E --> G[限流中间件]
        E --> H[CORS中间件]
        E --> I[多端适配中间件]
    end
    
    subgraph "业务服务层"
        J[导航服务] --> K[路线规划算法]
        L[数据采集服务] --> M[检查站爬虫]
        N[用户服务] --> O[认证授权]
        P[通知服务] --> Q[实时推送]
        R[管理服务] --> S[数据统计分析]
    end
    
    subgraph "数据层"
        T[PostgreSQL] --> U[检查站数据]
        T --> V[用户数据]
        T --> W[路线历史]
        T --> X[管理数据]
        Y[Redis] --> Z[缓存层]
        Y --> AA[会话存储]
    end
    
    subgraph "外部服务"
        BB[高德地图API]
        CC[进京365网站]
        DD[WebSocket推送]
        EE[移动推送服务]
        FF[Android Auto]
    end
    
    A1 --> E
    A2 --> E
    A3 --> E
    A4 --> E
    E --> J
    E --> L
    E --> N
    E --> P
    E --> R
    J --> T
    J --> Y
    J --> BB
    L --> CC
    L --> T
    P --> DD
    P --> EE
    A3 --> FF
```

### 技术栈选择

**后端技术栈：**
- **框架**: Gin (已有) - 高性能HTTP框架
- **数据库**: PostgreSQL (已有) - 关系型数据存储
- **缓存**: Redis (已有) - 高速缓存和会话存储
- **ORM**: GORM (已有) - 数据库操作
- **配置**: Viper (已有) - 配置管理
- **日志**: Zap (已有) - 结构化日志
- **HTTP客户端**: net/http - API调用和数据爬取
- **定时任务**: cron - 定时数据更新
- **WebSocket**: gorilla/websocket - 实时通信

**多端前端技术栈：**

**Web端 (管理后台):**
- **框架**: React (已有) - 管理界面
- **UI组件**: Ant Design - 企业级UI组件
- **图表**: ECharts - 数据可视化
- **表格**: React Table - 数据表格管理

**移动端 App:**
- **框架**: React Native - 跨平台移动开发
- **地图**: 高德地图React Native SDK
- **导航**: React Navigation - 页面导航
- **状态管理**: Zustand - 状态管理
- **推送**: React Native Push Notification

**微信小程序:**
- **框架**: 微信小程序原生开发
- **地图**: 微信小程序地图组件 + 高德地图API
- **UI组件**: WeUI - 微信官方UI组件
- **状态管理**: MobX - 轻量级状态管理
## 组件
设计

### 1. 数据采集模块

**检查站数据爬虫**
```go
type CheckpointCrawler struct {
    client     *http.Client
    parser     *CheckpointParser
    storage    CheckpointRepository
    scheduler  *cron.Cron
}

type CheckpointInfo struct {
    ID          string    `json:"id" gorm:"primaryKey"`
    Name        string    `json:"name"`
    Location    Location  `json:"location"`
    Status      string    `json:"status"` // active, inactive, unknown
    Severity    int       `json:"severity"` // 1-5 检查严格程度
    LastUpdate  time.Time `json:"last_update"`
    Source      string    `json:"source"`
}

type Location struct {
    Latitude  float64 `json:"latitude"`
    Longitude float64 `json:"longitude"`
    Address   string  `json:"address"`
}
```

**数据更新策略**
- 每5分钟爬取一次jinjing365.com
- 使用增量更新，只处理变化的数据
- 失败重试机制，最多重试3次
- 数据验证和清洗，过滤无效信息

### 2. 路线规划模块

**路线规划服务**
```go
type NavigationService struct {
    amapClient     *AmapClient
    checkpointRepo CheckpointRepository
    routeCache     *RouteCache
}

type RouteRequest struct {
    Origin      Location `json:"origin"`
    Destination Location `json:"destination"`
    CarPlate    string   `json:"car_plate"`
    AvoidLevel  int      `json:"avoid_level"` // 1-3 避让程度
}

type RouteResponse struct {
    Routes          []Route           `json:"routes"`
    AvoidedPoints   []CheckpointInfo  `json:"avoided_points"`
    EstimatedTime   int               `json:"estimated_time"`
    Distance        int               `json:"distance"`
    RiskLevel       int               `json:"risk_level"`
}
```

**路线算法设计**
1. 调用高德API获取基础路线
2. 获取路线沿途的检查站信息
3. 根据检查站状态和用户避让级别过滤路线
4. 重新规划避开检查站的路线
5. 计算风险评分和推荐度

### 3. 实时通知模块

**WebSocket推送服务**
```go
type NotificationService struct {
    hub        *WebSocketHub
    userSubs   map[string]*UserSubscription
    broadcaster chan *Notification
}

type UserSubscription struct {
    UserID    string
    Routes    []string  // 订阅的路线ID
    Locations []Location // 关注的位置
}

type Notification struct {
    Type      string      `json:"type"`
    Title     string      `json:"title"`
    Content   string      `json:"content"`
    Data      interface{} `json:"data"`
    Timestamp time.Time   `json:"timestamp"`
}
```

### 4. 用户管理模块

**用户服务扩展**
```go
type NavigationUser struct {
    UserID        string    `json:"user_id" gorm:"primaryKey"`
    CarPlate      string    `json:"car_plate"`
    PlateRegion   string    `json:"plate_region"`
    Subscription  string    `json:"subscription"` // trial, free, premium
    TrialExpiry   time.Time `json:"trial_expiry"`
    Preferences   UserPrefs `json:"preferences"`
    CreatedAt     time.Time `json:"created_at"`
    LastLoginAt   time.Time `json:"last_login_at"`
}

type UserPrefs struct {
    DefaultAvoidLevel int      `json:"default_avoid_level"`
    NotificationTypes []string `json:"notification_types"`
    FavoriteRoutes    []string `json:"favorite_routes"`
}

type SubscriptionService struct {
    userRepo UserRepository
}

// 检查用户权限
func (s *SubscriptionService) CheckUserPermission(userID string, feature string) (*Permission, error) {
    user, err := s.userRepo.GetByID(userID)
    if err != nil {
        return nil, err
    }
    
    now := time.Now()
    
    // 试用期内，所有功能可用
    if user.Subscription == "trial" && now.Before(user.TrialExpiry) {
        return &Permission{CanNavigate: true, CanViewCheckpoints: true}, nil
    }
    
    // 试用期过后，免费用户只能查看检查站
    if user.Subscription == "free" || (user.Subscription == "trial" && now.After(user.TrialExpiry)) {
        return &Permission{CanNavigate: false, CanViewCheckpoints: true}, nil
    }
    
    // 付费用户，所有功能可用
    if user.Subscription == "premium" {
        return &Permission{CanNavigate: true, CanViewCheckpoints: true}, nil
    }
    
    return &Permission{CanNavigate: false, CanViewCheckpoints: false}, nil
}

type Permission struct {
    CanNavigate       bool `json:"can_navigate"`
    CanViewCheckpoints bool `json:"can_view_checkpoints"`
}
```## 接口设计


### RESTful API设计

**检查站相关接口**
```
GET    /api/v1/checkpoints              获取检查站列表
GET    /api/v1/checkpoints/{id}         获取检查站详情
GET    /api/v1/checkpoints/nearby       获取附近检查站
POST   /api/v1/checkpoints/report       用户举报检查站状态
```

**路线规划接口**
```
POST   /api/v1/navigation/route         路线规划
GET    /api/v1/navigation/route/{id}    获取路线详情
POST   /api/v1/navigation/optimize      路线优化
GET    /api/v1/navigation/history       历史路线
```

**用户管理接口**
```
POST   /api/v1/users/profile            更新用户信息
GET    /api/v1/users/subscription       获取订阅状态
POST   /api/v1/users/subscribe          订阅服务
GET    /api/v1/users/preferences        获取用户偏好
PUT    /api/v1/users/preferences        更新用户偏好
```

### WebSocket接口设计

**连接管理**
```
ws://domain/ws/navigation?token={jwt_token}
```

**消息格式**
```json
{
  "type": "checkpoint_update",
  "data": {
    "checkpoint_id": "cp_001",
    "status": "active",
    "severity": 3,
    "affected_routes": ["route_001", "route_002"]
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## 数据模型

### 数据库表设计

**检查站表 (checkpoints)**
```sql
CREATE TABLE checkpoints (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    address VARCHAR(200),
    status VARCHAR(20) DEFAULT 'unknown',
    severity INTEGER DEFAULT 1,
    last_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    source VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**路线记录表 (routes)**
```sql
CREATE TABLE routes (
    id VARCHAR(50) PRIMARY KEY,
    user_id VARCHAR(50) REFERENCES users(id),
    origin_lat DECIMAL(10, 8),
    origin_lng DECIMAL(11, 8),
    dest_lat DECIMAL(10, 8),
    dest_lng DECIMAL(11, 8),
    route_data JSONB,
    avoided_checkpoints JSONB,
    estimated_time INTEGER,
    distance INTEGER,
    risk_level INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**用户导航配置表 (user_navigation_config)**
```sql
CREATE TABLE user_navigation_config (
    user_id VARCHAR(50) PRIMARY KEY REFERENCES users(id),
    car_plate VARCHAR(20),
    plate_region VARCHAR(10),
    subscription VARCHAR(20) DEFAULT 'free',
    preferences JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 错误处理

### 错误类型定义
```go
var (
    ErrCheckpointNotFound = errors.New("检查站信息未找到")
    ErrRouteCalculationFailed = errors.New("路线计算失败")
    ErrAmapAPIError = errors.New("高德地图API调用失败")
    ErrDataSourceUnavailable = errors.New("数据源暂时不可用")
    ErrInvalidCarPlate = errors.New("车牌号格式不正确")
)
```

### 错误处理策略
- API调用失败时使用缓存数据
- 数据源不可用时降级到基础导航功能
- 用户输入验证和友好错误提示
- 系统异常时记录详细日志

## 测试策略

### 单元测试
- 数据爬虫模块测试
- 路线算法测试
- API接口测试
- 数据库操作测试

### 集成测试
- 高德地图API集成测试
- 数据源爬取集成测试
- WebSocket通信测试
- 端到端路线规划测试

### 性能测试
- 并发用户访问测试
- 路线计算性能测试
- 数据库查询优化测试
- 缓存命中率测试