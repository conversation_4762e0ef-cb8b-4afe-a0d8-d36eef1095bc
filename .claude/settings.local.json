{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["Bash(kubectl:*)", "<PERSON><PERSON>(curl:*)", "Bash(docker run --rm app:latest find /app/internal/interfaces/http/router/frontend -name \"*.html\" -o -name \"*.js\" -o -name \"*.css\")", "Bash(docker run --rm app:latest find /app -name \"*.html\" -o -name \"index.html\")", "<PERSON><PERSON>(docker run:*)", "Bash(docker build:*)", "Bash(go build:*)", "Bash(git add:*)", "<PERSON><PERSON>(pkill:*)", "Bash(kill:*)", "<PERSON><PERSON>(make:*)", "Bash(ls:*)", "Bash(task build:*)", "<PERSON><PERSON>(task:*)", "Bash(./deploy/local-k3s/build.sh:*)", "Bash(./deploy/local-k3s/deploy.sh:*)"], "deny": []}}