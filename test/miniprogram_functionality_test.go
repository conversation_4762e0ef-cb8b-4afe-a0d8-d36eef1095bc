package test

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestMiniprogramFunctionality 测试微信小程序的功能完整性
func TestMiniprogramFunctionality(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("小程序用户认证", func(t *testing.T) {
		testMiniprogramAuth(t)
	})

	t.Run("检查站查询功能", func(t *testing.T) {
		testCheckpointQuery(t)
	})

	t.Run("权限控制测试", func(t *testing.T) {
		testPermissionControl(t)
	})

	t.Run("路线规划功能", func(t *testing.T) {
		testRouteNavigation(t)
	})

	t.Run("用户偏好设置", func(t *testing.T) {
		testUserPreferences(t)
	})

	t.Run("位置分享功能", func(t *testing.T) {
		testLocationSharing(t)
	})
}

func testMiniprogramAuth(t *testing.T) {
	router := setupTestRouter()

	// 测试微信小程序登录
	loginData := map[string]interface{}{
		"code":     "test-wx-code",
		"platform": "miniprogram",
		"user_info": map[string]interface{}{
			"nickName":  "测试用户",
			"avatarUrl": "https://example.com/avatar.jpg",
			"gender":    1,
			"city":      "北京",
			"province":  "北京",
			"country":   "中国",
		},
	}

	jsonData, _ := json.Marshal(loginData)
	req, _ := http.NewRequest("POST", "/api/v1/auth/miniprogram/login", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "miniprogram")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	// 验证返回的数据结构
	assert.Contains(t, response, "token")
	assert.Contains(t, response, "user")
	assert.Contains(t, response, "subscription")

	// 验证新用户自动获得试用期
	subscription := response["subscription"].(map[string]interface{})
	assert.Equal(t, "trial", subscription["type"])
	assert.Contains(t, subscription, "trial_expiry")
}

func testCheckpointQuery(t *testing.T) {
	router := setupTestRouter()
	token := getTestUserToken(t, router)

	// 测试获取附近检查站（所有用户都可以使用）
	req, _ := http.NewRequest("GET", "/api/v1/checkpoints/nearby?lat=39.9042&lng=116.4074&radius=10", nil)
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("User-Agent", "miniprogram")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.Contains(t, response, "data")
	assert.Contains(t, response, "total")

	// 测试检查站详情查询
	req, _ = http.NewRequest("GET", "/api/v1/checkpoints/test-checkpoint-id", nil)
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("User-Agent", "miniprogram")

	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// 应该返回成功或者404（如果检查站不存在）
	assert.True(t, w.Code == http.StatusOK || w.Code == http.StatusNotFound)

	// 测试检查站状态举报
	reportData := map[string]interface{}{
		"checkpoint_id": "test-checkpoint-id",
		"status":        "active",
		"severity":      3,
		"comment":       "刚刚经过，检查很严格",
	}

	jsonData, _ := json.Marshal(reportData)
	req, _ = http.NewRequest("POST", "/api/v1/checkpoints/report", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("User-Agent", "miniprogram")

	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
}

func testPermissionControl(t *testing.T) {
	router := setupTestRouter()

	// 测试试用期用户权限
	trialToken := getTrialUserToken(t, router)

	// 试用期用户可以使用导航功能
	routeData := map[string]interface{}{
		"origin":      map[string]float64{"lat": 39.9042, "lng": 116.4074},
		"destination": map[string]float64{"lat": 40.0042, "lng": 116.5074},
		"car_plate":   "京A12345",
		"avoid_level": 2,
	}

	jsonData, _ := json.Marshal(routeData)
	req, _ := http.NewRequest("POST", "/api/v1/navigation/route", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+trialToken)
	req.Header.Set("User-Agent", "miniprogram")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	// 测试免费用户权限（试用期过期）
	freeToken := getFreeUserToken(t, router)

	// 免费用户不能使用导航功能
	req, _ = http.NewRequest("POST", "/api/v1/navigation/route", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+freeToken)
	req.Header.Set("User-Agent", "miniprogram")

	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusForbidden, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.Contains(t, response, "error")
	assert.Contains(t, response["error"].(string), "试用期已过期")

	// 但免费用户可以查看检查站
	req, _ = http.NewRequest("GET", "/api/v1/checkpoints/nearby?lat=39.9042&lng=116.4074&radius=10", nil)
	req.Header.Set("Authorization", "Bearer "+freeToken)
	req.Header.Set("User-Agent", "miniprogram")

	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
}

func testRouteNavigation(t *testing.T) {
	router := setupTestRouter()
	token := getTrialUserToken(t, router) // 使用试用期用户

	// 测试路线规划
	routeData := map[string]interface{}{
		"origin":      map[string]float64{"lat": 39.9042, "lng": 116.4074},
		"destination": map[string]float64{"lat": 40.0042, "lng": 116.5074},
		"car_plate":   "冀A12345", // 外地车牌
		"avoid_level": 2,
	}

	jsonData, _ := json.Marshal(routeData)
	req, _ := http.NewRequest("POST", "/api/v1/navigation/route", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("User-Agent", "miniprogram")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	// 验证路线规划结果
	assert.Contains(t, response, "routes")
	assert.Contains(t, response, "avoided_points")
	assert.Contains(t, response, "estimated_time")
	assert.Contains(t, response, "risk_level")

	// 测试路线历史查询
	req, _ = http.NewRequest("GET", "/api/v1/navigation/history?limit=10", nil)
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("User-Agent", "miniprogram")

	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
}

func testUserPreferences(t *testing.T) {
	router := setupTestRouter()
	token := getTestUserToken(t, router)

	// 测试获取用户偏好
	req, _ := http.NewRequest("GET", "/api/v1/users/preferences", nil)
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("User-Agent", "miniprogram")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	// 测试更新用户偏好
	prefsData := map[string]interface{}{
		"default_avoid_level": 3,
		"notification_types":  []string{"checkpoint_update", "route_change"},
		"favorite_routes":     []string{"home_to_work", "work_to_home"},
		"car_plate":           "冀A12345",
		"plate_region":        "河北",
	}

	jsonData, _ := json.Marshal(prefsData)
	req, _ = http.NewRequest("PUT", "/api/v1/users/preferences", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("User-Agent", "miniprogram")

	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	// 测试获取用户订阅状态
	req, _ = http.NewRequest("GET", "/api/v1/users/subscription", nil)
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("User-Agent", "miniprogram")

	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.Contains(t, response, "subscription")
	assert.Contains(t, response, "permissions")
}

func testLocationSharing(t *testing.T) {
	router := setupTestRouter()
	token := getTestUserToken(t, router)

	// 测试分享位置信息
	shareData := map[string]interface{}{
		"location": map[string]float64{"lat": 39.9042, "lng": 116.4074},
		"message":  "这里有检查站，大家注意",
		"type":     "checkpoint_alert",
	}

	jsonData, _ := json.Marshal(shareData)
	req, _ := http.NewRequest("POST", "/api/v1/community/share", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("User-Agent", "miniprogram")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	// 测试获取附近的分享信息
	req, _ = http.NewRequest("GET", "/api/v1/community/nearby?lat=39.9042&lng=116.4074&radius=5", nil)
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("User-Agent", "miniprogram")

	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.Contains(t, response, "data")
}

// 辅助函数：获取测试用户token
func getTestUserToken(t *testing.T, router *gin.Engine) string {
	loginData := map[string]interface{}{
		"code":     "test-wx-code",
		"platform": "miniprogram",
	}

	jsonData, _ := json.Marshal(loginData)
	req, _ := http.NewRequest("POST", "/api/v1/auth/miniprogram/login", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "miniprogram")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	var response map[string]interface{}
	json.Unmarshal(w.Body.Bytes(), &response)

	if token, ok := response["token"].(string); ok {
		return token
	}

	return "test-miniprogram-token"
}

// 获取试用期用户token
func getTrialUserToken(t *testing.T, router *gin.Engine) string {
	// 创建试用期用户
	userData := map[string]interface{}{
		"username":     "trial_user",
		"password":     "password123",
		"platform":     "miniprogram",
		"subscription": "trial",
	}

	jsonData, _ := json.Marshal(userData)
	req, _ := http.NewRequest("POST", "/api/v1/auth/register", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "miniprogram")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	var response map[string]interface{}
	json.Unmarshal(w.Body.Bytes(), &response)

	if token, ok := response["token"].(string); ok {
		return token
	}

	return "test-trial-token"
}

// 获取免费用户token（试用期过期）
func getFreeUserToken(t *testing.T, router *gin.Engine) string {
	// 创建免费用户（试用期已过期）
	userData := map[string]interface{}{
		"username":     "free_user",
		"password":     "password123",
		"platform":     "miniprogram",
		"subscription": "free",
	}

	jsonData, _ := json.Marshal(userData)
	req, _ := http.NewRequest("POST", "/api/v1/auth/register", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "miniprogram")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	var response map[string]interface{}
	json.Unmarshal(w.Body.Bytes(), &response)

	if token, ok := response["token"].(string); ok {
		return token
	}

	return "test-free-token"
}
