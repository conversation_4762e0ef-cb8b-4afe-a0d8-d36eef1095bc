package test

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/azel-ko/final-ddd/internal/application/services"
	"github.com/azel-ko/final-ddd/internal/domain/entities"
	wsocket "github.com/azel-ko/final-ddd/internal/infrastructure/websocket"
	"github.com/azel-ko/final-ddd/internal/interfaces/http/handlers"
	"github.com/azel-ko/final-ddd/internal/pkg/config"
	"github.com/azel-ko/final-ddd/internal/pkg/logger"
)

func init() {
	// 初始化日志
	logger.Init("info")
}

func TestWebSocketConnection(t *testing.T) {
	// 创建WebSocket配置
	wsConfig := &config.WebSocketConfig{
		Enabled:         true,
		MaxConnections:  100,
		ReadBufferSize:  1024,
		WriteBufferSize: 1024,
		PingPeriod:      "54s",
		PongWait:        "60s",
		WriteWait:       "10s",
		MaxMessageSize:  512,
	}

	// 创建WebSocket Hub
	hub := wsocket.NewHub(wsConfig)
	go hub.Run()
	defer hub.Stop()

	// 创建通知服务
	notificationService := services.NewNotificationService(hub, nil)
	notificationService.Start()
	defer notificationService.Stop()

	// 创建WebSocket处理器
	wsHandler := handlers.NewWebSocketHandler(hub, notificationService)

	// 创建测试路由
	gin.SetMode(gin.TestMode)
	router := gin.New()
	
	// 模拟认证中间件
	router.Use(func(c *gin.Context) {
		c.Set("userID", uint(1))
		c.Next()
	})
	
	router.GET("/ws", wsHandler.HandleConnection)

	// 创建测试服务器
	server := httptest.NewServer(router)
	defer server.Close()

	// 将 http:// 替换为 ws://
	wsURL := strings.Replace(server.URL, "http://", "ws://", 1) + "/ws"

	// 连接WebSocket
	conn, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
	require.NoError(t, err)
	defer conn.Close()

	// 测试连接是否成功
	// 应该收到欢迎消息
	var welcomeMsg map[string]interface{}
	err = conn.ReadJSON(&welcomeMsg)
	require.NoError(t, err)
	assert.Equal(t, "welcome", welcomeMsg["type"])

	// 测试订阅功能
	subscribeMsg := map[string]interface{}{
		"type": "subscribe",
		"data": "checkpoint_update",
	}
	
	err = conn.WriteJSON(subscribeMsg)
	require.NoError(t, err)

	// 应该收到订阅确认
	var confirmMsg map[string]interface{}
	err = conn.ReadJSON(&confirmMsg)
	require.NoError(t, err)
	assert.Equal(t, "subscribed", confirmMsg["type"])
	assert.Equal(t, "checkpoint_update", confirmMsg["topic"])

	t.Log("WebSocket connection test passed")
}

func TestNotificationService(t *testing.T) {
	// 创建WebSocket配置
	wsConfig := &config.WebSocketConfig{
		Enabled:         true,
		MaxConnections:  100,
		ReadBufferSize:  1024,
		WriteBufferSize: 1024,
		PingPeriod:      "54s",
		PongWait:        "60s",
		WriteWait:       "10s",
		MaxMessageSize:  512,
	}

	// 创建WebSocket Hub
	hub := wsocket.NewHub(wsConfig)
	go hub.Run()
	defer hub.Stop()

	// 创建通知服务
	notificationService := services.NewNotificationService(hub, nil)
	notificationService.Start()
	defer notificationService.Stop()

	// 测试用户订阅
	userID := "test_user_1"
	subscription := &services.UserSubscription{
		Routes:      []string{"route_1", "route_2"},
		Checkpoints: []string{"cp_1", "cp_2"},
		Locations: []services.Location{
			{
				Latitude:  39.9042,
				Longitude: 116.4074,
				Radius:    1000,
				Name:      "天安门",
			},
		},
		Preferences: services.NotificationPrefs{
			CheckpointUpdates: true,
			RouteChanges:      true,
			SystemAlerts:      true,
			EnabledTypes:      []string{"checkpoint_update", "route_change"},
		},
	}

	// 订阅用户
	err := notificationService.SubscribeUser(userID, subscription)
	require.NoError(t, err)

	// 验证订阅信息
	retrievedSub, err := notificationService.GetUserSubscription(userID)
	require.NoError(t, err)
	assert.Equal(t, userID, retrievedSub.UserID)
	assert.Equal(t, 2, len(retrievedSub.Routes))
	assert.Equal(t, 2, len(retrievedSub.Checkpoints))
	assert.Equal(t, 1, len(retrievedSub.Locations))

	// 测试检查站更新通知
	checkpoint := &entities.Checkpoint{
		ID:        1,
		Name:      "测试检查站",
		Status:    "active",
		Latitude:  39.9042,
		Longitude: 116.4074,
		Location:  "北京市东城区",
	}

	err = notificationService.NotifyCheckpointUpdate(checkpoint, "inactive")
	require.NoError(t, err)

	// 测试路线变化通知
	err = notificationService.NotifyRouteChange("route_1", "检查站状态变化", []string{"cp_1"})
	require.NoError(t, err)

	// 测试系统警告
	err = notificationService.NotifySystemAlert("系统维护通知", "系统将于今晚进行维护", 3)
	require.NoError(t, err)

	// 测试取消订阅
	err = notificationService.UnsubscribeUser(userID)
	require.NoError(t, err)

	// 验证订阅已取消
	_, err = notificationService.GetUserSubscription(userID)
	assert.Error(t, err)

	t.Log("Notification service test passed")
}

func TestNotificationAPI(t *testing.T) {
	// 创建WebSocket配置
	wsConfig := &config.WebSocketConfig{
		Enabled:         true,
		MaxConnections:  100,
		ReadBufferSize:  1024,
		WriteBufferSize: 1024,
		PingPeriod:      "54s",
		PongWait:        "60s",
		WriteWait:       "10s",
		MaxMessageSize:  512,
	}

	// 创建WebSocket Hub
	hub := wsocket.NewHub(wsConfig)
	go hub.Run()
	defer hub.Stop()

	// 创建通知服务
	notificationService := services.NewNotificationService(hub, nil)
	notificationService.Start()
	defer notificationService.Stop()

	// 创建WebSocket处理器
	wsHandler := handlers.NewWebSocketHandler(hub, notificationService)

	// 创建测试路由
	gin.SetMode(gin.TestMode)
	router := gin.New()
	
	// 模拟认证中间件
	router.Use(func(c *gin.Context) {
		c.Set("userID", uint(1))
		c.Next()
	})

	// 注册路由
	router.POST("/api/notifications/subscribe", wsHandler.SubscribeNotifications)
	router.PUT("/api/notifications/subscription", wsHandler.UpdateSubscription)
	router.GET("/api/notifications/subscription", wsHandler.GetSubscription)
	router.DELETE("/api/notifications/subscription", wsHandler.UnsubscribeNotifications)
	router.GET("/api/notifications/stats", wsHandler.GetNotificationStats)
	router.POST("/api/notifications/test", wsHandler.SendTestNotification)

	// 测试订阅API
	subscribePayload := `{
		"routes": ["route_1", "route_2"],
		"checkpoints": ["cp_1", "cp_2"],
		"locations": [{
			"latitude": 39.9042,
			"longitude": 116.4074,
			"radius": 1000,
			"name": "天安门"
		}],
		"preferences": {
			"checkpoint_updates": true,
			"route_changes": true,
			"system_alerts": true,
			"enabled_types": ["checkpoint_update", "route_change"]
		}
	}`

	req := httptest.NewRequest("POST", "/api/notifications/subscribe", strings.NewReader(subscribePayload))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	
	var subscribeResp map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &subscribeResp)
	require.NoError(t, err)
	assert.Equal(t, "订阅成功", subscribeResp["message"])

	// 测试获取订阅API
	req = httptest.NewRequest("GET", "/api/notifications/subscription", nil)
	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	
	var getResp map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &getResp)
	require.NoError(t, err)
	assert.NotNil(t, getResp["subscription"])

	// 测试统计API
	req = httptest.NewRequest("GET", "/api/notifications/stats", nil)
	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	
	var statsResp map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &statsResp)
	require.NoError(t, err)
	assert.NotNil(t, statsResp["stats"])

	// 测试发送测试通知API
	testNotificationPayload := `{
		"title": "测试通知",
		"content": "这是一个测试通知",
		"priority": 3
	}`

	req = httptest.NewRequest("POST", "/api/notifications/test", strings.NewReader(testNotificationPayload))
	req.Header.Set("Content-Type", "application/json")
	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	
	var testResp map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &testResp)
	require.NoError(t, err)
	assert.Equal(t, "测试通知发送成功", testResp["message"])

	// 测试取消订阅API
	req = httptest.NewRequest("DELETE", "/api/notifications/subscription", nil)
	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	
	var unsubscribeResp map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &unsubscribeResp)
	require.NoError(t, err)
	assert.Equal(t, "取消订阅成功", unsubscribeResp["message"])

	t.Log("Notification API test passed")
}

func TestWebSocketIntegration(t *testing.T) {
	// 创建WebSocket配置
	wsConfig := &config.WebSocketConfig{
		Enabled:         true,
		MaxConnections:  100,
		ReadBufferSize:  1024,
		WriteBufferSize: 1024,
		PingPeriod:      "54s",
		PongWait:        "60s",
		WriteWait:       "10s",
		MaxMessageSize:  512,
	}

	// 创建WebSocket Hub
	hub := wsocket.NewHub(wsConfig)
	go hub.Run()
	defer hub.Stop()

	// 创建通知服务
	notificationService := services.NewNotificationService(hub, nil)
	notificationService.Start()
	defer notificationService.Stop()

	// 创建WebSocket处理器
	wsHandler := handlers.NewWebSocketHandler(hub, notificationService)

	// 创建测试路由
	gin.SetMode(gin.TestMode)
	router := gin.New()
	
	// 模拟认证中间件
	router.Use(func(c *gin.Context) {
		c.Set("userID", uint(1))
		c.Next()
	})
	
	router.GET("/ws", wsHandler.HandleConnection)

	// 创建测试服务器
	server := httptest.NewServer(router)
	defer server.Close()

	// 连接WebSocket
	wsURL := strings.Replace(server.URL, "http://", "ws://", 1) + "/ws"
	conn, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
	require.NoError(t, err)
	defer conn.Close()

	// 读取欢迎消息
	var welcomeMsg map[string]interface{}
	err = conn.ReadJSON(&welcomeMsg)
	require.NoError(t, err)

	// 订阅检查站更新主题
	subscribeMsg := map[string]interface{}{
		"type": "subscribe",
		"data": "checkpoint_update",
	}
	
	err = conn.WriteJSON(subscribeMsg)
	require.NoError(t, err)

	// 读取订阅确认
	var confirmMsg map[string]interface{}
	err = conn.ReadJSON(&confirmMsg)
	require.NoError(t, err)

	// 在另一个goroutine中发送通知
	go func() {
		time.Sleep(100 * time.Millisecond)
		
		// 发送检查站更新通知
		checkpoint := &entities.Checkpoint{
			ID:        1,
			Name:      "测试检查站",
			Status:    "active",
			Latitude:  39.9042,
			Longitude: 116.4074,
			Location:  "北京市东城区",
		}
		
		notificationService.NotifyCheckpointUpdate(checkpoint, "inactive")
	}()

	// 设置读取超时
	conn.SetReadDeadline(time.Now().Add(5 * time.Second))

	// 读取通知消息
	var notificationMsg map[string]interface{}
	err = conn.ReadJSON(&notificationMsg)
	require.NoError(t, err)
	
	assert.Equal(t, "notification", notificationMsg["type"])
	assert.Equal(t, "checkpoint_update", notificationMsg["topic"])
	assert.NotNil(t, notificationMsg["data"])

	t.Log("WebSocket integration test passed")
}