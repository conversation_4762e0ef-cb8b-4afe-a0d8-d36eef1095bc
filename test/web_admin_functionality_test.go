package test

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestWebAdminFunctionality 测试Web管理后台的功能完整性
func TestWebAdminFunctionality(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("管理后台登录功能", func(t *testing.T) {
		testAdminLogin(t)
	})

	t.Run("检查站数据管理", func(t *testing.T) {
		testCheckpointManagement(t)
	})

	t.Run("用户管理功能", func(t *testing.T) {
		testUserManagement(t)
	})

	t.Run("订阅管理功能", func(t *testing.T) {
		testSubscriptionManagement(t)
	})

	t.Run("系统监控功能", func(t *testing.T) {
		testSystemMonitoring(t)
	})

	t.Run("数据统计和报表", func(t *testing.T) {
		testDataAnalytics(t)
	})
}

func testAdminLogin(t *testing.T) {
	router := setupTestRouter()

	// 测试管理员登录
	loginData := map[string]interface{}{
		"username": "admin",
		"password": "admin123",
	}

	jsonData, _ := json.Marshal(loginData)
	req, _ := http.NewRequest("POST", "/api/v1/auth/login", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	// 验证返回的token
	assert.Contains(t, response, "token")
	assert.Contains(t, response, "user")
}

func testCheckpointManagement(t *testing.T) {
	router := setupTestRouter()
	token := getAdminToken(t, router)

	// 测试获取检查站列表
	req, _ := http.NewRequest("GET", "/api/v1/admin/checkpoints", nil)
	req.Header.Set("Authorization", "Bearer "+token)

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.Contains(t, response, "data")
	assert.Contains(t, response, "total")

	// 测试创建检查站
	checkpointData := map[string]interface{}{
		"name":      "测试检查站",
		"latitude":  39.9042,
		"longitude": 116.4074,
		"address":   "北京市朝阳区",
		"status":    "active",
		"severity":  3,
	}

	jsonData, _ := json.Marshal(checkpointData)
	req, _ = http.NewRequest("POST", "/api/v1/admin/checkpoints", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)

	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusCreated, w.Code)

	// 测试更新检查站状态
	updateData := map[string]interface{}{
		"status":   "inactive",
		"severity": 1,
	}

	jsonData, _ = json.Marshal(updateData)
	req, _ = http.NewRequest("PUT", "/api/v1/admin/checkpoints/test-id", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)

	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// 应该返回成功或者404（如果测试数据不存在）
	assert.True(t, w.Code == http.StatusOK || w.Code == http.StatusNotFound)
}

func testUserManagement(t *testing.T) {
	router := setupTestRouter()
	token := getAdminToken(t, router)

	// 测试获取用户列表
	req, _ := http.NewRequest("GET", "/api/v1/admin/users", nil)
	req.Header.Set("Authorization", "Bearer "+token)

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.Contains(t, response, "data")
	assert.Contains(t, response, "total")

	// 测试获取用户详情
	req, _ = http.NewRequest("GET", "/api/v1/admin/users/test-user-id", nil)
	req.Header.Set("Authorization", "Bearer "+token)

	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// 应该返回成功或者404（如果用户不存在）
	assert.True(t, w.Code == http.StatusOK || w.Code == http.StatusNotFound)

	// 测试更新用户订阅状态
	updateData := map[string]interface{}{
		"subscription": "premium",
	}

	jsonData, _ := json.Marshal(updateData)
	req, _ = http.NewRequest("PUT", "/api/v1/admin/users/test-user-id/subscription", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)

	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// 应该返回成功或者404（如果用户不存在）
	assert.True(t, w.Code == http.StatusOK || w.Code == http.StatusNotFound)
}

func testSubscriptionManagement(t *testing.T) {
	router := setupTestRouter()
	token := getAdminToken(t, router)

	// 测试获取订阅统计
	req, _ := http.NewRequest("GET", "/api/v1/admin/subscriptions/stats", nil)
	req.Header.Set("Authorization", "Bearer "+token)

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	// 验证统计数据结构
	assert.Contains(t, response, "total_users")
	assert.Contains(t, response, "trial_users")
	assert.Contains(t, response, "free_users")
	assert.Contains(t, response, "premium_users")

	// 测试获取订阅历史
	req, _ = http.NewRequest("GET", "/api/v1/admin/subscriptions/history", nil)
	req.Header.Set("Authorization", "Bearer "+token)

	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
}

func testSystemMonitoring(t *testing.T) {
	router := setupTestRouter()
	token := getAdminToken(t, router)

	// 测试系统状态监控
	req, _ := http.NewRequest("GET", "/api/v1/admin/system/status", nil)
	req.Header.Set("Authorization", "Bearer "+token)

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	// 验证系统状态数据
	assert.Contains(t, response, "database_status")
	assert.Contains(t, response, "redis_status")
	assert.Contains(t, response, "api_status")

	// 测试API调用统计
	req, _ = http.NewRequest("GET", "/api/v1/admin/system/api-stats", nil)
	req.Header.Set("Authorization", "Bearer "+token)

	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	// 测试数据源状态
	req, _ = http.NewRequest("GET", "/api/v1/admin/system/data-sources", nil)
	req.Header.Set("Authorization", "Bearer "+token)

	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
}

func testDataAnalytics(t *testing.T) {
	router := setupTestRouter()
	token := getAdminToken(t, router)

	// 测试用户行为分析
	req, _ := http.NewRequest("GET", "/api/v1/admin/analytics/user-behavior", nil)
	req.Header.Set("Authorization", "Bearer "+token)

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	// 测试路线使用统计
	req, _ = http.NewRequest("GET", "/api/v1/admin/analytics/route-usage", nil)
	req.Header.Set("Authorization", "Bearer "+token)

	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	// 测试检查站活跃度分析
	req, _ = http.NewRequest("GET", "/api/v1/admin/analytics/checkpoint-activity", nil)
	req.Header.Set("Authorization", "Bearer "+token)

	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
}

// 辅助函数：获取管理员token
func getAdminToken(t *testing.T, router *gin.Engine) string {
	loginData := map[string]interface{}{
		"username": "admin",
		"password": "admin123",
	}

	jsonData, _ := json.Marshal(loginData)
	req, _ := http.NewRequest("POST", "/api/v1/auth/login", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	var response map[string]interface{}
	json.Unmarshal(w.Body.Bytes(), &response)

	if token, ok := response["token"].(string); ok {
		return token
	}

	return "test-token" // 返回测试token
}
