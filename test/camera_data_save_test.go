package test

import (
	"context"
	"testing"
	"time"

	"github.com/azel-ko/final-ddd/internal/application/services"
	"github.com/azel-ko/final-ddd/internal/domain/entities"
	sqliteRepo "github.com/azel-ko/final-ddd/internal/infrastructure/persistence/sqlite"
	"github.com/azel-ko/final-ddd/internal/pkg/logger"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// setupTestDB 设置测试数据库
func setupTestDB() (*gorm.DB, error) {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		return nil, err
	}

	// 自动迁移表结构
	err = db.AutoMigrate(
		&entities.User{},
		&entities.Checkpoint{},
		&entities.CheckpointReport{},
		&entities.Route{},
		&entities.UserPreference{},
		&entities.DataUpdateLog{},
		&entities.UserSession{},
		&entities.PushDevice{},
		&entities.PushLog{},
		&entities.PushTemplate{},
		&entities.PushSubscription{},
	)
	if err != nil {
		return nil, err
	}

	return db, nil
}

// convertRealCameraDataToCrawledData 将真实摄像头数据转换为爬虫数据格式
func convertRealCameraDataToCrawledData(realCameras []RealCameraData) []services.CrawledCameraData {
	crawledData := make([]services.CrawledCameraData, len(realCameras))
	for i, camera := range realCameras {
		crawledData[i] = services.CrawledCameraData{
			Name:     camera.Name,
			Lat:      camera.Lat,
			Lng:      camera.Lng,
			Status:   camera.Status,
			Time:     camera.Time,
			District: camera.District,
			Road:     camera.Road,
		}
	}
	return crawledData
}

// TestSaveCrawledCameraData 测试保存爬虫抓取的摄像头数据
func TestSaveCrawledCameraData(t *testing.T) {
	logger.Init("info")

	// 设置测试数据库
	db, err := setupTestDB()
	require.NoError(t, err, "设置测试数据库应该成功")

	// 创建repository和service
	repo := sqliteRepo.NewSQLiteRepository(db)
	checkpointService := services.NewCheckpointService(repo, nil, nil)

	ctx := context.Background()

	// 获取真实摄像头数据
	t.Log("获取真实摄像头数据...")
	realCameras, err := fetchRealCameraData()
	require.NoError(t, err, "获取真实摄像头数据应该成功")
	require.Greater(t, len(realCameras), 100, "应该获取到足够的摄像头数据")

	// 取前100个摄像头进行测试（避免测试时间过长）
	testCameras := realCameras[:100]
	t.Logf("使用前 %d 个摄像头进行测试", len(testCameras))

	// 转换数据格式
	crawledData := convertRealCameraDataToCrawledData(testCameras)

	// 保存到数据库
	t.Log("保存摄像头数据到数据库...")
	result, err := checkpointService.SaveCrawledCameraData(ctx, crawledData)
	require.NoError(t, err, "保存摄像头数据应该成功")

	// 验证保存结果
	assert.Equal(t, len(testCameras), result.TotalCount, "总数应该匹配")
	assert.Equal(t, len(testCameras), result.SuccessCount, "成功数应该等于总数")
	assert.Equal(t, 0, result.FailedCount, "失败数应该为0")
	assert.Equal(t, 0, result.UpdatedCount, "更新数应该为0（不进行去重）")

	t.Logf("保存结果: 总数=%d, 成功=%d, 失败=%d, 更新=%d",
		result.TotalCount, result.SuccessCount, result.FailedCount, result.UpdatedCount)

	// 验证数据库中的数据
	t.Log("验证数据库中的检查点数据...")
	var checkpointCount int64
	err = db.Model(&entities.Checkpoint{}).Count(&checkpointCount).Error
	require.NoError(t, err, "查询检查点数量应该成功")
	assert.Equal(t, int64(len(testCameras)), checkpointCount, "数据库中的检查点数量应该匹配")

	// 验证数据内容
	var checkpoints []entities.Checkpoint
	err = db.Limit(5).Find(&checkpoints).Error
	require.NoError(t, err, "查询检查点数据应该成功")

	t.Log("验证保存的检查点数据样本:")
	for i, checkpoint := range checkpoints {
		t.Logf("  检查点%d: Name='%s', District='%s', Status='%s', Type='%s', Source='%s'",
			i+1, checkpoint.Name, checkpoint.District, checkpoint.Status, checkpoint.Type, checkpoint.Source)

		// 验证基本字段
		assert.NotEmpty(t, checkpoint.Name, "检查点名称不应为空")
		assert.NotZero(t, checkpoint.Latitude, "纬度不应为0")
		assert.NotZero(t, checkpoint.Longitude, "经度不应为0")
		assert.Equal(t, "北京", checkpoint.Province, "省份应该为北京")
		assert.Equal(t, "北京", checkpoint.City, "城市应该为北京")
		assert.Equal(t, "camera_checkpoint", checkpoint.Type, "类型应该为camera_checkpoint")
		assert.Equal(t, "jinjing365.com", checkpoint.Source, "数据源应该为jinjing365.com")
		assert.GreaterOrEqual(t, checkpoint.Reliability, 0, "可靠性评分应该>=0")
		assert.LessOrEqual(t, checkpoint.Reliability, 100, "可靠性评分应该<=100")
	}
}

// TestUpdateExistingCheckpoints 测试更新现有检查点
func TestUpdateExistingCheckpoints(t *testing.T) {
	logger.Init("info")

	// 设置测试数据库
	db, err := setupTestDB()
	require.NoError(t, err, "设置测试数据库应该成功")

	// 创建repository和service
	repo := sqliteRepo.NewSQLiteRepository(db)
	checkpointService := services.NewCheckpointService(repo, nil, nil)

	ctx := context.Background()

	// 创建测试数据
	testData := []services.CrawledCameraData{
		{
			Name:     "测试摄像头1",
			Lat:      39.9042,
			Lng:      116.4074,
			Status:   "active",
			Time:     "2025-08-07",
			District: "朝阳区",
			Road:     "长安街",
		},
		{
			Name:     "测试摄像头2",
			Lat:      39.9043,
			Lng:      116.4075,
			Status:   "inactive",
			Time:     "2025-08-06",
			District: "朝阳区",
			Road:     "建国门外大街",
		},
	}

	// 第一次保存
	t.Log("第一次保存检查点数据...")
	result1, err := checkpointService.SaveCrawledCameraData(ctx, testData)
	require.NoError(t, err, "第一次保存应该成功")
	assert.Equal(t, 2, result1.SuccessCount, "第一次保存成功数应该为2")
	assert.Equal(t, 0, result1.UpdatedCount, "第一次保存更新数应该为0")

	// 修改数据后再次保存（不进行去重，会创建新记录）
	testData[0].Status = "inactive"
	testData[0].Time = "2025-08-08"
	testData[1].Status = "active"
	testData[1].District = "东城区"

	t.Log("第二次保存检查点数据（创建新记录）...")
	result2, err := checkpointService.SaveCrawledCameraData(ctx, testData)
	require.NoError(t, err, "第二次保存应该成功")
	assert.Equal(t, 2, result2.SuccessCount, "第二次保存成功数应该为2")
	assert.Equal(t, 0, result2.UpdatedCount, "第二次保存更新数应该为0（不进行去重）")

	// 验证数据库中有4条记录（2次保存，每次2个）
	var checkpointCount int64
	err = db.Model(&entities.Checkpoint{}).Count(&checkpointCount).Error
	require.NoError(t, err, "查询检查点数量应该成功")
	assert.Equal(t, int64(4), checkpointCount, "数据库中应该有4个检查点（不去重）")

	// 验证数据已保存（不去重，所以有4个检查点）
	var checkpoints []entities.Checkpoint
	err = db.Find(&checkpoints).Error
	require.NoError(t, err, "查询检查点数据应该成功")

	t.Log("验证保存的检查点数据:")
	for i, checkpoint := range checkpoints {
		t.Logf("  检查点%d: Name='%s', Status='%s', District='%s'",
			i+1, checkpoint.Name, checkpoint.Status, checkpoint.District)
	}

	// 验证有4个检查点（2次保存，每次2个，不去重）
	assert.Equal(t, 4, len(checkpoints), "应该有4个检查点（不去重）")

	// 验证包含了不同状态的摄像头
	statusCount := make(map[string]int)
	for _, checkpoint := range checkpoints {
		statusCount[checkpoint.Status]++
	}

	t.Logf("状态分布: active=%d, inactive=%d", statusCount["active"], statusCount["inactive"])
	assert.Equal(t, 2, statusCount["active"], "应该有2个active状态的检查点")
	assert.Equal(t, 2, statusCount["inactive"], "应该有2个inactive状态的检查点")
}

// TestCheckpointReliabilityCalculation 测试检查点可靠性评分计算
func TestCheckpointReliabilityCalculation(t *testing.T) {
	logger.Init("info")

	// 设置测试数据库
	db, err := setupTestDB()
	require.NoError(t, err, "设置测试数据库应该成功")

	// 创建repository和service
	repo := sqliteRepo.NewSQLiteRepository(db)
	checkpointService := services.NewCheckpointService(repo, nil, nil)

	ctx := context.Background()

	// 测试不同情况的可靠性评分
	testCases := []struct {
		name             string
		data             services.CrawledCameraData
		expectedMinScore int
		expectedMaxScore int
	}{
		{
			name: "活跃摄像头，最新时间，完整信息",
			data: services.CrawledCameraData{
				Name:     "高分摄像头",
				Lat:      39.9042,
				Lng:      116.4074,
				Status:   "active",
				Time:     time.Now().Format("2006-01-02"),
				District: "朝阳区",
				Road:     "长安街",
			},
			expectedMinScore: 90,
			expectedMaxScore: 100,
		},
		{
			name: "非活跃摄像头，旧时间，部分信息",
			data: services.CrawledCameraData{
				Name:     "低分摄像头",
				Lat:      39.9042,
				Lng:      116.4074,
				Status:   "inactive",
				Time:     "2025-07-01",
				District: "",
				Road:     "",
			},
			expectedMinScore: 50,
			expectedMaxScore: 70,
		},
		{
			name: "未知状态，无时间信息",
			data: services.CrawledCameraData{
				Name:     "未知摄像头",
				Lat:      39.9042,
				Lng:      116.4074,
				Status:   "unknown",
				Time:     "",
				District: "海淀区",
				Road:     "",
			},
			expectedMinScore: 50,
			expectedMaxScore: 60,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 保存数据
			result, err := checkpointService.SaveCrawledCameraData(ctx, []services.CrawledCameraData{tc.data})
			require.NoError(t, err, "保存数据应该成功")
			assert.Equal(t, 1, result.SuccessCount, "应该成功保存1个检查点")

			// 查询保存的检查点
			var checkpoint entities.Checkpoint
			err = db.Where("name = ?", tc.data.Name).First(&checkpoint).Error
			require.NoError(t, err, "查询检查点应该成功")

			// 验证可靠性评分
			t.Logf("检查点 '%s' 的可靠性评分: %d", checkpoint.Name, checkpoint.Reliability)
			assert.GreaterOrEqual(t, checkpoint.Reliability, tc.expectedMinScore,
				"可靠性评分应该 >= %d", tc.expectedMinScore)
			assert.LessOrEqual(t, checkpoint.Reliability, tc.expectedMaxScore,
				"可靠性评分应该 <= %d", tc.expectedMaxScore)

			// 清理数据
			db.Where("name = ?", tc.data.Name).Delete(&entities.Checkpoint{})
		})
	}
}

// TestBatchSavePerformance 测试批量保存性能
func TestBatchSavePerformance(t *testing.T) {
	logger.Init("info")

	// 设置测试数据库
	db, err := setupTestDB()
	require.NoError(t, err, "设置测试数据库应该成功")

	// 创建repository和service
	repo := sqliteRepo.NewSQLiteRepository(db)
	checkpointService := services.NewCheckpointService(repo, nil, nil)

	ctx := context.Background()

	// 获取真实摄像头数据
	t.Log("获取真实摄像头数据进行性能测试...")
	realCameras, err := fetchRealCameraData()
	require.NoError(t, err, "获取真实摄像头数据应该成功")

	// 使用前1000个摄像头进行性能测试
	testSize := 1000
	if len(realCameras) < testSize {
		testSize = len(realCameras)
	}
	testCameras := realCameras[:testSize]
	crawledData := convertRealCameraDataToCrawledData(testCameras)

	t.Logf("开始批量保存 %d 个摄像头数据...", len(crawledData))

	// 记录开始时间
	startTime := time.Now()

	// 批量保存
	result, err := checkpointService.SaveCrawledCameraData(ctx, crawledData)
	require.NoError(t, err, "批量保存应该成功")

	// 记录结束时间
	duration := time.Since(startTime)

	// 输出性能统计
	t.Logf("批量保存完成:")
	t.Logf("  数据量: %d 个摄像头", len(crawledData))
	t.Logf("  耗时: %v", duration)
	t.Logf("  成功: %d 个", result.SuccessCount)
	t.Logf("  失败: %d 个", result.FailedCount)
	t.Logf("  平均速度: %.2f 个/秒", float64(len(crawledData))/duration.Seconds())

	// 验证结果
	assert.Equal(t, len(crawledData), result.TotalCount, "总数应该匹配")
	assert.Greater(t, result.SuccessCount, len(crawledData)*8/10, "成功率应该 > 80%")
	assert.Less(t, duration, 30*time.Second, "批量保存应该在30秒内完成")

	// 验证数据库中的数据
	var checkpointCount int64
	err = db.Model(&entities.Checkpoint{}).Count(&checkpointCount).Error
	require.NoError(t, err, "查询检查点数量应该成功")
	t.Logf("数据库中的检查点数量: %d", checkpointCount)
	assert.Equal(t, int64(result.SuccessCount), checkpointCount, "数据库中的数量应该等于成功保存的数量")
}

// TestCrawlerIntegration 测试爬虫集成功能
func TestCrawlerIntegration(t *testing.T) {
	logger.Init("info")

	// 设置测试数据库
	db, err := setupTestDB()
	require.NoError(t, err, "设置测试数据库应该成功")

	// 创建repository和service
	repo := sqliteRepo.NewSQLiteRepository(db)
	checkpointService := services.NewCheckpointService(repo, nil, nil)

	ctx := context.Background()

	t.Log("开始完整的爬虫数据保存流程测试...")

	// 1. 获取真实摄像头数据
	t.Log("步骤1: 获取真实摄像头数据...")
	realCameras, err := fetchRealCameraData()
	require.NoError(t, err, "获取真实摄像头数据应该成功")
	t.Logf("获取到 %d 个摄像头数据", len(realCameras))

	// 2. 数据质量分析
	t.Log("步骤2: 分析数据质量...")
	activeCount := 0
	districtCount := make(map[string]int)
	for _, camera := range realCameras {
		if camera.Status == "active" {
			activeCount++
		}
		if camera.District != "" {
			districtCount[camera.District]++
		}
	}
	t.Logf("活跃摄像头: %d 个 (%.1f%%)", activeCount, float64(activeCount)/float64(len(realCameras))*100)
	t.Logf("有区域信息的摄像头: %d 个", len(districtCount))

	// 3. 转换数据格式
	t.Log("步骤3: 转换数据格式...")
	crawledData := convertRealCameraDataToCrawledData(realCameras)

	// 4. 批量保存到数据库
	t.Log("步骤4: 批量保存到数据库...")
	startTime := time.Now()
	result, err := checkpointService.SaveCrawledCameraData(ctx, crawledData)
	require.NoError(t, err, "批量保存应该成功")
	duration := time.Since(startTime)

	// 5. 验证保存结果
	t.Log("步骤5: 验证保存结果...")
	t.Logf("保存结果统计:")
	t.Logf("  总数: %d", result.TotalCount)
	t.Logf("  成功: %d", result.SuccessCount)
	t.Logf("  失败: %d", result.FailedCount)
	t.Logf("  更新: %d", result.UpdatedCount)
	t.Logf("  耗时: %v", duration)
	t.Logf("  成功率: %.1f%%", float64(result.SuccessCount)/float64(result.TotalCount)*100)

	// 验证基本要求
	assert.Equal(t, len(realCameras), result.TotalCount, "总数应该匹配")
	assert.Greater(t, result.SuccessCount, result.TotalCount*8/10, "成功率应该 > 80%")

	// 6. 验证数据库中的数据
	t.Log("步骤6: 验证数据库中的数据...")
	var checkpointCount int64
	err = db.Model(&entities.Checkpoint{}).Count(&checkpointCount).Error
	require.NoError(t, err, "查询检查点数量应该成功")

	// 数据库中的数量应该等于成功保存的数量（不进行去重）
	assert.Equal(t, int64(result.SuccessCount), checkpointCount, "数据库中的数量应该等于成功保存的数量")

	t.Logf("数据库统计: 总检查点=%d, 成功保存=%d, 更新=%d",
		checkpointCount, result.SuccessCount, result.UpdatedCount)

	// 7. 验证数据分布
	t.Log("步骤7: 验证数据分布...")
	var districtStats []struct {
		District string
		Count    int64
	}
	err = db.Model(&entities.Checkpoint{}).
		Select("district, count(*) as count").
		Where("district != ''").
		Group("district").
		Order("count desc").
		Limit(10).
		Find(&districtStats).Error
	require.NoError(t, err, "查询区域分布应该成功")

	t.Log("检查点区域分布 (前10名):")
	for i, stat := range districtStats {
		t.Logf("  %d. %s: %d个", i+1, stat.District, stat.Count)
	}

	// 8. 验证数据类型
	t.Log("步骤8: 验证数据类型...")
	var typeStats []struct {
		Type  string
		Count int64
	}
	err = db.Model(&entities.Checkpoint{}).
		Select("type, count(*) as count").
		Group("type").
		Find(&typeStats).Error
	require.NoError(t, err, "查询类型分布应该成功")

	t.Log("检查点类型分布:")
	for _, stat := range typeStats {
		t.Logf("  %s: %d个", stat.Type, stat.Count)
		if stat.Type == "camera_checkpoint" {
			assert.Equal(t, checkpointCount, stat.Count, "所有检查点都应该是camera_checkpoint类型")
		}
	}

	t.Log("✓ 爬虫集成测试完成")
}
