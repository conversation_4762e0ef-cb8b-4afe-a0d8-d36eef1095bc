#!/bin/bash

# 多端功能测试运行脚本
# 测试Web管理后台和小程序的功能完整性

set -e

echo "🚀 开始多端功能测试..."
echo "================================"

# 检查依赖
echo "📋 检查测试依赖..."

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "❌ Go环境未安装"
    exit 1
fi

# 检查Node.js环境
if ! command -v node &> /dev/null; then
    echo "❌ Node.js环境未安装"
    exit 1
fi

# 检查npm
if ! command -v npm &> /dev/null; then
    echo "❌ npm未安装"
    exit 1
fi

echo "✅ 依赖检查完成"

# 设置测试环境变量
export GO_ENV=test
export API_BASE_URL=${API_BASE_URL:-"http://localhost:8080"}
export DB_HOST=${DB_HOST:-"localhost"}
export DB_PORT=${DB_PORT:-"5432"}
export DB_NAME=${DB_NAME:-"beijing_navigation_test"}
export DB_USER=${DB_USER:-"postgres"}
export DB_PASSWORD=${DB_PASSWORD:-"password"}
export REDIS_HOST=${REDIS_HOST:-"localhost"}
export REDIS_PORT=${REDIS_PORT:-"6379"}

echo "🔧 环境变量设置完成"

# 创建测试结果目录
mkdir -p test/results

# 1. 运行Go后端测试
echo ""
echo "🔧 运行后端功能测试..."
echo "--------------------------------"

# 运行Web管理后台功能测试
echo "📊 测试Web管理后台功能..."
if go test -v ./test -run TestWebAdminFunctionality > test/results/web_admin_test.log 2>&1; then
    echo "✅ Web管理后台功能测试通过"
    WEB_ADMIN_PASSED=true
else
    echo "❌ Web管理后台功能测试失败"
    echo "详细日志: test/results/web_admin_test.log"
    WEB_ADMIN_PASSED=false
fi

# 运行小程序功能测试
echo "📱 测试小程序功能..."
if go test -v ./test -run TestMiniprogramFunctionality > test/results/miniprogram_test.log 2>&1; then
    echo "✅ 小程序功能测试通过"
    MINIPROGRAM_PASSED=true
else
    echo "❌ 小程序功能测试失败"
    echo "详细日志: test/results/miniprogram_test.log"
    MINIPROGRAM_PASSED=false
fi

# 2. 安装前端测试依赖
echo ""
echo "📦 安装前端测试依赖..."
echo "--------------------------------"

if [ ! -d "node_modules" ]; then
    if [ -f "package.json" ]; then
        npm install
    else
        # 创建临时package.json用于测试
        cat > package.json << EOF
{
  "name": "beijing-navigation-test",
  "version": "1.0.0",
  "description": "Beijing Navigation Test Suite",
  "scripts": {
    "test": "node test/frontend_test.js"
  },
  "dependencies": {
    "axios": "^1.6.0",
    "assert": "^2.0.0"
  }
}
EOF
        npm install
    fi
fi

# 3. 运行前端集成测试
echo ""
echo "🌐 运行前端集成测试..."
echo "--------------------------------"

# 检查后端服务是否运行
echo "🔍 检查后端服务状态..."
if curl -s "$API_BASE_URL/health" > /dev/null 2>&1; then
    echo "✅ 后端服务运行正常"
    BACKEND_RUNNING=true
else
    echo "⚠️  后端服务未运行，将启动测试服务器..."
    BACKEND_RUNNING=false
    
    # 启动测试服务器（后台运行）
    echo "🚀 启动测试服务器..."
    go run cmd/main.go &
    SERVER_PID=$!
    
    # 等待服务器启动
    echo "⏳ 等待服务器启动..."
    for i in {1..30}; do
        if curl -s "$API_BASE_URL/health" > /dev/null 2>&1; then
            echo "✅ 测试服务器启动成功"
            BACKEND_RUNNING=true
            break
        fi
        sleep 1
    done
    
    if [ "$BACKEND_RUNNING" = false ]; then
        echo "❌ 测试服务器启动失败"
        exit 1
    fi
fi

# 运行前端测试
echo "🧪 执行前端功能测试..."
if node test/frontend_test.js > test/results/frontend_test.log 2>&1; then
    echo "✅ 前端功能测试通过"
    FRONTEND_PASSED=true
else
    echo "❌ 前端功能测试失败"
    echo "详细日志: test/results/frontend_test.log"
    FRONTEND_PASSED=false
fi

# 4. 性能测试
echo ""
echo "⚡ 运行性能测试..."
echo "--------------------------------"

# 简单的性能测试
echo "🔍 测试API响应时间..."
RESPONSE_TIME=$(curl -o /dev/null -s -w '%{time_total}' "$API_BASE_URL/api/v1/checkpoints/nearby?lat=39.9042&lng=116.4074&radius=10" || echo "999")

if (( $(echo "$RESPONSE_TIME < 2.0" | bc -l) )); then
    echo "✅ API响应时间正常: ${RESPONSE_TIME}s"
    PERFORMANCE_PASSED=true
else
    echo "⚠️  API响应时间较慢: ${RESPONSE_TIME}s"
    PERFORMANCE_PASSED=false
fi

# 5. 清理测试环境
echo ""
echo "🧹 清理测试环境..."
echo "--------------------------------"

# 停止测试服务器
if [ ! -z "$SERVER_PID" ]; then
    echo "🛑 停止测试服务器..."
    kill $SERVER_PID 2>/dev/null || true
    wait $SERVER_PID 2>/dev/null || true
fi

# 6. 生成测试报告
echo ""
echo "📊 生成测试报告..."
echo "--------------------------------"

# 创建汇总报告
cat > test/results/test_summary.md << EOF
# 多端功能测试报告

## 测试时间
$(date '+%Y-%m-%d %H:%M:%S')

## 测试环境
- API Base URL: $API_BASE_URL
- Go Version: $(go version)
- Node Version: $(node --version)

## 测试结果

### Web管理后台功能测试
- 状态: $([ "$WEB_ADMIN_PASSED" = true ] && echo "✅ 通过" || echo "❌ 失败")
- 日志: test/results/web_admin_test.log

### 小程序功能测试
- 状态: $([ "$MINIPROGRAM_PASSED" = true ] && echo "✅ 通过" || echo "❌ 失败")
- 日志: test/results/miniprogram_test.log

### 前端集成测试
- 状态: $([ "$FRONTEND_PASSED" = true ] && echo "✅ 通过" || echo "❌ 失败")
- 日志: test/results/frontend_test.log
- 详细报告: test/frontend_test_report.json

### 性能测试
- 状态: $([ "$PERFORMANCE_PASSED" = true ] && echo "✅ 通过" || echo "⚠️ 需要优化")
- API响应时间: ${RESPONSE_TIME}s

## 总体结果
EOF

# 计算总体通过率
TOTAL_TESTS=4
PASSED_TESTS=0

[ "$WEB_ADMIN_PASSED" = true ] && ((PASSED_TESTS++))
[ "$MINIPROGRAM_PASSED" = true ] && ((PASSED_TESTS++))
[ "$FRONTEND_PASSED" = true ] && ((PASSED_TESTS++))
[ "$PERFORMANCE_PASSED" = true ] && ((PASSED_TESTS++))

SUCCESS_RATE=$((PASSED_TESTS * 100 / TOTAL_TESTS))

echo "- 通过测试: $PASSED_TESTS/$TOTAL_TESTS" >> test/results/test_summary.md
echo "- 成功率: $SUCCESS_RATE%" >> test/results/test_summary.md

if [ $PASSED_TESTS -eq $TOTAL_TESTS ]; then
    echo "- 总体状态: ✅ 全部通过" >> test/results/test_summary.md
    OVERALL_PASSED=true
else
    echo "- 总体状态: ❌ 部分失败" >> test/results/test_summary.md
    OVERALL_PASSED=false
fi

# 7. 输出最终结果
echo ""
echo "🎯 测试完成！"
echo "================================"
echo "📊 测试结果汇总:"
echo "  - Web管理后台: $([ "$WEB_ADMIN_PASSED" = true ] && echo "✅" || echo "❌")"
echo "  - 小程序功能: $([ "$MINIPROGRAM_PASSED" = true ] && echo "✅" || echo "❌")"
echo "  - 前端集成: $([ "$FRONTEND_PASSED" = true ] && echo "✅" || echo "❌")"
echo "  - 性能测试: $([ "$PERFORMANCE_PASSED" = true ] && echo "✅" || echo "⚠️")"
echo ""
echo "📈 成功率: $SUCCESS_RATE% ($PASSED_TESTS/$TOTAL_TESTS)"
echo "📄 详细报告: test/results/test_summary.md"

if [ "$OVERALL_PASSED" = true ]; then
    echo ""
    echo "🎉 所有测试通过！系统功能正常。"
    exit 0
else
    echo ""
    echo "⚠️  部分测试失败，请检查日志文件。"
    exit 1
fi