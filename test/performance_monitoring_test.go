package test

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/azel-ko/final-ddd/internal/application/services"
	"github.com/azel-ko/final-ddd/internal/infrastructure/cache"
	"github.com/azel-ko/final-ddd/internal/infrastructure/monitoring"
	"github.com/azel-ko/final-ddd/internal/infrastructure/persistence"
	"github.com/azel-ko/final-ddd/internal/pkg/config"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestPerformanceMonitoringAndOptimization(t *testing.T) {
	// 加载配置
	cfg, err := config.Load()
	require.NoError(t, err)

	// 初始化数据库和仓库
	repo, db, err := persistence.NewRepository(cfg)
	require.NoError(t, err)

	// 初始化Redis缓存
	redisAddr := fmt.Sprintf("%s:%d", cfg.Redis.Host, cfg.Redis.Port)
	redisCache := cache.NewRedisCache(redisAddr, cfg.Redis.Password)

	// 创建统计收集器
	statsCollector := monitoring.NewStatsCollector(redisCache)

	// 创建数据库优化服务
	dbOptService := services.NewDatabaseOptimizationService(repo, db)

	ctx := context.Background()

	t.Run("测试数据库优化初始化", func(t *testing.T) {
		err := dbOptService.InitializeOptimizations(ctx)
		assert.NoError(t, err)
	})

	t.Run("测试API统计收集", func(t *testing.T) {
		// 模拟API调用统计
		stats := monitoring.APICallStats{
			Endpoint:     "/api/checkpoints",
			Method:       "GET",
			StatusCode:   200,
			ResponseTime: 150,
			Timestamp:    time.Now(),
			ClientIP:     "127.0.0.1",
			UserAgent:    "test-agent",
			RequestSize:  100,
			ResponseSize: 1024,
		}

		statsCollector.RecordAPICall(stats)

		// 验证统计数据
		apiStats, err := statsCollector.GetAPIStats(ctx, "/api/checkpoints", "GET")
		assert.NoError(t, err)
		assert.NotNil(t, apiStats)
	})

	t.Run("测试错误记录", func(t *testing.T) {
		// 模拟错误记录
		errorEntry := monitoring.ErrorEntry{
			Timestamp: time.Now(),
			ErrorType: "TestError",
			Message:   "This is a test error",
			Endpoint:  "/api/test",
			Method:    "POST",
			ClientIP:  "127.0.0.1",
		}

		statsCollector.RecordError(errorEntry)

		// 验证错误记录
		errors, total, err := statsCollector.GetErrors(ctx, 10, 0)
		assert.NoError(t, err)
		assert.Greater(t, total, int64(0))
		assert.NotEmpty(t, errors)
	})

	t.Run("测试日志记录", func(t *testing.T) {
		// 模拟日志记录
		logEntry := monitoring.LogEntry{
			Timestamp: time.Now(),
			Level:     "INFO",
			Message:   "Test log message",
			Source:    "test",
			Details: map[string]interface{}{
				"test_key": "test_value",
			},
		}

		statsCollector.RecordLog(logEntry)

		// 验证日志记录
		logs, total, err := statsCollector.GetLogs(ctx, 10, 0)
		assert.NoError(t, err)
		assert.Greater(t, total, int64(0))
		assert.NotEmpty(t, logs)
	})

	t.Run("测试性能分析", func(t *testing.T) {
		perfStats, err := dbOptService.AnalyzePerformance(ctx)
		assert.NoError(t, err)
		assert.NotNil(t, perfStats)
	})

	t.Run("测试优化建议", func(t *testing.T) {
		suggestions, err := dbOptService.GetOptimizationSuggestions(ctx)
		assert.NoError(t, err)
		assert.NotNil(t, suggestions)
		assert.NotNil(t, suggestions.OptimizationTips)
	})

	t.Run("测试连接池统计", func(t *testing.T) {
		poolStats := dbOptService.GetConnectionPoolStats()
		assert.NotNil(t, poolStats)
		assert.Contains(t, poolStats, "max_open_connections")
		assert.Contains(t, poolStats, "open_connections")
	})

	t.Run("测试数据库健康检查", func(t *testing.T) {
		health := dbOptService.DatabaseHealthCheck(ctx)
		assert.NotNil(t, health)
		assert.Contains(t, health, "connection")
		assert.Contains(t, health, "connection_pool")
		assert.Contains(t, health, "performance")
	})

	t.Run("测试数据库指标", func(t *testing.T) {
		metrics := dbOptService.GetDatabaseMetrics(ctx)
		assert.NotNil(t, metrics)
		assert.Contains(t, metrics, "connection_pool")
		assert.Contains(t, metrics, "performance")
	})

	t.Run("测试查询缓存", func(t *testing.T) {
		// 测试查询优化（带缓存）
		sql := "SELECT COUNT(*) FROM users"
		result1, err := dbOptService.OptimizeQuery(ctx, sql, []interface{}{})
		assert.NoError(t, err)

		// 第二次查询应该从缓存获取
		result2, err := dbOptService.OptimizeQuery(ctx, sql, []interface{}{})
		assert.NoError(t, err)
		assert.Equal(t, result1, result2)
	})

	t.Run("测试慢查询检测", func(t *testing.T) {
		// 获取慢查询列表
		slowQueries := dbOptService.GetSlowQueries(100 * time.Millisecond)
		assert.NotNil(t, slowQueries)
		// 慢查询列表可能为空，这是正常的
	})

	t.Run("测试表优化", func(t *testing.T) {
		err := dbOptService.OptimizeSpecificTable(ctx, "users")
		assert.NoError(t, err)
	})

	t.Run("测试统计数据清理", func(t *testing.T) {
		// 测试清理功能
		statsCollector.Cleanup(ctx)

		// 清理后应该仍然可以正常工作
		stats := monitoring.APICallStats{
			Endpoint:     "/api/test-cleanup",
			Method:       "GET",
			StatusCode:   200,
			ResponseTime: 100,
			Timestamp:    time.Now(),
			ClientIP:     "127.0.0.1",
		}

		statsCollector.RecordAPICall(stats)

		apiStats, err := statsCollector.GetAPIStats(ctx, "/api/test-cleanup", "GET")
		assert.NoError(t, err)
		assert.NotNil(t, apiStats)
	})
}

func TestMonitoringMiddleware(t *testing.T) {
	// 这个测试需要在实际的HTTP环境中运行
	// 这里只是验证中间件可以正常创建

	// 加载配置
	cfg, err := config.Load()
	require.NoError(t, err)

	// 初始化Redis缓存
	redisAddr := fmt.Sprintf("%s:%d", cfg.Redis.Host, cfg.Redis.Port)
	redisCache := cache.NewRedisCache(redisAddr, cfg.Redis.Password)

	// 创建统计收集器
	statsCollector := monitoring.NewStatsCollector(redisCache)

	t.Run("测试中间件创建", func(t *testing.T) {
		// 验证中间件可以正常创建
		assert.NotNil(t, statsCollector)

		// 这些中间件函数应该可以正常调用
		// 在实际的HTTP测试中，这些会被Gin框架调用
		// middleware.EnhancedAPIStatsMiddleware(statsCollector)
		// middleware.EnhancedErrorTrackingMiddleware(statsCollector)
		// middleware.EnhancedPerformanceMonitoringMiddleware(statsCollector)
		// middleware.DatabaseQueryMonitoringMiddleware(statsCollector)
	})
}

func TestPerformanceThresholds(t *testing.T) {
	t.Run("测试性能阈值", func(t *testing.T) {
		// 定义性能阈值
		slowRequestThreshold := 2 * time.Second
		largeResponseThreshold := 1024 * 1024 // 1MB

		// 测试慢请求检测
		normalDuration := 100 * time.Millisecond
		slowDuration := 3 * time.Second

		assert.True(t, normalDuration < slowRequestThreshold)
		assert.True(t, slowDuration > slowRequestThreshold)

		// 测试大响应检测
		normalSize := 1024           // 1KB
		largeSize := 2 * 1024 * 1024 // 2MB

		assert.True(t, normalSize < largeResponseThreshold)
		assert.True(t, largeSize > largeResponseThreshold)
	})
}

func BenchmarkStatsCollection(b *testing.B) {
	// 加载配置
	cfg, err := config.Load()
	require.NoError(b, err)

	// 初始化Redis缓存
	redisAddr := fmt.Sprintf("%s:%d", cfg.Redis.Host, cfg.Redis.Port)
	redisCache := cache.NewRedisCache(redisAddr, cfg.Redis.Password)

	// 创建统计收集器
	statsCollector := monitoring.NewStatsCollector(redisCache)

	b.Run("API统计记录性能", func(b *testing.B) {
		stats := monitoring.APICallStats{
			Endpoint:     "/api/benchmark",
			Method:       "GET",
			StatusCode:   200,
			ResponseTime: 100,
			Timestamp:    time.Now(),
			ClientIP:     "127.0.0.1",
			RequestSize:  100,
			ResponseSize: 1024,
		}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			stats.Timestamp = time.Now()
			statsCollector.RecordAPICall(stats)
		}
	})

	b.Run("错误记录性能", func(b *testing.B) {
		errorEntry := monitoring.ErrorEntry{
			Timestamp: time.Now(),
			ErrorType: "BenchmarkError",
			Message:   "Benchmark error message",
			Endpoint:  "/api/benchmark",
			Method:    "GET",
			ClientIP:  "127.0.0.1",
		}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			errorEntry.Timestamp = time.Now()
			statsCollector.RecordError(errorEntry)
		}
	})
}
