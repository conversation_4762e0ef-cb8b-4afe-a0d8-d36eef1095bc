package test

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/azel-ko/final-ddd/internal/application/dto"
	"github.com/azel-ko/final-ddd/internal/domain/entities"
	"github.com/azel-ko/final-ddd/internal/infrastructure/cache"
	"github.com/azel-ko/final-ddd/internal/infrastructure/migration"
	"github.com/azel-ko/final-ddd/internal/infrastructure/persistence"
	"github.com/azel-ko/final-ddd/internal/interfaces/http/router"
	"github.com/azel-ko/final-ddd/internal/pkg/config"
	baseMigration "github.com/azel-ko/final-ddd/internal/pkg/database/migration"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestCheckpointInterface 测试检查站查询接口的完整功能
func TestCheckpointInterface(t *testing.T) {
	// 设置测试环境
	cfg := &config.Config{
		App: config.App{
			Env: "test",
		},
		Database: config.DatabaseConfig{
			Type: "sqlite",
			Path: ":memory:",
		},
		JWT: config.JWTConfig{
			Key: "test-secret-key-for-jwt-signing",
		},
		Redis: config.RedisConfig{
			Host:     "localhost",
			Port:     6379,
			Password: "",
			DB:       1,
		},
	}

	// 初始化数据库
	repo, db, err := persistence.NewRepository(cfg)
	require.NoError(t, err, "Failed to initialize repository")

	// 运行数据库迁移
	migrator := baseMigration.NewMigrator(db)
	migration.RegisterMigrations(migrator)
	err = migrator.Run()
	require.NoError(t, err, "Failed to run database migrations")

	// 初始化Redis缓存
	redisAddr := fmt.Sprintf("%s:%d", cfg.Redis.Host, cfg.Redis.Port)
	redisCache := cache.NewRedisCache(redisAddr, cfg.Redis.Password)

	// 测试Redis连接
	ctx := context.Background()
	if err := redisCache.Ping(ctx); err != nil {
		t.Logf("Redis not available, some tests may be skipped: %v", err)
	}

	// 设置路由
	gin.SetMode(gin.TestMode)
	r := router.Setup(cfg, repo, redisCache)

	// 创建测试用户和token
	token := createTestUserAndGetToken(t, r)

	// 创建测试检查站数据
	createTestCheckpoints(t, repo)

	t.Run("检查站列表查询接口测试", func(t *testing.T) {
		testCheckpointList(t, r, token)
	})

	t.Run("检查站详情查询接口测试", func(t *testing.T) {
		testCheckpointDetail(t, r, token)
	})

	t.Run("附近检查站查询接口测试", func(t *testing.T) {
		testNearbyCheckpoints(t, r, token)
	})

	t.Run("检查站搜索过滤测试", func(t *testing.T) {
		testCheckpointFiltering(t, r, token)
	})

	t.Run("检查站报告接口测试", func(t *testing.T) {
		testCheckpointReporting(t, r, token)
	})
}

// createTestUserAndGetToken 创建测试用户并获取token
func createTestUserAndGetToken(t *testing.T, r *gin.Engine) string {
	// 注册测试用户
	registerReq := dto.UserRequest{
		Name:     "检查站测试用户",
		Email:    "<EMAIL>",
		Password: "password123",
	}
	jsonData, _ := json.Marshal(registerReq)
	req, _ := http.NewRequest("POST", "/api/auth/register", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusCreated, w.Code)

	// 登录获取token
	loginReq := dto.LoginRequest{
		Email:    "<EMAIL>",
		Password: "password123",
		Platform: "web",
	}
	jsonData, _ = json.Marshal(loginReq)
	req, _ = http.NewRequest("POST", "/api/auth/login", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusOK, w.Code)

	var loginResponse dto.LoginResponse
	err := json.Unmarshal(w.Body.Bytes(), &loginResponse)
	require.NoError(t, err)
	return loginResponse.Token
}

// createTestCheckpoints 创建测试检查站数据
func createTestCheckpoints(t *testing.T, repo interface{}) {
	// 类型断言获取repository接口
	checkpointRepo, ok := repo.(interface {
		CreateCheckpoint(*entities.Checkpoint) error
	})
	require.True(t, ok, "Repository does not implement CreateCheckpoint method")

	now := time.Now()
	testCheckpoints := []*entities.Checkpoint{
		{
			Name:        "京藏高速进京检查站",
			Location:    "京藏高速公路进京方向",
			Latitude:    40.0776,
			Longitude:   116.3297,
			Province:    "北京",
			City:        "北京",
			District:    "昌平区",
			Road:        "京藏高速",
			Direction:   "进京方向",
			Status:      "active",
			Type:        "checkpoint",
			Source:      "test",
			Reliability: 85,
			LastSeen:    &now,
			CreatedAt:   now,
			UpdatedAt:   now,
		},
		{
			Name:        "京沪高速进京检查站",
			Location:    "京沪高速公路进京方向",
			Latitude:    39.7284,
			Longitude:   116.2734,
			Province:    "北京",
			City:        "北京",
			District:    "大兴区",
			Road:        "京沪高速",
			Direction:   "进京方向",
			Status:      "active",
			Type:        "checkpoint",
			Source:      "test",
			Reliability: 90,
			LastSeen:    &now,
			CreatedAt:   now,
			UpdatedAt:   now,
		},
		{
			Name:        "京港澳高速进京检查站",
			Location:    "京港澳高速公路进京方向",
			Latitude:    39.6891,
			Longitude:   116.1831,
			Province:    "北京",
			City:        "北京",
			District:    "房山区",
			Road:        "京港澳高速",
			Direction:   "进京方向",
			Status:      "inactive",
			Type:        "checkpoint",
			Source:      "test",
			Reliability: 88,
			LastSeen:    &now,
			CreatedAt:   now,
			UpdatedAt:   now,
		},
		{
			Name:        "京承高速进京检查站",
			Location:    "京承高速公路进京方向",
			Latitude:    40.1776,
			Longitude:   116.4297,
			Province:    "北京",
			City:        "北京",
			District:    "顺义区",
			Road:        "京承高速",
			Direction:   "进京方向",
			Status:      "unknown",
			Type:        "camera_checkpoint",
			Source:      "test",
			Reliability: 82,
			LastSeen:    &now,
			CreatedAt:   now,
			UpdatedAt:   now,
		},
	}

	for _, checkpoint := range testCheckpoints {
		err := checkpointRepo.CreateCheckpoint(checkpoint)
		require.NoError(t, err, "Failed to create test checkpoint: %s", checkpoint.Name)
	}
}

// testCheckpointList 测试检查站列表查询功能
func testCheckpointList(t *testing.T, r *gin.Engine, token string) {
	tests := []struct {
		name           string
		queryParams    string
		expectedStatus int
		expectError    bool
		minResults     int
	}{
		{
			name:           "获取所有检查站列表",
			queryParams:    "",
			expectedStatus: http.StatusOK,
			expectError:    false,
			minResults:     4,
		},
		{
			name:           "按省份筛选",
			queryParams:    "?province=北京",
			expectedStatus: http.StatusOK,
			expectError:    false,
			minResults:     4,
		},
		{
			name:           "按城市筛选",
			queryParams:    "?city=北京",
			expectedStatus: http.StatusOK,
			expectError:    false,
			minResults:     4,
		},
		{
			name:           "按区县筛选",
			queryParams:    "?district=昌平区",
			expectedStatus: http.StatusOK,
			expectError:    false,
			minResults:     1,
		},
		{
			name:           "按状态筛选-活跃",
			queryParams:    "?status=active",
			expectedStatus: http.StatusOK,
			expectError:    false,
			minResults:     2,
		},
		{
			name:           "按状态筛选-非活跃",
			queryParams:    "?status=inactive",
			expectedStatus: http.StatusOK,
			expectError:    false,
			minResults:     1,
		},
		{
			name:           "按类型筛选",
			queryParams:    "?type=checkpoint",
			expectedStatus: http.StatusOK,
			expectError:    false,
			minResults:     3,
		},
		{
			name:           "分页查询",
			queryParams:    "?page=1&page_size=2",
			expectedStatus: http.StatusOK,
			expectError:    false,
			minResults:     2,
		},
		{
			name:           "排序查询",
			queryParams:    "?sort_by=name&sort_order=asc",
			expectedStatus: http.StatusOK,
			expectError:    false,
			minResults:     4,
		},
		{
			name:           "无效状态参数",
			queryParams:    "?status=invalid",
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
			minResults:     0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, _ := http.NewRequest("GET", "/api/checkpoints"+tt.queryParams, nil)
			req.Header.Set("Authorization", "Bearer "+token)
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			if !tt.expectError {
				var response dto.CheckpointListResponse
				err := json.Unmarshal(w.Body.Bytes(), &response)
				require.NoError(t, err)

				assert.GreaterOrEqual(t, len(response.Data), tt.minResults)
				assert.Greater(t, response.Total, int64(0))
				assert.Greater(t, response.TotalPages, 0)

				// 验证返回数据结构
				if len(response.Data) > 0 {
					checkpoint := response.Data[0]
					assert.NotEmpty(t, checkpoint.Name)
					assert.NotEmpty(t, checkpoint.Location)
					assert.NotZero(t, checkpoint.Latitude)
					assert.NotZero(t, checkpoint.Longitude)
					assert.NotEmpty(t, checkpoint.Status)
				}
			}
		})
	}
}

// testCheckpointDetail 测试检查站详情查询功能
func testCheckpointDetail(t *testing.T, r *gin.Engine, token string) {
	tests := []struct {
		name           string
		checkpointID   string
		expectedStatus int
		expectError    bool
	}{
		{
			name:           "获取存在的检查站详情",
			checkpointID:   "1",
			expectedStatus: http.StatusOK,
			expectError:    false,
		},
		{
			name:           "获取不存在的检查站详情",
			checkpointID:   "999",
			expectedStatus: http.StatusNotFound,
			expectError:    true,
		},
		{
			name:           "无效的检查站ID",
			checkpointID:   "invalid",
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, _ := http.NewRequest("GET", "/api/checkpoints/"+tt.checkpointID, nil)
			req.Header.Set("Authorization", "Bearer "+token)
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			if !tt.expectError {
				var response dto.CheckpointResponse
				err := json.Unmarshal(w.Body.Bytes(), &response)
				require.NoError(t, err)

				assert.NotEmpty(t, response.Name)
				assert.NotEmpty(t, response.Location)
				assert.NotZero(t, response.Latitude)
				assert.NotZero(t, response.Longitude)
				assert.NotEmpty(t, response.Province)
				assert.NotEmpty(t, response.City)
				assert.NotEmpty(t, response.Status)
				assert.NotEmpty(t, response.Type)
			}
		})
	}
}

// testNearbyCheckpoints 测试附近检查站查询功能
func testNearbyCheckpoints(t *testing.T, r *gin.Engine, token string) {
	tests := []struct {
		name           string
		queryParams    string
		expectedStatus int
		expectError    bool
		minResults     int
	}{
		{
			name:           "查询北京市中心附近检查站",
			queryParams:    "?latitude=39.9042&longitude=116.4074&radius=50",
			expectedStatus: http.StatusOK,
			expectError:    false,
			minResults:     4,
		},
		{
			name:           "查询小范围附近检查站",
			queryParams:    "?latitude=40.0776&longitude=116.3297&radius=5",
			expectedStatus: http.StatusOK,
			expectError:    false,
			minResults:     1,
		},
		{
			name:           "按状态筛选附近检查站",
			queryParams:    "?latitude=39.9042&longitude=116.4074&radius=50&status=active",
			expectedStatus: http.StatusOK,
			expectError:    false,
			minResults:     2,
		},
		{
			name:           "按类型筛选附近检查站",
			queryParams:    "?latitude=39.9042&longitude=116.4074&radius=50&type=checkpoint",
			expectedStatus: http.StatusOK,
			expectError:    false,
			minResults:     3,
		},
		{
			name:           "限制返回数量",
			queryParams:    "?latitude=39.9042&longitude=116.4074&radius=50&limit=2",
			expectedStatus: http.StatusOK,
			expectError:    false,
			minResults:     2,
		},
		{
			name:           "缺少必需参数",
			queryParams:    "?radius=10",
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
			minResults:     0,
		},
		{
			name:           "无效的坐标参数",
			queryParams:    "?latitude=invalid&longitude=116.4074&radius=10",
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
			minResults:     0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, _ := http.NewRequest("GET", "/api/checkpoints/nearby"+tt.queryParams, nil)
			req.Header.Set("Authorization", "Bearer "+token)
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			if !tt.expectError {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				require.NoError(t, err)

				data, exists := response["data"]
				assert.True(t, exists)

				dataArray, ok := data.([]interface{})
				assert.True(t, ok)
				assert.GreaterOrEqual(t, len(dataArray), tt.minResults)

				// 验证返回数据包含距离信息
				if len(dataArray) > 0 {
					checkpoint := dataArray[0].(map[string]interface{})
					assert.Contains(t, checkpoint, "name")
					assert.Contains(t, checkpoint, "latitude")
					assert.Contains(t, checkpoint, "longitude")
					assert.Contains(t, checkpoint, "distance")
				}
			}
		})
	}
}

// testCheckpointFiltering 测试检查站搜索过滤功能
func testCheckpointFiltering(t *testing.T, r *gin.Engine, token string) {
	tests := []struct {
		name           string
		queryParams    string
		expectedStatus int
		expectedCount  int
		description    string
	}{
		{
			name:           "组合筛选-省份和状态",
			queryParams:    "?province=北京&status=active",
			expectedStatus: http.StatusOK,
			expectedCount:  2,
			description:    "筛选北京省活跃状态的检查站",
		},
		{
			name:           "组合筛选-城市和类型",
			queryParams:    "?city=北京&type=checkpoint",
			expectedStatus: http.StatusOK,
			expectedCount:  3,
			description:    "筛选北京市checkpoint类型的检查站",
		},
		{
			name:           "组合筛选-区县和状态",
			queryParams:    "?district=大兴区&status=active",
			expectedStatus: http.StatusOK,
			expectedCount:  1,
			description:    "筛选大兴区活跃状态的检查站",
		},
		{
			name:           "多条件组合筛选",
			queryParams:    "?province=北京&city=北京&status=active&type=checkpoint",
			expectedStatus: http.StatusOK,
			expectedCount:  2,
			description:    "多条件组合筛选",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, _ := http.NewRequest("GET", "/api/checkpoints"+tt.queryParams, nil)
			req.Header.Set("Authorization", "Bearer "+token)
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			var response dto.CheckpointListResponse
			err := json.Unmarshal(w.Body.Bytes(), &response)
			require.NoError(t, err)

			assert.Equal(t, tt.expectedCount, len(response.Data), tt.description)
		})
	}
}

// testCheckpointReporting 测试检查站报告功能
func testCheckpointReporting(t *testing.T, r *gin.Engine, token string) {
	tests := []struct {
		name           string
		requestBody    dto.CheckpointReportRequest
		expectedStatus int
		expectError    bool
	}{
		{
			name: "成功报告检查站状态",
			requestBody: dto.CheckpointReportRequest{
				CheckpointID: 1,
				Status:       "active",
				Description:  "检查站正常运行",
				Latitude:     40.0776,
				Longitude:    116.3297,
			},
			expectedStatus: http.StatusCreated,
			expectError:    false,
		},
		{
			name: "报告检查站状态变更",
			requestBody: dto.CheckpointReportRequest{
				CheckpointID: 2,
				Status:       "inactive",
				Description:  "检查站暂停检查",
				Latitude:     39.7284,
				Longitude:    116.2734,
			},
			expectedStatus: http.StatusCreated,
			expectError:    false,
		},
		{
			name: "报告不存在的检查站",
			requestBody: dto.CheckpointReportRequest{
				CheckpointID: 999,
				Status:       "active",
				Description:  "测试报告",
			},
			expectedStatus: http.StatusInternalServerError,
			expectError:    true,
		},
		{
			name: "无效的状态参数",
			requestBody: dto.CheckpointReportRequest{
				CheckpointID: 1,
				Status:       "invalid_status",
				Description:  "测试报告",
			},
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
		},
		{
			name: "缺少必需参数",
			requestBody: dto.CheckpointReportRequest{
				Status:      "active",
				Description: "测试报告",
			},
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			jsonData, _ := json.Marshal(tt.requestBody)
			req, _ := http.NewRequest("POST", "/api/checkpoints/report", bytes.NewBuffer(jsonData))
			req.Header.Set("Content-Type", "application/json")
			req.Header.Set("Authorization", "Bearer "+token)
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			if !tt.expectError {
				var response dto.CheckpointReportResponse
				err := json.Unmarshal(w.Body.Bytes(), &response)
				require.NoError(t, err)

				assert.Equal(t, tt.requestBody.CheckpointID, response.CheckpointID)
				assert.Equal(t, tt.requestBody.Status, response.Status)
				assert.Equal(t, tt.requestBody.Description, response.Description)
				assert.False(t, response.Verified) // 新报告默认未验证
			}
		})
	}

	// 测试获取检查站报告列表
	t.Run("获取检查站报告列表", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/api/checkpoints/1/reports", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		data, exists := response["data"]
		assert.True(t, exists)

		dataArray, ok := data.([]interface{})
		assert.True(t, ok)
		assert.GreaterOrEqual(t, len(dataArray), 1) // 至少有一个报告
	})
}
