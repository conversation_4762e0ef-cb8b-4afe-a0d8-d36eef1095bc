package test

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/azel-ko/final-ddd/internal/application/dto"
	"github.com/azel-ko/final-ddd/internal/infrastructure/cache"
	"github.com/azel-ko/final-ddd/internal/infrastructure/migration"
	"github.com/azel-ko/final-ddd/internal/infrastructure/persistence"
	"github.com/azel-ko/final-ddd/internal/interfaces/http/router"
	"github.com/azel-ko/final-ddd/internal/pkg/config"
	baseMigration "github.com/azel-ko/final-ddd/internal/pkg/database/migration"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestRouteInterface 测试路线规划接口的完整功能
func TestRouteInterface(t *testing.T) {
	// 设置测试环境
	cfg := &config.Config{
		App: config.App{
			Env: "test",
		},
		Database: config.DatabaseConfig{
			Type: "sqlite",
			Path: ":memory:",
		},
		JWT: config.JWTConfig{
			Key: "test-secret-key-for-jwt-signing",
		},
		Redis: config.RedisConfig{
			Host:     "localhost",
			Port:     6379,
			Password: "",
			DB:       1,
		},
		Amap: config.AmapConfig{
			APIKey:        "test-api-key",
			WebServiceKey: "test-web-service-key",
			BaseURL:       "https://restapi.amap.com/v3",
		},
	}

	// 初始化数据库
	repo, db, err := persistence.NewRepository(cfg)
	require.NoError(t, err, "Failed to initialize repository")

	// 运行数据库迁移
	migrator := baseMigration.NewMigrator(db)
	migration.RegisterMigrations(migrator)
	err = migrator.Run()
	require.NoError(t, err, "Failed to run database migrations")

	// 初始化Redis缓存
	redisAddr := fmt.Sprintf("%s:%d", cfg.Redis.Host, cfg.Redis.Port)
	redisCache := cache.NewRedisCache(redisAddr, cfg.Redis.Password)

	// 测试Redis连接
	ctx := context.Background()
	if err := redisCache.Ping(ctx); err != nil {
		t.Logf("Redis not available, some tests may be skipped: %v", err)
	}

	// 设置路由
	gin.SetMode(gin.TestMode)
	r := router.Setup(cfg, repo, redisCache)

	// 创建测试用户和token
	token := createRouteTestUserAndGetToken(t, r, "<EMAIL>", "路线测试用户")

	t.Run("路线规划接口测试", func(t *testing.T) {
		testRoutePlanning(t, r, token)
	})

	t.Run("路线历史记录接口测试", func(t *testing.T) {
		testRouteHistory(t, r, token)
	})

	t.Run("路线详情查询接口测试", func(t *testing.T) {
		testRouteDetail(t, r, token)
	})

	t.Run("路线优化接口测试", func(t *testing.T) {
		testRouteOptimization(t, r, token)
	})

	t.Run("路线分析接口测试", func(t *testing.T) {
		testRouteAnalysis(t, r, token)
	})
}

// createRouteTestUserAndGetToken 创建测试用户并获取token
func createRouteTestUserAndGetToken(t *testing.T, r *gin.Engine, email, name string) string {
	// 注册测试用户
	registerReq := dto.UserRequest{
		Name:     name,
		Email:    email,
		Password: "password123",
	}
	jsonData, _ := json.Marshal(registerReq)
	req, _ := http.NewRequest("POST", "/api/auth/register", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusCreated, w.Code)

	// 登录获取token
	loginReq := dto.LoginRequest{
		Email:    email,
		Password: "password123",
		Platform: "web",
	}
	jsonData, _ = json.Marshal(loginReq)
	req, _ = http.NewRequest("POST", "/api/auth/login", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusOK, w.Code)

	var loginResponse dto.LoginResponse
	err := json.Unmarshal(w.Body.Bytes(), &loginResponse)
	require.NoError(t, err)
	return loginResponse.Token
}

// testRoutePlanning 测试路线规划功能
func testRoutePlanning(t *testing.T, r *gin.Engine, token string) {
	tests := []struct {
		name           string
		requestBody    dto.RouteRequest
		expectedStatus int
		expectError    bool
		description    string
	}{
		{
			name: "成功规划基础路线",
			requestBody: dto.RouteRequest{
				StartLat:     39.9042,
				StartLng:     116.4074,
				EndLat:       40.0776,
				EndLng:       116.3297,
				StartAddress: "北京市天安门广场",
				EndAddress:   "北京市昌平区",
				AvoidLevel:   1,
				Strategy:     "fastest",
			},
			expectedStatus: http.StatusOK,
			expectError:    false,
			description:    "基础路线规划功能",
		},
		{
			name: "规划避让检查站路线",
			requestBody: dto.RouteRequest{
				StartLat:     39.9042,
				StartLng:     116.4074,
				EndLat:       40.0776,
				EndLng:       116.3297,
				StartAddress: "北京市天安门广场",
				EndAddress:   "北京市昌平区",
				AvoidLevel:   3,
				Strategy:     "avoid_checkpoints",
			},
			expectedStatus: http.StatusOK,
			expectError:    false,
			description:    "高级别避让检查站路线",
		},
		{
			name: "规划最短距离路线",
			requestBody: dto.RouteRequest{
				StartLat:     39.9042,
				StartLng:     116.4074,
				EndLat:       39.7284,
				EndLng:       116.2734,
				StartAddress: "北京市天安门广场",
				EndAddress:   "北京市大兴区",
				AvoidLevel:   2,
				Strategy:     "shortest",
			},
			expectedStatus: http.StatusOK,
			expectError:    false,
			description:    "最短距离路线规划",
		},
		{
			name: "缺少必需参数",
			requestBody: dto.RouteRequest{
				StartLat: 39.9042,
				StartLng: 116.4074,
				// 缺少终点坐标
				AvoidLevel: 1,
				Strategy:   "fastest",
			},
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
			description:    "参数验证测试",
		},
		{
			name: "无效的避让级别",
			requestBody: dto.RouteRequest{
				StartLat:   39.9042,
				StartLng:   116.4074,
				EndLat:     40.0776,
				EndLng:     116.3297,
				AvoidLevel: 5, // 超出范围
				Strategy:   "fastest",
			},
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
			description:    "避让级别验证",
		},
		{
			name: "无效的策略参数",
			requestBody: dto.RouteRequest{
				StartLat:   39.9042,
				StartLng:   116.4074,
				EndLat:     40.0776,
				EndLng:     116.3297,
				AvoidLevel: 1,
				Strategy:   "invalid_strategy",
			},
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
			description:    "策略参数验证",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			jsonData, _ := json.Marshal(tt.requestBody)
			req, _ := http.NewRequest("POST", "/api/routes/plan", bytes.NewBuffer(jsonData))
			req.Header.Set("Content-Type", "application/json")
			req.Header.Set("Authorization", "Bearer "+token)
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code, tt.description)

			if !tt.expectError {
				var response dto.RouteResponse
				err := json.Unmarshal(w.Body.Bytes(), &response)
				require.NoError(t, err)

				// 验证响应数据结构
				assert.Equal(t, tt.requestBody.StartLat, response.StartLat)
				assert.Equal(t, tt.requestBody.StartLng, response.StartLng)
				assert.Equal(t, tt.requestBody.EndLat, response.EndLat)
				assert.Equal(t, tt.requestBody.EndLng, response.EndLng)
				assert.Equal(t, tt.requestBody.AvoidLevel, response.AvoidLevel)
				assert.GreaterOrEqual(t, response.Distance, 0)
				assert.GreaterOrEqual(t, response.Duration, 0)
				assert.GreaterOrEqual(t, response.RiskScore, 0)
				assert.LessOrEqual(t, response.RiskScore, 100)
			}
		})
	}
}

// testRouteHistory 测试路线历史记录功能
func testRouteHistory(t *testing.T, r *gin.Engine, token string) {
	// 先创建一些路线记录
	routeReq := dto.RouteRequest{
		StartLat:     39.9042,
		StartLng:     116.4074,
		EndLat:       40.0776,
		EndLng:       116.3297,
		StartAddress: "测试起点",
		EndAddress:   "测试终点",
		AvoidLevel:   1,
		Strategy:     "fastest",
	}

	// 创建几条路线记录
	for i := 0; i < 3; i++ {
		jsonData, _ := json.Marshal(routeReq)
		req, _ := http.NewRequest("POST", "/api/routes/plan", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)
		// 不检查结果，只是为了创建历史记录
	}

	tests := []struct {
		name           string
		queryParams    string
		expectedStatus int
		expectError    bool
		description    string
	}{
		{
			name:           "获取默认历史记录",
			queryParams:    "",
			expectedStatus: http.StatusOK,
			expectError:    false,
			description:    "默认分页获取历史记录",
		},
		{
			name:           "分页查询历史记录",
			queryParams:    "?page=1&page_size=2",
			expectedStatus: http.StatusOK,
			expectError:    false,
			description:    "分页参数测试",
		},
		{
			name:           "按创建时间排序",
			queryParams:    "?sort_by=created_at&sort_order=desc",
			expectedStatus: http.StatusOK,
			expectError:    false,
			description:    "排序功能测试",
		},
		{
			name:           "按距离排序",
			queryParams:    "?sort_by=distance&sort_order=asc",
			expectedStatus: http.StatusOK,
			expectError:    false,
			description:    "按距离排序",
		},
		{
			name:           "无效的排序字段",
			queryParams:    "?sort_by=invalid_field",
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
			description:    "排序字段验证",
		},
		{
			name:           "无效的页码",
			queryParams:    "?page=0",
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
			description:    "页码验证",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, _ := http.NewRequest("GET", "/api/routes/history"+tt.queryParams, nil)
			req.Header.Set("Authorization", "Bearer "+token)
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code, tt.description)

			if !tt.expectError {
				var response dto.RouteHistoryResponse
				err := json.Unmarshal(w.Body.Bytes(), &response)
				require.NoError(t, err)

				// 验证响应结构
				assert.GreaterOrEqual(t, len(response.Data), 0)
				assert.GreaterOrEqual(t, response.Total, int64(0))
				assert.Greater(t, response.Page, 0)
				assert.Greater(t, response.PageSize, 0)
				assert.GreaterOrEqual(t, response.TotalPages, 0)
			}
		})
	}
}

// testRouteDetail 测试路线详情查询功能
func testRouteDetail(t *testing.T, r *gin.Engine, token string) {
	tests := []struct {
		name           string
		routeID        string
		expectedStatus int
		expectError    bool
		description    string
	}{
		{
			name:           "获取存在的路线详情",
			routeID:        "1",
			expectedStatus: http.StatusOK,
			expectError:    false,
			description:    "正常路线详情查询",
		},
		{
			name:           "获取不存在的路线详情",
			routeID:        "999",
			expectedStatus: http.StatusOK, // 当前实现返回占位符响应
			expectError:    false,
			description:    "不存在路线的处理",
		},
		{
			name:           "无效的路线ID",
			routeID:        "invalid",
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
			description:    "路线ID格式验证",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, _ := http.NewRequest("GET", "/api/routes/"+tt.routeID, nil)
			req.Header.Set("Authorization", "Bearer "+token)
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code, tt.description)

			if !tt.expectError {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				require.NoError(t, err)

				// 验证响应包含基本字段
				assert.Contains(t, response, "id")
				assert.Contains(t, response, "user_id")
			}
		})
	}
}

// testRouteOptimization 测试路线优化功能
func testRouteOptimization(t *testing.T, r *gin.Engine, token string) {
	tests := []struct {
		name           string
		requestBody    dto.RouteOptimizeRequest
		expectedStatus int
		expectError    bool
		description    string
	}{
		{
			name: "优化现有路线",
			requestBody: dto.RouteOptimizeRequest{
				RouteID:    1,
				AvoidLevel: 3,
			},
			expectedStatus: http.StatusPaymentRequired, // 需要高级订阅
			expectError:    true,
			description:    "路线优化需要高级权限",
		},
		{
			name: "优化不存在的路线",
			requestBody: dto.RouteOptimizeRequest{
				RouteID:    999,
				AvoidLevel: 2,
			},
			expectedStatus: http.StatusPaymentRequired, // 权限检查在前
			expectError:    true,
			description:    "不存在路线的优化请求",
		},
		{
			name: "无效的避让级别",
			requestBody: dto.RouteOptimizeRequest{
				RouteID:    1,
				AvoidLevel: 5, // 超出范围
			},
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
			description:    "避让级别参数验证",
		},
		{
			name: "缺少路线ID",
			requestBody: dto.RouteOptimizeRequest{
				AvoidLevel: 2,
			},
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
			description:    "必需参数验证",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			jsonData, _ := json.Marshal(tt.requestBody)
			req, _ := http.NewRequest("POST", "/api/routes/optimize", bytes.NewBuffer(jsonData))
			req.Header.Set("Content-Type", "application/json")
			req.Header.Set("Authorization", "Bearer "+token)
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code, tt.description)

			if tt.expectError {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				require.NoError(t, err)
				assert.Contains(t, response, "error")
			}
		})
	}
}

// testRouteAnalysis 测试路线分析功能
func testRouteAnalysis(t *testing.T, r *gin.Engine, token string) {
	tests := []struct {
		name           string
		routeID        string
		expectedStatus int
		expectError    bool
		description    string
	}{
		{
			name:           "分析现有路线",
			routeID:        "1",
			expectedStatus: http.StatusPaymentRequired, // 需要高级订阅
			expectError:    true,
			description:    "路线分析需要高级权限",
		},
		{
			name:           "分析不存在的路线",
			routeID:        "999",
			expectedStatus: http.StatusPaymentRequired, // 权限检查在前
			expectError:    true,
			description:    "不存在路线的分析请求",
		},
		{
			name:           "无效的路线ID",
			routeID:        "invalid",
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
			description:    "路线ID格式验证",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, _ := http.NewRequest("GET", "/api/routes/"+tt.routeID+"/analyze", nil)
			req.Header.Set("Authorization", "Bearer "+token)
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code, tt.description)

			if tt.expectError {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				require.NoError(t, err)
				assert.Contains(t, response, "error")
			}
		})
	}
}

// testRoutePermissions 测试路线功能权限控制
func testRoutePermissions(t *testing.T, r *gin.Engine) {
	// 测试无token访问
	t.Run("无token访问路线规划", func(t *testing.T) {
		routeReq := dto.RouteRequest{
			StartLat:   39.9042,
			StartLng:   116.4074,
			EndLat:     40.0776,
			EndLng:     116.3297,
			AvoidLevel: 1,
			Strategy:   "fastest",
		}
		jsonData, _ := json.Marshal(routeReq)
		req, _ := http.NewRequest("POST", "/api/routes/plan", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})

	// 测试无效token访问
	t.Run("无效token访问路线规划", func(t *testing.T) {
		routeReq := dto.RouteRequest{
			StartLat:   39.9042,
			StartLng:   116.4074,
			EndLat:     40.0776,
			EndLng:     116.3297,
			AvoidLevel: 1,
			Strategy:   "fastest",
		}
		jsonData, _ := json.Marshal(routeReq)
		req, _ := http.NewRequest("POST", "/api/routes/plan", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer invalid-token")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}
