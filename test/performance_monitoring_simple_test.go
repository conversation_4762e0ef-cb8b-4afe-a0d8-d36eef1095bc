package test

import (
	"context"
	"testing"
	"time"

	"github.com/azel-ko/final-ddd/internal/infrastructure/persistence"
	"github.com/azel-ko/final-ddd/internal/pkg/logger"
	"github.com/stretchr/testify/assert"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

func TestPerformanceMonitoringSimple(t *testing.T) {
	// 初始化日志器
	logger.Init("info")

	// 使用内存SQLite数据库进行测试
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	assert.NoError(t, err)

	ctx := context.Background()

	t.Run("测试数据库索引优化器", func(t *testing.T) {
		// 创建索引优化器
		indexOptimizer := persistence.NewDatabaseIndexOptimizer(db)
		assert.NotNil(t, indexOptimizer)

		// 测试获取优化建议
		suggestions, err := indexOptimizer.GetOptimizationSuggestions(ctx)
		assert.NoError(t, err)
		assert.NotNil(t, suggestions)
		assert.NotNil(t, suggestions.OptimizationTips)
		assert.Greater(t, len(suggestions.OptimizationTips), 0)

		// 验证包含基本的优化建议
		found := false
		for _, tip := range suggestions.OptimizationTips {
			if tip == "使用LIMIT限制查询结果数量" {
				found = true
				break
			}
		}
		assert.True(t, found, "应该包含基本的优化建议")
	})

	t.Run("测试连接池优化器", func(t *testing.T) {
		// 创建连接池优化器
		poolOptimizer := persistence.NewConnectionPoolOptimizer(db)
		assert.NotNil(t, poolOptimizer)

		// 测试优化连接池
		err := poolOptimizer.OptimizeConnectionPool()
		assert.NoError(t, err)

		// 获取连接池统计
		stats := poolOptimizer.GetConnectionPoolStats()
		assert.NotNil(t, stats)
		assert.Contains(t, stats, "max_open_connections")
		assert.Contains(t, stats, "open_connections")
		assert.Contains(t, stats, "in_use")
		assert.Contains(t, stats, "idle")
	})

	t.Run("测试性能监控器", func(t *testing.T) {
		// 创建性能监控器
		perfMonitor := persistence.NewPerformanceMonitor()
		assert.NotNil(t, perfMonitor)

		// 记录查询
		query := persistence.OptimizedQuery{
			SQL:      "SELECT * FROM users",
			Args:     []interface{}{},
			Duration: 100 * time.Millisecond,
			Rows:     10,
		}
		perfMonitor.RecordQuery(query)

		// 获取统计信息
		stats := perfMonitor.GetStats()
		assert.Equal(t, int64(1), stats.TotalQueries)
		assert.Equal(t, 100*time.Millisecond, stats.AverageTime)
		assert.Equal(t, 100*time.Millisecond, stats.MaxTime)
		assert.Equal(t, 100*time.Millisecond, stats.MinTime)

		// 记录慢查询
		slowQuery := persistence.OptimizedQuery{
			SQL:      "SELECT * FROM large_table",
			Args:     []interface{}{},
			Duration: 2 * time.Second,
			Rows:     1000,
		}
		perfMonitor.RecordQuery(slowQuery)

		// 验证慢查询统计
		updatedStats := perfMonitor.GetStats()
		assert.Equal(t, int64(2), updatedStats.TotalQueries)
		assert.Equal(t, int64(1), updatedStats.SlowQueries)
		assert.Equal(t, 2*time.Second, updatedStats.MaxTime)
		assert.Equal(t, 100*time.Millisecond, updatedStats.MinTime)

		// 获取慢查询列表
		slowQueries := perfMonitor.GetSlowQueries(1 * time.Second)
		assert.Len(t, slowQueries, 1)
		assert.Equal(t, "SELECT * FROM large_table", slowQueries[0].SQL)
		assert.Equal(t, 2*time.Second, slowQueries[0].Duration)
	})

	t.Run("测试查询缓存管理器", func(t *testing.T) {
		// 创建查询缓存管理器
		cacheManager := persistence.NewQueryCacheManager()
		assert.NotNil(t, cacheManager)

		// 测试缓存查询结果
		key := "test_query"
		result := map[string]interface{}{"count": 100}
		cacheManager.CacheQuery(key, result)

		// 获取缓存的查询结果
		cachedResult, exists := cacheManager.GetCachedQuery(key)
		assert.True(t, exists)
		assert.Equal(t, result, cachedResult)

		// 测试不存在的缓存
		_, exists = cacheManager.GetCachedQuery("non_existent_key")
		assert.False(t, exists)

		// 清空缓存
		cacheManager.ClearCache()
		_, exists = cacheManager.GetCachedQuery(key)
		assert.False(t, exists)
	})

	t.Run("测试查询优化器", func(t *testing.T) {
		// 创建查询优化器
		queryOptimizer := persistence.NewQueryOptimizer(db)
		assert.NotNil(t, queryOptimizer)

		// 测试查询优化器的基本功能
		// 这里只是验证对象可以正常创建
	})
}

func TestPerformanceThresholds(t *testing.T) {
	t.Run("测试性能阈值", func(t *testing.T) {
		// 定义性能阈值
		slowRequestThreshold := 2 * time.Second
		largeResponseThreshold := 1024 * 1024 // 1MB

		// 测试慢请求检测
		normalDuration := 100 * time.Millisecond
		slowDuration := 3 * time.Second

		assert.True(t, normalDuration < slowRequestThreshold)
		assert.True(t, slowDuration > slowRequestThreshold)

		// 测试大响应检测
		normalSize := 1024           // 1KB
		largeSize := 2 * 1024 * 1024 // 2MB

		assert.True(t, normalSize < largeResponseThreshold)
		assert.True(t, largeSize > largeResponseThreshold)
	})

	t.Run("测试性能指标计算", func(t *testing.T) {
		// 创建性能监控器
		perfMonitor := persistence.NewPerformanceMonitor()

		// 记录多个查询
		queries := []persistence.OptimizedQuery{
			{SQL: "SELECT 1", Duration: 50 * time.Millisecond, Rows: 1},
			{SQL: "SELECT 2", Duration: 100 * time.Millisecond, Rows: 1},
			{SQL: "SELECT 3", Duration: 150 * time.Millisecond, Rows: 1},
			{SQL: "SELECT 4", Duration: 2 * time.Second, Rows: 1}, // 慢查询
		}

		for _, query := range queries {
			perfMonitor.RecordQuery(query)
		}

		stats := perfMonitor.GetStats()
		assert.Equal(t, int64(4), stats.TotalQueries)
		assert.Equal(t, int64(1), stats.SlowQueries) // 只有一个慢查询
		assert.Equal(t, 2*time.Second, stats.MaxTime)
		assert.Equal(t, 50*time.Millisecond, stats.MinTime)

		// 验证平均时间计算
		expectedAvg := (50*time.Millisecond + 100*time.Millisecond + 150*time.Millisecond + 2*time.Second) / 4
		assert.Equal(t, expectedAvg, stats.AverageTime)
	})
}

func BenchmarkPerformanceMonitoring(b *testing.B) {
	b.Run("性能监控记录", func(b *testing.B) {
		perfMonitor := persistence.NewPerformanceMonitor()

		query := persistence.OptimizedQuery{
			SQL:      "SELECT * FROM test_table",
			Args:     []interface{}{},
			Duration: 50 * time.Millisecond,
			Rows:     1,
		}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			perfMonitor.RecordQuery(query)
		}
	})

	b.Run("查询缓存操作", func(b *testing.B) {
		cacheManager := persistence.NewQueryCacheManager()
		result := map[string]interface{}{"count": 100}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			key := "test_query_" + string(rune(i%100))
			cacheManager.CacheQuery(key, result)
			_, _ = cacheManager.GetCachedQuery(key)
		}
	})

	b.Run("连接池统计获取", func(b *testing.B) {
		db, _ := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
		poolOptimizer := persistence.NewConnectionPoolOptimizer(db)

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = poolOptimizer.GetConnectionPoolStats()
		}
	})
}

func TestOptimizationFeatures(t *testing.T) {
	// 使用内存SQLite数据库进行测试
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	assert.NoError(t, err)

	ctx := context.Background()

	t.Run("测试数据库性能分析", func(t *testing.T) {
		indexOptimizer := persistence.NewDatabaseIndexOptimizer(db)

		// 分析查询性能
		stats, err := indexOptimizer.AnalyzeQueryPerformance(ctx)
		assert.NoError(t, err)
		assert.NotNil(t, stats)

		// SQLite的性能分析功能有限，但应该不会出错
		assert.GreaterOrEqual(t, stats.TotalQueries, int64(0))
		assert.GreaterOrEqual(t, stats.SlowQueries, int64(0))
	})

	t.Run("测试表统计优化", func(t *testing.T) {
		indexOptimizer := persistence.NewDatabaseIndexOptimizer(db)

		// 优化表统计信息
		err := indexOptimizer.OptimizeTableStatistics(ctx)
		assert.NoError(t, err)
	})

	t.Run("测试缺失索引检查", func(t *testing.T) {
		indexOptimizer := persistence.NewDatabaseIndexOptimizer(db)

		suggestions, err := indexOptimizer.GetOptimizationSuggestions(ctx)
		assert.NoError(t, err)
		assert.NotNil(t, suggestions)

		// 验证优化建议包含预期内容
		assert.Contains(t, suggestions.OptimizationTips, "使用LIMIT限制查询结果数量")
		assert.Contains(t, suggestions.OptimizationTips, "避免SELECT *，只查询需要的字段")
		assert.Contains(t, suggestions.OptimizationTips, "使用适当的WHERE条件过滤数据")
	})
}
