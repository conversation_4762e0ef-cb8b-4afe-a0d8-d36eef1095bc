package test

import (
	"context"
	"testing"
	"time"

	"github.com/azel-ko/final-ddd/internal/infrastructure/amap"
	"github.com/azel-ko/final-ddd/internal/pkg/config"
	"github.com/azel-ko/final-ddd/internal/pkg/logger"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// 测试用的坐标点
var (
	// 北京市内的测试点
	tianAnMen    = "116.481028,39.989643" // 天安门
	birdNest     = "116.587922,40.081577" // 鸟巢
	wangJing     = "116.480639,39.996356" // 望京SOHO
	zhongGuanCun = "116.298904,39.979808" // 中关村
	sanlitun     = "116.455399,39.937967" // 三里屯

	// 进京检查站附近的测试点
	yuFaCheckpoint    = "116.051944,39.522222" // 榆垡检查站附近
	liuLiHeCheckpoint = "116.112778,39.728889" // 六里河检查站附近
)

func setupAmapClient() *amap.Client {
	// 初始化日志
	logger.Init("info")

	// 创建高德地图配置
	amapConfig := &config.AmapConfig{
		APIKey:        "9e471ba01ae18f216cd0fb84032ec7e2",
		WebServiceKey: "9e471ba01ae18f216cd0fb84032ec7e2",
		BaseURL:       "https://restapi.amap.com",
		Timeout:       "30s",
		RateLimit: config.RateLimitConfig{
			RequestsPerSecond: 10,
			Burst:             20,
		},
	}

	return amap.NewClient(amapConfig)
}

// TestBasicRouteRequest 测试基础路线规划
func TestBasicRouteRequest(t *testing.T) {
	client := setupAmapClient()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 测试天安门到鸟巢的路线
	req := &amap.DirectionRequest{
		Origin:      tianAnMen,
		Destination: birdNest,
		Strategy:    amap.StrategyFastest,
		Extensions:  "all", // 获取详细信息
	}

	resp, err := client.Direction(ctx, req)
	require.NoError(t, err, "基础路线规划请求应该成功")
	require.NotNil(t, resp, "响应不应该为空")
	require.Greater(t, len(resp.Route.Paths), 0, "应该返回至少一条路线")

	// 验证路线信息
	path := resp.Route.Paths[0]
	distance, err := path.GetDistanceInt()
	require.NoError(t, err, "距离解析应该成功")
	assert.Greater(t, distance, 0, "距离应该大于0")

	duration, err := path.GetDurationInt()
	require.NoError(t, err, "时间解析应该成功")
	assert.Greater(t, duration, 0, "时间应该大于0")

	t.Logf("基础路线规划成功: 距离=%d米, 时间=%d秒", distance, duration)
}

// TestMultipleStrategies 测试不同的路线策略
func TestMultipleStrategies(t *testing.T) {
	client := setupAmapClient()
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	strategies := []struct {
		name     string
		strategy int
	}{
		{"速度优先", amap.StrategyFastest},
		{"费用优先", amap.StrategyNoHighway},
		{"距离优先", amap.StrategyDistance},
		{"躲避拥堵", amap.StrategyAvoidJam},
	}

	for _, s := range strategies {
		t.Run(s.name, func(t *testing.T) {
			req := &amap.DirectionRequest{
				Origin:      zhongGuanCun,
				Destination: sanlitun,
				Strategy:    s.strategy,
				Extensions:  "base",
			}

			resp, err := client.Direction(ctx, req)
			require.NoError(t, err, "路线规划请求应该成功")
			require.Greater(t, len(resp.Route.Paths), 0, "应该返回至少一条路线")

			path := resp.Route.Paths[0]
			distance, _ := path.GetDistanceInt()
			duration, _ := path.GetDurationInt()

			t.Logf("%s策略: 距离=%d米, 时间=%d秒", s.name, distance, duration)
		})
	}
}

// TestWaypointsRouting 测试途经点路线规划
func TestWaypointsRouting(t *testing.T) {
	client := setupAmapClient()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 测试从天安门到鸟巢，途经望京SOHO
	req := &amap.DirectionRequest{
		Origin:      tianAnMen,
		Destination: birdNest,
		Waypoints:   wangJing, // 途经点
		Strategy:    amap.StrategyFastest,
		Extensions:  "base",
	}

	resp, err := client.Direction(ctx, req)
	require.NoError(t, err, "途经点路线规划请求应该成功")
	require.Greater(t, len(resp.Route.Paths), 0, "应该返回至少一条路线")

	path := resp.Route.Paths[0]
	distance, _ := path.GetDistanceInt()
	duration, _ := path.GetDurationInt()

	t.Logf("途经点路线规划成功: 距离=%d米, 时间=%d秒", distance, duration)

	// 验证途经点路线比直达路线更长
	directReq := &amap.DirectionRequest{
		Origin:      tianAnMen,
		Destination: birdNest,
		Strategy:    amap.StrategyFastest,
		Extensions:  "base",
	}

	directResp, err := client.Direction(ctx, directReq)
	require.NoError(t, err, "直达路线规划请求应该成功")

	directPath := directResp.Route.Paths[0]
	directDistance, _ := directPath.GetDistanceInt()

	assert.Greater(t, distance, directDistance, "途经点路线应该比直达路线更长")
	t.Logf("直达路线距离: %d米, 途经点路线距离: %d米", directDistance, distance)
}

// TestMultipleWaypoints 测试多个途经点
func TestMultipleWaypoints(t *testing.T) {
	client := setupAmapClient()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 测试多个途经点：天安门 -> 中关村 -> 三里屯 -> 鸟巢
	waypoints := zhongGuanCun + ";" + sanlitun
	req := &amap.DirectionRequest{
		Origin:      tianAnMen,
		Destination: birdNest,
		Waypoints:   waypoints,
		Strategy:    amap.StrategyFastest,
		Extensions:  "base",
	}

	resp, err := client.Direction(ctx, req)
	require.NoError(t, err, "多途经点路线规划请求应该成功")
	require.Greater(t, len(resp.Route.Paths), 0, "应该返回至少一条路线")

	path := resp.Route.Paths[0]
	distance, _ := path.GetDistanceInt()
	duration, _ := path.GetDurationInt()

	t.Logf("多途经点路线规划成功: 距离=%d米, 时间=%d秒", distance, duration)
}

// TestAvoidanceFeatures 测试避让功能
func TestAvoidanceFeatures(t *testing.T) {
	client := setupAmapClient()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 测试避让收费路段
	req := &amap.DirectionRequest{
		Origin:      tianAnMen,
		Destination: birdNest,
		Strategy:    amap.StrategyNoHighway, // 不走收费路段
		Extensions:  "base",
	}

	resp, err := client.Direction(ctx, req)
	require.NoError(t, err, "避让收费路段请求应该成功")
	require.Greater(t, len(resp.Route.Paths), 0, "应该返回至少一条路线")

	path := resp.Route.Paths[0]
	distance, _ := path.GetDistanceInt()
	duration, _ := path.GetDurationInt()

	t.Logf("避让收费路段: 距离=%d米, 时间=%d秒, 收费=%s元", distance, duration, path.Tolls)
}

// TestLongDistanceRoute 测试长距离路线规划
func TestLongDistanceRoute(t *testing.T) {
	client := setupAmapClient()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 测试从市中心到进京检查站附近的长距离路线
	req := &amap.DirectionRequest{
		Origin:      tianAnMen,
		Destination: yuFaCheckpoint, // 榆垡检查站附近
		Strategy:    amap.StrategyFastest,
		Extensions:  "base",
	}

	resp, err := client.Direction(ctx, req)
	require.NoError(t, err, "长距离路线规划请求应该成功")
	require.Greater(t, len(resp.Route.Paths), 0, "应该返回至少一条路线")

	path := resp.Route.Paths[0]
	distance, _ := path.GetDistanceInt()
	duration, _ := path.GetDurationInt()

	// 验证这是一条长距离路线
	assert.Greater(t, distance, 30000, "到检查站的距离应该超过30公里")
	assert.Greater(t, duration, 1800, "到检查站的时间应该超过30分钟")

	t.Logf("长距离路线规划成功: 距离=%d米, 时间=%d秒", distance, duration)
}

// TestRouteDetails 测试路线详细信息
func TestRouteDetails(t *testing.T) {
	client := setupAmapClient()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	req := &amap.DirectionRequest{
		Origin:      tianAnMen,
		Destination: wangJing,
		Strategy:    amap.StrategyFastest,
		Extensions:  "all", // 获取详细信息
	}

	resp, err := client.Direction(ctx, req)
	require.NoError(t, err, "详细路线信息请求应该成功")
	require.Greater(t, len(resp.Route.Paths), 0, "应该返回至少一条路线")

	path := resp.Route.Paths[0]

	// 验证路线包含步骤信息
	assert.Greater(t, len(path.Steps), 0, "路线应该包含导航步骤")

	// 检查每个步骤的信息
	for i, step := range path.Steps {
		assert.NotEmpty(t, step.Instruction, "步骤%d应该有导航指示", i)
		// 道路名称可能为空，这是正常的
		assert.NotEmpty(t, step.Distance, "步骤%d应该有距离信息", i)
		assert.NotEmpty(t, step.Duration, "步骤%d应该有时间信息", i)

		if i < 3 { // 只打印前3个步骤
			t.Logf("步骤%d: %s, 道路: %s, 距离: %s米, 时间: %s秒",
				i+1, step.Instruction, step.Road, step.Distance, step.Duration)
		}
	}

	t.Logf("路线包含 %d 个导航步骤", len(path.Steps))
}

// TestErrorHandling 测试错误处理
func TestErrorHandling(t *testing.T) {
	client := setupAmapClient()
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 测试无效坐标
	req := &amap.DirectionRequest{
		Origin:      "invalid,coordinates",
		Destination: tianAnMen,
		Strategy:    amap.StrategyFastest,
		Extensions:  "base",
	}

	_, err := client.Direction(ctx, req)
	assert.Error(t, err, "无效坐标应该返回错误")
	t.Logf("无效坐标错误处理正确: %v", err)

	// 测试空坐标
	req2 := &amap.DirectionRequest{
		Origin:      "",
		Destination: tianAnMen,
		Strategy:    amap.StrategyFastest,
		Extensions:  "base",
	}

	_, err2 := client.Direction(ctx, req2)
	assert.Error(t, err2, "空坐标应该返回错误")
	t.Logf("空坐标错误处理正确: %v", err2)
}

// TestRateLimiting 测试限流处理
func TestRateLimiting(t *testing.T) {
	client := setupAmapClient()
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	// 快速发送多个请求测试限流
	successCount := 0
	for i := 0; i < 5; i++ {
		req := &amap.DirectionRequest{
			Origin:      tianAnMen,
			Destination: birdNest,
			Strategy:    amap.StrategyFastest,
			Extensions:  "base",
		}

		_, err := client.Direction(ctx, req)
		if err == nil {
			successCount++
		}

		// 短暂延迟
		time.Sleep(100 * time.Millisecond)
	}

	assert.Greater(t, successCount, 0, "至少应该有一些请求成功")
	t.Logf("限流测试: %d/5 个请求成功", successCount)
}
