package test

import (
	"context"
	"testing"
	"time"

	"github.com/azel-ko/final-ddd/internal/infrastructure/amap"
	"github.com/azel-ko/final-ddd/internal/pkg/config"
	"github.com/azel-ko/final-ddd/internal/pkg/logger"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// 测试用的地址和坐标
var (
	// 北京知名地址
	addresses = map[string]string{
		"天安门":    "北京市东城区天安门广场",
		"鸟巢":     "北京市朝阳区国家体育场",
		"望京SOHO": "北京市朝阳区望京SOHO",
		"中关村":    "北京市海淀区中关村大街",
		"三里屯":    "北京市朝阳区三里屯",
		"首都机场":   "北京首都国际机场",
		"北京站":    "北京市东城区北京站",
		"清华大学":   "北京市海淀区清华大学",
	}

	// 对应的坐标（用于逆地理编码测试）
	coordinates = map[string]string{
		"天安门":  "116.397128,39.916527",
		"鸟巢":   "116.388374,39.993175",
		"望京":   "116.480639,39.996356",
		"中关村":  "116.298904,39.979808",
		"三里屯":  "116.455399,39.937967",
		"首都机场": "116.584556,40.080111",
		"北京西站": "116.322056,39.893611",
		"清华大学": "116.326393,40.003611",
	}
)

func setupGeocodingClient() *amap.Client {
	// 初始化日志
	logger.Init("info")

	// 创建高德地图配置
	amapConfig := &config.AmapConfig{
		APIKey:        "9e471ba01ae18f216cd0fb84032ec7e2",
		WebServiceKey: "9e471ba01ae18f216cd0fb84032ec7e2",
		BaseURL:       "https://restapi.amap.com",
		Timeout:       "30s",
		RateLimit: config.RateLimitConfig{
			RequestsPerSecond: 10,
			Burst:             20,
		},
	}

	return amap.NewClient(amapConfig)
}

// TestBasicGeocoding 测试基础地理编码功能
func TestBasicGeocoding(t *testing.T) {
	client := setupGeocodingClient()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 测试天安门地址转坐标
	req := &amap.GeocodeRequest{
		Address: addresses["天安门"],
		City:    "北京",
	}

	resp, err := client.Geocode(ctx, req)
	require.NoError(t, err, "地理编码请求应该成功")
	require.NotNil(t, resp, "响应不应该为空")
	require.Greater(t, len(resp.Geocodes), 0, "应该返回至少一个地理编码结果")

	// 验证结果
	geocode := resp.Geocodes[0]
	lat, lng, err := geocode.GetLocation()
	require.NoError(t, err, "坐标解析应该成功")

	// 验证坐标在北京范围内
	assert.Greater(t, lat, 39.0, "纬度应该在北京范围内")
	assert.Less(t, lat, 41.0, "纬度应该在北京范围内")
	assert.Greater(t, lng, 115.0, "经度应该在北京范围内")
	assert.Less(t, lng, 118.0, "经度应该在北京范围内")

	t.Logf("地理编码成功: %s -> (%.6f, %.6f)", geocode.FormattedAddress, lat, lng)
}

// TestMultipleAddresses 测试多个地址的地理编码
func TestMultipleAddresses(t *testing.T) {
	client := setupGeocodingClient()
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	for name, address := range addresses {
		t.Run(name, func(t *testing.T) {
			req := &amap.GeocodeRequest{
				Address: address,
				City:    "北京",
			}

			resp, err := client.Geocode(ctx, req)
			require.NoError(t, err, "地理编码请求应该成功")
			require.Greater(t, len(resp.Geocodes), 0, "应该返回至少一个结果")

			geocode := resp.Geocodes[0]
			lat, lng, err := geocode.GetLocation()
			require.NoError(t, err, "坐标解析应该成功")

			// 验证坐标合理性
			assert.Greater(t, lat, 39.0, "纬度应该在北京范围内")
			assert.Less(t, lat, 41.0, "纬度应该在北京范围内")
			assert.Greater(t, lng, 115.0, "经度应该在北京范围内")
			assert.Less(t, lng, 118.0, "经度应该在北京范围内")

			t.Logf("%s: %s -> (%.6f, %.6f)", name, geocode.FormattedAddress, lat, lng)

			// 短暂延迟避免API限流
			time.Sleep(100 * time.Millisecond)
		})
	}
}

// TestReverseGeocoding 测试逆地理编码功能
func TestReverseGeocoding(t *testing.T) {
	client := setupGeocodingClient()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 测试天安门坐标转地址
	req := &amap.ReverseGeocodeRequest{
		Location:   coordinates["天安门"],
		Radius:     1000,
		Extensions: "base",
	}

	resp, err := client.ReverseGeocode(ctx, req)
	require.NoError(t, err, "逆地理编码请求应该成功")
	require.NotNil(t, resp, "响应不应该为空")

	// 验证结果
	regeocode := resp.Regeocode
	formattedAddress := string(regeocode.FormattedAddress)
	assert.NotEmpty(t, formattedAddress, "应该返回格式化地址")
	assert.Contains(t, formattedAddress, "北京", "地址应该包含北京")

	t.Logf("逆地理编码成功: %s -> %s", coordinates["天安门"], formattedAddress)
}

// TestMultipleCoordinates 测试多个坐标的逆地理编码
func TestMultipleCoordinates(t *testing.T) {
	client := setupGeocodingClient()
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	for name, coordinate := range coordinates {
		t.Run(name, func(t *testing.T) {
			req := &amap.ReverseGeocodeRequest{
				Location:   coordinate,
				Radius:     1000,
				Extensions: "base",
			}

			resp, err := client.ReverseGeocode(ctx, req)
			require.NoError(t, err, "逆地理编码请求应该成功")

			regeocode := resp.Regeocode
			formattedAddress := string(regeocode.FormattedAddress)
			assert.NotEmpty(t, formattedAddress, "应该返回格式化地址")

			t.Logf("%s: %s -> %s", name, coordinate, formattedAddress)

			// 短暂延迟避免API限流
			time.Sleep(100 * time.Millisecond)
		})
	}
}

// TestDetailedReverseGeocoding 测试详细逆地理编码
func TestDetailedReverseGeocoding(t *testing.T) {
	client := setupGeocodingClient()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 使用all扩展获取详细信息
	req := &amap.ReverseGeocodeRequest{
		Location:   coordinates["望京"],
		Radius:     500,
		Extensions: "all",
	}

	resp, err := client.ReverseGeocode(ctx, req)
	require.NoError(t, err, "详细逆地理编码请求应该成功")

	regeocode := resp.Regeocode
	component := regeocode.AddressComponent

	// 验证地址组件
	formattedAddress := string(regeocode.FormattedAddress)
	assert.NotEmpty(t, formattedAddress, "应该有格式化地址")
	t.Logf("格式化地址: %s", formattedAddress)
	t.Logf("省份: %s", string(component.Province))
	t.Logf("城市: %s", string(component.City))
	t.Logf("区县: %s", string(component.District))
	t.Logf("乡镇: %s", string(component.Township))
}

// TestGeocodingAccuracy 测试地理编码精度
func TestGeocodingAccuracy(t *testing.T) {
	client := setupGeocodingClient()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 测试精确地址
	preciseAddresses := []string{
		"北京市朝阳区望京街道望京SOHO T1",
		"北京市海淀区清华大学主楼",
		"北京市东城区天安门广场人民英雄纪念碑",
		"北京市朝阳区国家体育场南路1号",
	}

	for _, address := range preciseAddresses {
		t.Run(address, func(t *testing.T) {
			req := &amap.GeocodeRequest{
				Address: address,
				City:    "北京",
			}

			resp, err := client.Geocode(ctx, req)
			require.NoError(t, err, "精确地址编码应该成功")
			require.Greater(t, len(resp.Geocodes), 0, "应该返回结果")

			geocode := resp.Geocodes[0]
			lat, lng, err := geocode.GetLocation()
			require.NoError(t, err, "坐标解析应该成功")

			t.Logf("精确地址: %s -> (%.6f, %.6f), 级别: %s",
				geocode.FormattedAddress, lat, lng, string(geocode.Level))

			// 短暂延迟
			time.Sleep(200 * time.Millisecond)
		})
	}
}

// TestGeocodingWithoutCity 测试不指定城市的地理编码
func TestGeocodingWithoutCity(t *testing.T) {
	client := setupGeocodingClient()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 测试不指定城市的地址编码
	req := &amap.GeocodeRequest{
		Address: "天安门广场",
		// 不指定City
	}

	resp, err := client.Geocode(ctx, req)
	require.NoError(t, err, "不指定城市的地理编码应该成功")
	require.Greater(t, len(resp.Geocodes), 0, "应该返回结果")

	geocode := resp.Geocodes[0]
	lat, lng, err := geocode.GetLocation()
	require.NoError(t, err, "坐标解析应该成功")

	t.Logf("不指定城市: %s -> (%.6f, %.6f)", geocode.FormattedAddress, lat, lng)
}

// TestGeocodingErrorHandling 测试地理编码错误处理
func TestGeocodingErrorHandling(t *testing.T) {
	client := setupGeocodingClient()
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 测试无效地址
	invalidAddresses := []string{
		"",                // 空地址
		"不存在的地址123456789", // 不存在的地址
		"火星上的某个地方",        // 完全无效的地址
		"@#$%^&*()",       // 特殊字符
	}

	for _, address := range invalidAddresses {
		t.Run("invalid_"+address, func(t *testing.T) {
			req := &amap.GeocodeRequest{
				Address: address,
				City:    "北京",
			}

			resp, err := client.Geocode(ctx, req)
			if err != nil {
				t.Logf("无效地址 '%s' 正确返回错误: %v", address, err)
			} else if len(resp.Geocodes) == 0 {
				t.Logf("无效地址 '%s' 返回空结果", address)
			} else {
				t.Logf("无效地址 '%s' 返回了结果: %s", address, resp.Geocodes[0].FormattedAddress)
			}
		})
	}
}

// TestReverseGeocodingErrorHandling 测试逆地理编码错误处理
func TestReverseGeocodingErrorHandling(t *testing.T) {
	client := setupGeocodingClient()
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 测试无效坐标
	invalidCoordinates := []string{
		"",                    // 空坐标
		"invalid,coordinates", // 无效格式
		"999,999",             // 超出范围的坐标
		"0,0",                 // 海洋中的坐标
		"abc,def",             // 非数字坐标
	}

	for _, coordinate := range invalidCoordinates {
		t.Run("invalid_"+coordinate, func(t *testing.T) {
			req := &amap.ReverseGeocodeRequest{
				Location:   coordinate,
				Radius:     1000,
				Extensions: "base",
			}

			resp, err := client.ReverseGeocode(ctx, req)
			if err != nil {
				t.Logf("无效坐标 '%s' 正确返回错误: %v", coordinate, err)
			} else {
				t.Logf("无效坐标 '%s' 返回了结果: %s", coordinate, string(resp.Regeocode.FormattedAddress))
			}
		})
	}
}

// TestGeocodingRoundTrip 测试地理编码往返转换
func TestGeocodingRoundTrip(t *testing.T) {
	client := setupGeocodingClient()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 选择一个测试地址
	testAddress := addresses["望京SOHO"]

	// 第一步：地址转坐标
	geocodeReq := &amap.GeocodeRequest{
		Address: testAddress,
		City:    "北京",
	}

	geocodeResp, err := client.Geocode(ctx, geocodeReq)
	require.NoError(t, err, "地理编码应该成功")
	require.Greater(t, len(geocodeResp.Geocodes), 0, "应该返回结果")

	geocode := geocodeResp.Geocodes[0]
	lat, lng, err := geocode.GetLocation()
	require.NoError(t, err, "坐标解析应该成功")

	t.Logf("第一步 - 地址转坐标: %s -> (%.6f, %.6f)", testAddress, lat, lng)

	// 第二步：坐标转地址
	reverseReq := &amap.ReverseGeocodeRequest{
		Location:   geocode.Location,
		Radius:     1000,
		Extensions: "base",
	}

	reverseResp, err := client.ReverseGeocode(ctx, reverseReq)
	require.NoError(t, err, "逆地理编码应该成功")

	reversedAddress := string(reverseResp.Regeocode.FormattedAddress)
	t.Logf("第二步 - 坐标转地址: (%.6f, %.6f) -> %s", lat, lng, reversedAddress)

	// 验证往返转换的一致性（地址可能不完全相同，但应该在同一区域）
	assert.Contains(t, reversedAddress, "北京", "逆转换的地址应该包含北京")
	assert.Contains(t, reversedAddress, "朝阳", "逆转换的地址应该包含朝阳区")
}

// TestGeocodingPerformance 测试地理编码性能
func TestGeocodingPerformance(t *testing.T) {
	client := setupGeocodingClient()
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	// 测试批量地理编码性能
	testAddresses := []string{
		"北京市东城区天安门广场",
		"北京市朝阳区国家体育场",
		"北京市海淀区中关村大街",
		"北京市西城区西单大街",
		"北京市丰台区南三环",
	}

	start := time.Now()
	successCount := 0

	for i, address := range testAddresses {
		req := &amap.GeocodeRequest{
			Address: address,
			City:    "北京",
		}

		resp, err := client.Geocode(ctx, req)
		if err == nil && len(resp.Geocodes) > 0 {
			successCount++
			geocode := resp.Geocodes[0]
			lat, lng, _ := geocode.GetLocation()
			t.Logf("批量测试 %d: %s -> (%.6f, %.6f)", i+1, geocode.FormattedAddress, lat, lng)
		}

		// 遵守API限流
		time.Sleep(100 * time.Millisecond)
	}

	duration := time.Since(start)
	t.Logf("性能测试完成: %d/%d 成功, 总耗时: %v, 平均耗时: %v",
		successCount, len(testAddresses), duration, duration/time.Duration(len(testAddresses)))

	assert.Greater(t, successCount, len(testAddresses)/2, "至少一半的请求应该成功")
}
