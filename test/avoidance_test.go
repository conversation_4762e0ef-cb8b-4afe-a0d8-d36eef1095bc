package test

import (
	"context"
	"testing"
	"time"

	"github.com/azel-ko/final-ddd/internal/infrastructure/amap"
	"github.com/azel-ko/final-ddd/internal/pkg/config"
	"github.com/azel-ko/final-ddd/internal/pkg/logger"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func setupAvoidanceTestClient() *amap.Client {
	logger.Init("info")

	amapConfig := &config.AmapConfig{
		APIKey:        "9e471ba01ae18f216cd0fb84032ec7e2",
		WebServiceKey: "9e471ba01ae18f216cd0fb84032ec7e2",
		BaseURL:       "https://restapi.amap.com",
		Timeout:       "30s",
		RateLimit: config.RateLimitConfig{
			RequestsPerSecond: 10,
			Burst:             20,
		},
	}

	return amap.NewClient(amapConfig)
}

// TestAvoidanceStrategy 测试避让策略
func TestAvoidanceStrategy(t *testing.T) {
	client := setupAvoidanceTestClient()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 测试从天津到北京的路线（模拟进京场景）
	origin := "117.200983,39.084158"      // 天津
	destination := "116.407526,39.904030" // 北京

	// 1. 普通路线（速度优先，可能经过检查站）
	normalReq := &amap.DirectionRequest{
		Origin:      origin,
		Destination: destination,
		Strategy:    amap.StrategyFastest,
		Extensions:  "base",
	}

	normalResp, err := client.Direction(ctx, normalReq)
	require.NoError(t, err, "普通路线规划应该成功")
	require.Greater(t, len(normalResp.Route.Paths), 0, "应该返回路线")

	normalPath := normalResp.Route.Paths[0]
	normalDistance, _ := normalPath.GetDistanceInt()
	normalDuration, _ := normalPath.GetDurationInt()

	t.Logf("普通路线: 距离=%d米, 时间=%d秒", normalDistance, normalDuration)

	// 2. 避让路线（费用优先，避开高速公路上的检查站）
	avoidReq := &amap.DirectionRequest{
		Origin:      origin,
		Destination: destination,
		Strategy:    amap.StrategyNoHighway, // 不走高速，避开检查站
		Extensions:  "base",
	}

	avoidResp, err := client.Direction(ctx, avoidReq)
	require.NoError(t, err, "避让路线规划应该成功")
	require.Greater(t, len(avoidResp.Route.Paths), 0, "应该返回避让路线")

	avoidPath := avoidResp.Route.Paths[0]
	avoidDistance, _ := avoidPath.GetDistanceInt()
	avoidDuration, _ := avoidPath.GetDurationInt()

	t.Logf("避让路线: 距离=%d米, 时间=%d秒", avoidDistance, avoidDuration)

	// 3. 验证避让策略的合理性
	// 避让路线可能更长，但避开了高速公路上的检查站
	distanceIncrease := float64(avoidDistance-normalDistance) / float64(normalDistance) * 100
	timeIncrease := float64(avoidDuration-normalDuration) / float64(normalDuration) * 100

	t.Logf("避让代价: 距离增加%.1f%%, 时间增加%.1f%%", distanceIncrease, timeIncrease)

	// 验证避让策略的有效性
	assert.NotEqual(t, normalDistance, avoidDistance, "避让路线应该与普通路线不同")

	// 合理的避让代价应该在可接受范围内
	assert.Less(t, distanceIncrease, 100.0, "距离增加不应超过100%")
	assert.Less(t, timeIncrease, 200.0, "时间增加不应超过200%")
}

// TestMultipleAvoidanceStrategies 测试多种避让策略
func TestMultipleAvoidanceStrategies(t *testing.T) {
	client := setupAvoidanceTestClient()
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	origin := "116.200000,39.800000"      // 北京南部
	destination := "116.400000,40.000000" // 北京北部

	strategies := []struct {
		name     string
		strategy int
		desc     string
	}{
		{"速度优先", amap.StrategyFastest, "可能经过检查站"},
		{"费用优先", amap.StrategyNoHighway, "避开高速检查站"},
		{"距离优先", amap.StrategyDistance, "最短路径"},
	}

	results := make(map[string]map[string]int)

	for _, s := range strategies {
		t.Run(s.name, func(t *testing.T) {
			req := &amap.DirectionRequest{
				Origin:      origin,
				Destination: destination,
				Strategy:    s.strategy,
				Extensions:  "base",
			}

			resp, err := client.Direction(ctx, req)
			require.NoError(t, err, "%s策略应该成功", s.name)
			require.Greater(t, len(resp.Route.Paths), 0, "应该返回路线")

			path := resp.Route.Paths[0]
			distance, _ := path.GetDistanceInt()
			duration, _ := path.GetDurationInt()

			results[s.name] = map[string]int{
				"distance": distance,
				"duration": duration,
			}

			t.Logf("%s (%s): 距离=%d米, 时间=%d秒", s.name, s.desc, distance, duration)

			// 短暂延迟避免API限流
			time.Sleep(200 * time.Millisecond)
		})
	}

	// 分析不同策略的效果
	if len(results) >= 2 {
		fastest := results["速度优先"]
		noHighway := results["费用优先"]

		if fastest != nil && noHighway != nil {
			distanceDiff := noHighway["distance"] - fastest["distance"]
			timeDiff := noHighway["duration"] - fastest["duration"]

			t.Logf("避让效果分析: 费用优先比速度优先距离差=%d米, 时间差=%d秒", distanceDiff, timeDiff)

			// 验证避让策略提供了不同的路线选择
			assert.NotEqual(t, fastest["distance"], noHighway["distance"],
				"不同策略应该产生不同的路线")
		}
	}
}

// TestWaypointAvoidance 测试通过途经点实现避让
func TestWaypointAvoidance(t *testing.T) {
	client := setupAvoidanceTestClient()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	origin := "117.200983,39.084158"      // 天津
	destination := "116.407526,39.904030" // 北京
	waypoint := "116.683752,39.538047"    // 廊坊（绕行点）

	// 1. 直达路线
	directReq := &amap.DirectionRequest{
		Origin:      origin,
		Destination: destination,
		Strategy:    amap.StrategyFastest,
		Extensions:  "base",
	}

	directResp, err := client.Direction(ctx, directReq)
	require.NoError(t, err, "直达路线应该成功")
	require.Greater(t, len(directResp.Route.Paths), 0, "应该返回直达路线")

	directPath := directResp.Route.Paths[0]
	directDistance, _ := directPath.GetDistanceInt()
	directDuration, _ := directPath.GetDurationInt()

	t.Logf("直达路线: 距离=%d米, 时间=%d秒", directDistance, directDuration)

	// 2. 途经点避让路线
	waypointReq := &amap.DirectionRequest{
		Origin:      origin,
		Destination: destination,
		Waypoints:   waypoint,
		Strategy:    amap.StrategyNoHighway,
		Extensions:  "base",
	}

	waypointResp, err := client.Direction(ctx, waypointReq)
	require.NoError(t, err, "途经点路线应该成功")
	require.Greater(t, len(waypointResp.Route.Paths), 0, "应该返回途经点路线")

	waypointPath := waypointResp.Route.Paths[0]
	waypointDistance, _ := waypointPath.GetDistanceInt()
	waypointDuration, _ := waypointPath.GetDurationInt()

	t.Logf("途经点避让路线: 距离=%d米, 时间=%d秒", waypointDistance, waypointDuration)

	// 3. 验证途经点避让效果
	distanceIncrease := float64(waypointDistance-directDistance) / float64(directDistance) * 100
	timeIncrease := float64(waypointDuration-directDuration) / float64(directDuration) * 100

	t.Logf("途经点避让代价: 距离增加%.1f%%, 时间增加%.1f%%", distanceIncrease, timeIncrease)

	// 验证途经点策略的有效性
	assert.Greater(t, waypointDistance, directDistance, "途经点路线应该比直达路线更长")
	assert.Less(t, distanceIncrease, 50.0, "距离增加应该在合理范围内")
	assert.Less(t, timeIncrease, 100.0, "时间增加应该在合理范围内")
}

// TestAvoidanceAlgorithmLogic 测试避让算法逻辑
func TestAvoidanceAlgorithmLogic(t *testing.T) {
	// 测试避让算法的核心逻辑

	// 1. 模拟检查站数据
	checkpoints := []struct {
		name     string
		lat      float64
		lng      float64
		status   string
		severity int
	}{
		{"京藏高速检查站", 40.0776, 116.3297, "active", 4},
		{"京沪高速检查站", 39.7284, 116.2734, "active", 5},
		{"京港澳高速检查站", 39.6891, 116.1831, "active", 3},
		{"京承高速检查站", 40.1776, 116.4297, "inactive", 2},
	}

	// 2. 测试活跃检查站过滤
	activeCheckpoints := 0
	totalSeverity := 0

	for _, cp := range checkpoints {
		if cp.status == "active" {
			activeCheckpoints++
			totalSeverity += cp.severity
		}
	}

	assert.Equal(t, 3, activeCheckpoints, "应该有3个活跃检查站")
	assert.Equal(t, 12, totalSeverity, "活跃检查站总严格度应该为12")

	// 3. 测试风险评分算法
	avgSeverity := float64(totalSeverity) / float64(activeCheckpoints)
	riskScore := int(avgSeverity * 20) // 转换为0-100分制

	expectedRisk := int(float64(12) / float64(3) * 20) // (4+5+3)/3 * 20 = 80
	assert.Equal(t, expectedRisk, riskScore, "风险评分计算应该正确")

	t.Logf("避让算法验证: 活跃检查站=%d个, 平均严格度=%.1f, 风险评分=%d",
		activeCheckpoints, avgSeverity, riskScore)

	// 4. 验证避让策略选择逻辑
	if riskScore > 60 {
		recommendedStrategy := "费用优先" // 避开高速
		t.Logf("高风险场景推荐策略: %s", recommendedStrategy)
		assert.Equal(t, "费用优先", recommendedStrategy, "高风险时应推荐避让策略")
	} else {
		recommendedStrategy := "速度优先" // 正常路线
		t.Logf("低风险场景推荐策略: %s", recommendedStrategy)
	}
}
