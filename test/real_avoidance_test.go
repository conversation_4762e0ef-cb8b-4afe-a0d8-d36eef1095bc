package test

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/azel-ko/final-ddd/internal/infrastructure/amap"
	"github.com/azel-ko/final-ddd/internal/pkg/config"
	"github.com/azel-ko/final-ddd/internal/pkg/logger"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// RealCameraData 真实摄像头数据结构
type RealCameraData struct {
	Name     string
	Lat      float64
	Lng      float64
	Status   string
	Time     string
	District string
	Road     string
}

func setupRealAvoidanceClient() *amap.Client {
	logger.Init("info")

	amapConfig := &config.AmapConfig{
		APIKey:        "9e471ba01ae18f216cd0fb84032ec7e2",
		WebServiceKey: "9e471ba01ae18f216cd0fb84032ec7e2",
		BaseURL:       "https://restapi.amap.com",
		Timeout:       "30s",
		RateLimit: config.RateLimitConfig{
			RequestsPerSecond: 10,
			Burst:             20,
		},
	}

	return amap.NewClient(amapConfig)
}

// fetchRealCameraData 获取真实摄像头数据（带重试机制）
func fetchRealCameraData() ([]RealCameraData, error) {
	maxRetries := 3
	var lastErr error

	for retry := 0; retry < maxRetries; retry++ {
		if retry > 0 {
			time.Sleep(time.Duration(retry*2) * time.Second) // 指数退避
		}

		client := &http.Client{Timeout: 45 * time.Second}

		req, err := http.NewRequest("GET", "https://jinjing365.com/index.asp", nil)
		if err != nil {
			lastErr = err
			continue
		}

		req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
		req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
		req.Header.Set("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
		req.Header.Set("Connection", "keep-alive")

		resp, err := client.Do(req)
		if err != nil {
			lastErr = fmt.Errorf("网络请求失败 (重试 %d/%d): %v", retry+1, maxRetries, err)
			continue
		}

		if resp.StatusCode != 200 {
			resp.Body.Close()
			lastErr = fmt.Errorf("HTTP状态码错误: %d", resp.StatusCode)
			continue
		}

		body, err := io.ReadAll(resp.Body)
		resp.Body.Close()
		if err != nil {
			lastErr = fmt.Errorf("读取响应失败: %v", err)
			continue
		}

		htmlContent := string(body)
		if len(htmlContent) < 1000 {
			lastErr = fmt.Errorf("响应内容过短，可能是错误页面")
			continue
		}

		// 查找LabelsData
		re := regexp.MustCompile(`(?s)var\s+LabelsData\s*=\s*\[(.*?)\];`)
		matches := re.FindStringSubmatch(htmlContent)
		if len(matches) < 2 {
			lastErr = fmt.Errorf("未找到LabelsData数据")
			continue
		}

		cameras := parseRealCameras(matches[1])
		if len(cameras) == 0 {
			lastErr = fmt.Errorf("解析摄像头数据失败")
			continue
		}

		return cameras, nil
	}

	return nil, fmt.Errorf("获取摄像头数据失败，已重试%d次: %v", maxRetries, lastErr)
}

// parseRealCameras 解析真实摄像头数据
func parseRealCameras(labelsDataStr string) []RealCameraData {
	var cameras []RealCameraData

	objectRe := regexp.MustCompile(`\{[^}]*\}`)
	objects := objectRe.FindAllString(labelsDataStr, -1)

	for _, obj := range objects {
		camera := parseRealCameraObject(obj)
		if camera.Name != "" && camera.Lat != 0 && camera.Lng != 0 {
			cameras = append(cameras, camera)
		}
	}

	return cameras
}

// parseRealCameraObject 解析单个摄像头对象
func parseRealCameraObject(objStr string) RealCameraData {
	var camera RealCameraData

	// 提取name
	nameRe := regexp.MustCompile(`name:\s*'([^']+)'`)
	if matches := nameRe.FindStringSubmatch(objStr); len(matches) > 1 {
		camera.Name = matches[1]
	}

	// 提取position
	positionRe := regexp.MustCompile(`position:\s*\[([^\]]+)\]`)
	if matches := positionRe.FindStringSubmatch(objStr); len(matches) > 1 {
		coords := strings.Split(matches[1], ",")
		if len(coords) >= 2 {
			if lng, err := strconv.ParseFloat(strings.TrimSpace(coords[0]), 64); err == nil {
				if lat, err := strconv.ParseFloat(strings.TrimSpace(coords[1]), 64); err == nil {
					camera.Lng = lng
					camera.Lat = lat
				}
			}
		}
	}

	// 提取time
	timeRe := regexp.MustCompile(`time:\s*'([^']+)'`)
	if matches := timeRe.FindStringSubmatch(objStr); len(matches) > 1 {
		camera.Time = matches[1]
	}

	// 解析位置信息
	camera.District = parseDistrictFromName(camera.Name)
	camera.Road = parseRoadFromName(camera.Name)

	// 确定状态
	if camera.Time != "" {
		if updateTime, err := time.Parse("2006-01-02", camera.Time); err == nil {
			// 放宽活跃判断标准：30天内的都算活跃
			if time.Since(updateTime) <= 30*24*time.Hour {
				camera.Status = "active"
			} else {
				camera.Status = "inactive"
			}
		} else {
			camera.Status = "unknown"
		}
	} else {
		camera.Status = "unknown"
	}

	return camera
}

// parseDistrictFromName 从名称中解析区域
func parseDistrictFromName(name string) string {
	districts := []string{"朝阳区", "海淀区", "丰台区", "西城区", "东城区", "石景山区",
		"门头沟区", "房山区", "通州区", "顺义区", "昌平区", "大兴区", "怀柔区", "平谷区", "密云区", "延庆区"}

	// 直接匹配完整区名
	for _, district := range districts {
		if strings.Contains(name, district) {
			return district
		}
	}

	// 匹配不带"区"的名称
	for _, district := range districts {
		districtName := strings.TrimSuffix(district, "区")
		if strings.Contains(name, districtName) {
			return district
		}
	}

	// 特殊情况处理
	if strings.Contains(name, "开发区") {
		return "大兴区"
	}

	// 机场区域
	if strings.Contains(name, "首都机场") || strings.Contains(name, "机场") {
		return "朝阳区" // 首都机场主要在朝阳区
	}

	// 高速公路和环路
	if strings.Contains(name, "六环") || strings.Contains(name, "五环") ||
		strings.Contains(name, "四环") || strings.Contains(name, "三环") ||
		strings.Contains(name, "二环") {
		return "环路系统"
	}

	// 国道和高速
	if strings.Contains(name, "国道") || strings.Contains(name, "高速") ||
		strings.Contains(name, "京") && (strings.Contains(name, "线") || strings.Contains(name, "路")) {
		return "高速公路"
	}

	// 桥梁
	if strings.Contains(name, "桥") && !strings.Contains(name, "区") {
		return "桥梁系统"
	}

	return "其他区域"
}

// parseRoadFromName 从名称中解析道路
func parseRoadFromName(name string) string {
	roadPatterns := []string{
		`([^区\s]+路)`,
		`([^区\s]+街)`,
		`([^区\s]+大街)`,
		`([^区\s]+桥)`,
	}

	for _, pattern := range roadPatterns {
		re := regexp.MustCompile(pattern)
		if matches := re.FindStringSubmatch(name); len(matches) > 1 {
			return matches[1]
		}
	}

	return ""
}

// findCamerasAlongRoute 查找路线附近的摄像头
func findCamerasAlongRoute(cameras []RealCameraData, routeSteps []amap.Step, radiusKm float64) []RealCameraData {
	var nearbyCameras []RealCameraData

	// 简化实现：检查摄像头是否在北京范围内且为活跃状态
	for _, camera := range cameras {
		if camera.Status == "active" &&
			camera.Lat > 39.4 && camera.Lat < 41.1 &&
			camera.Lng > 115.4 && camera.Lng < 117.5 {
			nearbyCameras = append(nearbyCameras, camera)
		}
	}

	return nearbyCameras
}

// calculateRealRouteRisk 计算基于真实摄像头数据的路线风险评分
func calculateRealRouteRisk(nearbyCameras []RealCameraData) int {
	if len(nearbyCameras) == 0 {
		return 0
	}

	// 基于摄像头数量计算风险
	risk := len(nearbyCameras) * 5 // 每个摄像头5分风险
	if risk > 100 {
		risk = 100
	}

	return risk
}

// TestRealCameraDataFetch 测试真实摄像头数据获取
func TestRealCameraDataFetch(t *testing.T) {
	t.Log("开始获取真实摄像头数据...")

	cameras, err := fetchRealCameraData()
	require.NoError(t, err, "获取摄像头数据应该成功")
	require.Greater(t, len(cameras), 1000, "应该获取到大量摄像头数据")

	t.Logf("✓ 成功获取 %d 个摄像头数据", len(cameras))

	// 统计活跃摄像头
	activeCount := 0
	districtCount := make(map[string]int)

	for _, camera := range cameras {
		if camera.Status == "active" {
			activeCount++
		}
		if camera.District != "" {
			districtCount[camera.District]++
		}
	}

	t.Logf("活跃摄像头: %d 个 (%.1f%%)", activeCount, float64(activeCount)/float64(len(cameras))*100)

	// 显示各区分布
	t.Log("各区摄像头分布:")
	for district, count := range districtCount {
		if count > 0 {
			t.Logf("  %s: %d个", district, count)
		}
	}
}

// TestRealAvoidanceStrategy 基于真实数据测试避让策略
func TestRealAvoidanceStrategy(t *testing.T) {
	client := setupRealAvoidanceClient()
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	// 获取真实摄像头数据
	t.Log("获取真实摄像头数据...")
	cameras, err := fetchRealCameraData()
	require.NoError(t, err, "获取摄像头数据应该成功")
	t.Logf("获取到 %d 个摄像头", len(cameras))

	// 测试从天津到北京的路线（典型进京场景）
	origin := "117.200983,39.084158"      // 天津
	destination := "116.407526,39.904030" // 北京市中心

	// 1. 普通路线（速度优先）
	t.Log("规划普通路线...")
	normalReq := &amap.DirectionRequest{
		Origin:      origin,
		Destination: destination,
		Strategy:    amap.StrategyFastest,
		Extensions:  "all",
	}

	normalResp, err := client.Direction(ctx, normalReq)
	require.NoError(t, err, "普通路线规划应该成功")
	require.Greater(t, len(normalResp.Route.Paths), 0, "应该返回路线")

	normalPath := normalResp.Route.Paths[0]
	normalDistance, _ := normalPath.GetDistanceInt()
	normalDuration, _ := normalPath.GetDurationInt()

	// 查找普通路线附近的摄像头
	normalCameras := findCamerasAlongRoute(cameras, normalPath.Steps, 5.0)
	normalRisk := calculateRealRouteRisk(normalCameras)

	t.Logf("普通路线: 距离=%d米, 时间=%d秒, 附近摄像头=%d个, 风险=%d",
		normalDistance, normalDuration, len(normalCameras), normalRisk)

	// 2. 避让路线（费用优先，避开高速）
	t.Log("规划避让路线...")
	avoidReq := &amap.DirectionRequest{
		Origin:      origin,
		Destination: destination,
		Strategy:    amap.StrategyNoHighway, // 避开高速公路
		Extensions:  "all",
	}

	avoidResp, err := client.Direction(ctx, avoidReq)
	require.NoError(t, err, "避让路线规划应该成功")
	require.Greater(t, len(avoidResp.Route.Paths), 0, "应该返回避让路线")

	avoidPath := avoidResp.Route.Paths[0]
	avoidDistance, _ := avoidPath.GetDistanceInt()
	avoidDuration, _ := avoidPath.GetDurationInt()

	// 查找避让路线附近的摄像头
	avoidCameras := findCamerasAlongRoute(cameras, avoidPath.Steps, 5.0)
	avoidRisk := calculateRealRouteRisk(avoidCameras)

	t.Logf("避让路线: 距离=%d米, 时间=%d秒, 附近摄像头=%d个, 风险=%d",
		avoidDistance, avoidDuration, len(avoidCameras), avoidRisk)

	// 3. 验证避让效果
	distanceIncrease := float64(avoidDistance-normalDistance) / float64(normalDistance) * 100
	timeIncrease := float64(avoidDuration-normalDuration) / float64(normalDuration) * 100
	riskReduction := normalRisk - avoidRisk

	t.Logf("避让效果: 距离增加%.1f%%, 时间增加%.1f%%, 风险降低%d分",
		distanceIncrease, timeIncrease, riskReduction)

	// 验证避让策略的有效性
	assert.NotEqual(t, normalDistance, avoidDistance, "避让路线应该与普通路线不同")
	assert.Less(t, distanceIncrease, 100.0, "距离增加应该在合理范围内")
	assert.Less(t, timeIncrease, 200.0, "时间增加应该在合理范围内")

	// 如果风险确实降低了，说明避让策略有效
	if riskReduction > 0 {
		t.Logf("✓ 避让策略有效，成功降低风险 %d 分", riskReduction)
	} else {
		t.Logf("⚠ 避让策略效果不明显，可能需要优化算法")
	}
}

// TestRealCameraDistribution 测试真实摄像头分布
func TestRealCameraDistribution(t *testing.T) {
	cameras, err := fetchRealCameraData()
	require.NoError(t, err, "获取摄像头数据应该成功")

	// 先检查一些样本数据
	t.Log("样本摄像头数据:")
	sampleCount := 10
	if len(cameras) < sampleCount {
		sampleCount = len(cameras)
	}

	activeCount := 0
	emptyDistrictCount := 0
	emptyTimeCount := 0

	for i := 0; i < sampleCount; i++ {
		camera := cameras[i]
		t.Logf("  样本%d: Name='%s', Time='%s', Status='%s', District='%s'",
			i+1, camera.Name, camera.Time, camera.Status, camera.District)

		if camera.Status == "active" {
			activeCount++
		}
		if camera.District == "" {
			emptyDistrictCount++
		}
		if camera.Time == "" {
			emptyTimeCount++
		}
	}

	t.Logf("样本统计: 活跃=%d, 空区域=%d, 空时间=%d", activeCount, emptyDistrictCount, emptyTimeCount)

	// 检查一些没有区域信息的摄像头
	t.Log("没有区域信息的摄像头样本:")
	noDistrictCount := 0
	for _, camera := range cameras {
		if camera.District == "" && noDistrictCount < 5 {
			t.Logf("  无区域样本%d: Name='%s'", noDistrictCount+1, camera.Name)
			noDistrictCount++
		}
	}

	// 分析摄像头分布
	districtStats := make(map[string]struct {
		Total  int
		Active int
	})

	for _, camera := range cameras {
		if camera.District != "" {
			stats := districtStats[camera.District]
			stats.Total++
			if camera.Status == "active" {
				stats.Active++
			}
			districtStats[camera.District] = stats
		}
	}

	t.Log("各区摄像头分布详情:")
	for district, stats := range districtStats {
		activeRate := float64(stats.Active) / float64(stats.Total) * 100
		t.Logf("  %s: 总计%d个, 活跃%d个 (%.1f%%)",
			district, stats.Total, stats.Active, activeRate)
	}

	// 验证数据质量
	totalCameras := len(cameras)
	validCoords := 0
	validDistricts := 0

	for _, camera := range cameras {
		// 验证坐标是否在北京范围内
		if camera.Lat > 39.4 && camera.Lat < 41.1 &&
			camera.Lng > 115.4 && camera.Lng < 117.5 {
			validCoords++
		}

		if camera.District != "" {
			validDistricts++
		}
	}

	t.Logf("数据质量: 有效坐标%.1f%%, 有效区域%.1f%%",
		float64(validCoords)/float64(totalCameras)*100,
		float64(validDistricts)/float64(totalCameras)*100)

	assert.Greater(t, float64(validCoords)/float64(totalCameras), 0.8,
		"至少80%的摄像头应该有有效坐标")
	assert.Greater(t, float64(validDistricts)/float64(totalCameras), 0.8,
		"至少80%的摄像头应该有有效区域信息")
}

// TestHighRiskAreaIdentification 测试高风险区域识别
func TestHighRiskAreaIdentification(t *testing.T) {
	cameras, err := fetchRealCameraData()
	require.NoError(t, err, "获取摄像头数据应该成功")

	// 统计各区域的活跃摄像头密度
	districtDensity := make(map[string]float64)
	districtCount := make(map[string]int)

	for _, camera := range cameras {
		if camera.District != "" && camera.Status == "active" {
			districtCount[camera.District]++
		}
	}

	// 计算密度（简化为摄像头数量）
	for district, count := range districtCount {
		districtDensity[district] = float64(count)
	}

	// 识别高风险区域（摄像头数量最多的前5个区域）
	type DistrictRisk struct {
		Name    string
		Density float64
	}

	var risks []DistrictRisk
	for district, density := range districtDensity {
		risks = append(risks, DistrictRisk{Name: district, Density: density})
	}

	// 简单排序（冒泡排序）
	for i := 0; i < len(risks)-1; i++ {
		for j := 0; j < len(risks)-1-i; j++ {
			if risks[j].Density < risks[j+1].Density {
				risks[j], risks[j+1] = risks[j+1], risks[j]
			}
		}
	}

	t.Log("高风险区域排名（按活跃摄像头数量）:")
	topCount := 5
	if len(risks) < topCount {
		topCount = len(risks)
	}

	for i := 0; i < topCount; i++ {
		risk := risks[i]
		t.Logf("  %d. %s: %d个活跃摄像头", i+1, risk.Name, int(risk.Density))
	}

	// 验证至少识别出了一些高风险区域
	assert.Greater(t, len(risks), 0, "应该识别出高风险区域")
	if len(risks) > 0 {
		assert.Greater(t, risks[0].Density, 0.0, "最高风险区域应该有活跃摄像头")
	}
}
