package test

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestMiniprogramCompatibility 测试小程序的兼容性和用户体验
func TestMiniprogramCompatibility(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("小程序环境检测", func(t *testing.T) {
		testMiniprogramEnvironment(t)
	})

	t.Run("微信授权流程", func(t *testing.T) {
		testWechatAuthFlow(t)
	})

	t.Run("地图组件兼容性", func(t *testing.T) {
		testMapComponentCompatibility(t)
	})

	t.Run("网络请求优化", func(t *testing.T) {
		testNetworkOptimization(t)
	})

	t.Run("数据缓存策略", func(t *testing.T) {
		testDataCaching(t)
	})

	t.Run("错误处理和用户提示", func(t *testing.T) {
		testErrorHandling(t)
	})

	t.Run("性能和加载速度", func(t *testing.T) {
		testPerformanceAndLoading(t)
	})
}

func testMiniprogramEnvironment(t *testing.T) {
	router := setupTestRouter()

	// 测试小程序User-Agent识别
	req, _ := http.NewRequest("GET", "/api/v1/checkpoints/nearby?lat=39.9042&lng=116.4074&radius=10", nil)
	req.Header.Set("User-Agent", "miniprogram")
	req.Header.Set("Authorization", "Bearer test-token")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// 小程序请求应该被正确识别和处理
	assert.True(t, w.Code == http.StatusOK || w.Code == http.StatusUnauthorized)

	// 测试小程序特有的请求头处理
	req, _ = http.NewRequest("GET", "/api/v1/checkpoints/nearby?lat=39.9042&lng=116.4074&radius=10", nil)
	req.Header.Set("User-Agent", "miniprogram")
	req.Header.Set("X-Miniprogram-Version", "1.0.0")
	req.Header.Set("X-Miniprogram-Scene", "1001") // 发现小程序主入口

	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// 验证小程序特有头部被正确处理
	assert.True(t, w.Code == http.StatusOK || w.Code == http.StatusUnauthorized)
}

func testWechatAuthFlow(t *testing.T) {
	router := setupTestRouter()

	// 测试微信小程序登录流程
	loginData := map[string]interface{}{
		"code":     "test-wx-code-12345",
		"platform": "miniprogram",
		"user_info": map[string]interface{}{
			"nickName":  "小程序测试用户",
			"avatarUrl": "https://wx.qlogo.cn/mmopen/test.jpg",
			"gender":    1,
			"city":      "北京",
			"province":  "北京",
			"country":   "中国",
			"language":  "zh_CN",
		},
		"raw_data":       `{"nickName":"小程序测试用户","gender":1,"language":"zh_CN","city":"北京","province":"北京","country":"中国","avatarUrl":"https://wx.qlogo.cn/mmopen/test.jpg"}`,
		"signature":      "test-signature",
		"encrypted_data": "test-encrypted-data",
		"iv":             "test-iv",
	}

	jsonData, _ := json.Marshal(loginData)
	req, _ := http.NewRequest("POST", "/api/v1/auth/miniprogram/login", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "miniprogram")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	// 验证登录响应包含必要信息
	assert.Contains(t, response, "token")
	assert.Contains(t, response, "user")
	assert.Contains(t, response, "subscription")

	// 验证用户信息正确保存
	user := response["user"].(map[string]interface{})
	assert.Equal(t, "小程序测试用户", user["nickname"])

	// 验证试用期自动分配
	subscription := response["subscription"].(map[string]interface{})
	assert.Equal(t, "trial", subscription["type"])
}

func testMapComponentCompatibility(t *testing.T) {
	router := setupTestRouter()
	token := getTestUserToken(t, router)

	// 测试地图数据格式兼容性
	req, _ := http.NewRequest("GET", "/api/v1/checkpoints/nearby?lat=39.9042&lng=116.4074&radius=10", nil)
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("User-Agent", "miniprogram")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	// 验证数据格式符合小程序地图组件要求
	data := response["data"].([]interface{})
	if len(data) > 0 {
		checkpoint := data[0].(map[string]interface{})

		// 验证坐标格式
		assert.Contains(t, checkpoint, "latitude")
		assert.Contains(t, checkpoint, "longitude")

		// 验证坐标值类型
		lat, ok := checkpoint["latitude"].(float64)
		assert.True(t, ok)
		assert.True(t, lat >= -90 && lat <= 90)

		lng, ok := checkpoint["longitude"].(float64)
		assert.True(t, ok)
		assert.True(t, lng >= -180 && lng <= 180)
	}

	// 测试路线数据格式
	routeData := map[string]interface{}{
		"origin":      map[string]float64{"lat": 39.9042, "lng": 116.4074},
		"destination": map[string]float64{"lat": 40.0042, "lng": 116.5074},
		"car_plate":   "冀A12345",
		"avoid_level": 2,
	}

	jsonData, _ := json.Marshal(routeData)
	req, _ = http.NewRequest("POST", "/api/v1/navigation/route", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("User-Agent", "miniprogram")

	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	if w.Code == http.StatusOK {
		var routeResponse map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &routeResponse)
		require.NoError(t, err)

		// 验证路线数据格式符合小程序要求
		routes := routeResponse["routes"].([]interface{})
		if len(routes) > 0 {
			route := routes[0].(map[string]interface{})
			assert.Contains(t, route, "polyline") // 小程序地图需要的路线数据
		}
	}
}

func testNetworkOptimization(t *testing.T) {
	router := setupTestRouter()
	token := getTestUserToken(t, router)

	// 测试请求压缩
	req, _ := http.NewRequest("GET", "/api/v1/checkpoints/nearby?lat=39.9042&lng=116.4074&radius=10", nil)
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("User-Agent", "miniprogram")
	req.Header.Set("Accept-Encoding", "gzip, deflate")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	// 测试响应时间（小程序对响应时间敏感）
	start := time.Now()

	req, _ = http.NewRequest("GET", "/api/v1/checkpoints/nearby?lat=39.9042&lng=116.4074&radius=10", nil)
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("User-Agent", "miniprogram")

	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	duration := time.Since(start)

	// 小程序API响应时间应该在合理范围内
	assert.True(t, duration < 2*time.Second, "API响应时间过长: %v", duration)

	// 测试批量请求优化
	batchData := map[string]interface{}{
		"requests": []map[string]interface{}{
			{"type": "checkpoints", "params": map[string]interface{}{"lat": 39.9042, "lng": 116.4074, "radius": 5}},
			{"type": "user_preferences", "params": map[string]interface{}{}},
			{"type": "subscription", "params": map[string]interface{}{}},
		},
	}

	jsonData, _ := json.Marshal(batchData)
	req, _ = http.NewRequest("POST", "/api/v1/batch", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("User-Agent", "miniprogram")

	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// 批量请求可能未实现，但不应该返回500错误
	assert.True(t, w.Code != http.StatusInternalServerError)
}

func testDataCaching(t *testing.T) {
	router := setupTestRouter()
	token := getTestUserToken(t, router)

	// 测试缓存头设置
	req, _ := http.NewRequest("GET", "/api/v1/checkpoints/nearby?lat=39.9042&lng=116.4074&radius=10", nil)
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("User-Agent", "miniprogram")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	// 验证缓存相关头部
	cacheControl := w.Header().Get("Cache-Control")
	assert.NotEmpty(t, cacheControl)

	// 测试条件请求支持
	etag := w.Header().Get("ETag")
	if etag != "" {
		req, _ = http.NewRequest("GET", "/api/v1/checkpoints/nearby?lat=39.9042&lng=116.4074&radius=10", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("User-Agent", "miniprogram")
		req.Header.Set("If-None-Match", etag)

		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// 如果数据未变化，应该返回304
		assert.True(t, w.Code == http.StatusOK || w.Code == http.StatusNotModified)
	}
}

func testErrorHandling(t *testing.T) {
	router := setupTestRouter()

	// 测试网络错误处理
	req, _ := http.NewRequest("GET", "/api/v1/nonexistent-endpoint", nil)
	req.Header.Set("User-Agent", "miniprogram")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	// 验证错误响应格式适合小程序显示
	assert.Contains(t, response, "error")
	assert.Contains(t, response, "code")

	// 测试权限错误处理
	req, _ = http.NewRequest("GET", "/api/v1/users/preferences", nil)
	req.Header.Set("User-Agent", "miniprogram")
	// 不设置Authorization头

	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusUnauthorized, w.Code)

	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	// 验证错误信息对小程序用户友好
	errorMsg := response["error"].(string)
	assert.NotContains(t, errorMsg, "internal") // 不应该暴露内部错误
	assert.NotContains(t, errorMsg, "database") // 不应该暴露数据库错误

	// 测试业务逻辑错误处理
	token := getFreeUserToken(t, router) // 免费用户

	routeData := map[string]interface{}{
		"origin":      map[string]float64{"lat": 39.9042, "lng": 116.4074},
		"destination": map[string]float64{"lat": 40.0042, "lng": 116.5074},
		"car_plate":   "冀A12345",
		"avoid_level": 2,
	}

	jsonData, _ := json.Marshal(routeData)
	req, _ = http.NewRequest("POST", "/api/v1/navigation/route", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("User-Agent", "miniprogram")

	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusForbidden, w.Code)

	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	// 验证业务错误信息清晰
	errorMsg = response["error"].(string)
	assert.Contains(t, errorMsg, "试用期") // 应该明确说明试用期问题
}

func testPerformanceAndLoading(t *testing.T) {
	router := setupTestRouter()
	token := getTestUserToken(t, router)

	// 测试首屏加载性能
	start := time.Now()

	// 模拟小程序首屏需要的数据请求
	requests := []string{
		"/api/v1/users/subscription",
		"/api/v1/users/preferences",
		"/api/v1/checkpoints/nearby?lat=39.9042&lng=116.4074&radius=10",
	}

	for _, endpoint := range requests {
		req, _ := http.NewRequest("GET", endpoint, nil)
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("User-Agent", "miniprogram")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// 每个请求都应该快速响应
		assert.True(t, w.Code == http.StatusOK || w.Code == http.StatusForbidden)
	}

	totalDuration := time.Since(start)

	// 首屏数据加载总时间应该在合理范围内
	assert.True(t, totalDuration < 3*time.Second, "首屏加载时间过长: %v", totalDuration)

	// 测试分页加载性能
	start = time.Now()

	req, _ := http.NewRequest("GET", "/api/v1/checkpoints?page=1&limit=20", nil)
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("User-Agent", "miniprogram")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	pageDuration := time.Since(start)

	// 分页请求应该快速响应
	assert.True(t, pageDuration < 1*time.Second, "分页加载时间过长: %v", pageDuration)

	if w.Code == http.StatusOK {
		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		// 验证分页数据结构
		assert.Contains(t, response, "data")
		assert.Contains(t, response, "total")
		assert.Contains(t, response, "page")
		assert.Contains(t, response, "limit")
	}
}
