package test

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/azel-ko/final-ddd/internal/application/dto"
	"github.com/azel-ko/final-ddd/internal/infrastructure/cache"
	"github.com/azel-ko/final-ddd/internal/infrastructure/migration"
	"github.com/azel-ko/final-ddd/internal/infrastructure/persistence"
	"github.com/azel-ko/final-ddd/internal/interfaces/http/router"
	"github.com/azel-ko/final-ddd/internal/pkg/config"
	baseMigration "github.com/azel-ko/final-ddd/internal/pkg/database/migration"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestAuthInterface 测试用户认证接口的完整功能
func TestAuthInterface(t *testing.T) {
	// 设置测试环境
	cfg := &config.Config{
		App: config.App{
			Env: "test",
		},
		Database: config.DatabaseConfig{
			Type: "sqlite",
			Path: ":memory:",
		},
		JWT: config.JWTConfig{
			Key: "test-secret-key-for-jwt-signing",
		},
		Redis: config.RedisConfig{
			Host:     "localhost",
			Port:     6379,
			Password: "",
			DB:       1, // 使用测试数据库
		},
	}

	// 初始化数据库
	repo, db, err := persistence.NewRepository(cfg)
	require.NoError(t, err, "Failed to initialize repository")

	// 运行数据库迁移
	migrator := baseMigration.NewMigrator(db)
	migration.RegisterMigrations(migrator)
	err = migrator.Run()
	require.NoError(t, err, "Failed to run database migrations")

	// 初始化Redis缓存
	redisAddr := fmt.Sprintf("%s:%d", cfg.Redis.Host, cfg.Redis.Port)
	redisCache := cache.NewRedisCache(redisAddr, cfg.Redis.Password)

	// 测试Redis连接，如果失败则跳过需要Redis的测试
	ctx := context.Background()
	if err := redisCache.Ping(ctx); err != nil {
		t.Logf("Redis not available, some tests may be skipped: %v", err)
	}

	// 设置路由
	gin.SetMode(gin.TestMode)
	r := router.Setup(cfg, repo, redisCache)

	t.Run("用户注册接口测试", func(t *testing.T) {
		testUserRegistration(t, r)
	})

	t.Run("用户登录接口测试", func(t *testing.T) {
		testUserLogin(t, r)
	})

	t.Run("会话管理接口测试", func(t *testing.T) {
		testSessionManagement(t, r)
	})

	t.Run("认证中间件测试", func(t *testing.T) {
		testAuthMiddleware(t, r)
	})

	t.Run("多平台登录测试", func(t *testing.T) {
		testMultiPlatformLogin(t, r)
	})
}

// testUserRegistration 测试用户注册功能
func testUserRegistration(t *testing.T, r *gin.Engine) {
	tests := []struct {
		name           string
		requestBody    dto.UserRequest
		expectedStatus int
		expectError    bool
	}{
		{
			name: "成功注册新用户",
			requestBody: dto.UserRequest{
				Name:        "测试用户",
				Email:       "<EMAIL>",
				Password:    "password123",
				CarPlate:    "京A12345",
				PlateRegion: "北京",
			},
			expectedStatus: http.StatusCreated,
			expectError:    false,
		},
		{
			name: "邮箱格式错误",
			requestBody: dto.UserRequest{
				Name:     "测试用户2",
				Email:    "invalid-email",
				Password: "password123",
			},
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
		},
		{
			name: "密码太短",
			requestBody: dto.UserRequest{
				Name:     "测试用户3",
				Email:    "<EMAIL>",
				Password: "123",
			},
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
		},
		{
			name: "重复邮箱注册",
			requestBody: dto.UserRequest{
				Name:     "测试用户4",
				Email:    "<EMAIL>", // 使用已存在的邮箱
				Password: "password123",
			},
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			jsonData, _ := json.Marshal(tt.requestBody)
			req, _ := http.NewRequest("POST", "/api/auth/register", bytes.NewBuffer(jsonData))
			req.Header.Set("Content-Type", "application/json")

			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			var response map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &response)
			require.NoError(t, err)

			if tt.expectError {
				assert.Contains(t, response, "error")
			} else {
				assert.Contains(t, response, "id")
				assert.Equal(t, tt.requestBody.Email, response["email"])
				assert.Equal(t, tt.requestBody.Name, response["username"])
			}
		})
	}
}

// testUserLogin 测试用户登录功能
func testUserLogin(t *testing.T, r *gin.Engine) {
	// 首先注册一个用户用于登录测试
	registerReq := dto.UserRequest{
		Name:        "登录测试用户",
		Email:       "<EMAIL>",
		Password:    "password123",
		CarPlate:    "京B12345",
		PlateRegion: "北京",
	}
	jsonData, _ := json.Marshal(registerReq)
	req, _ := http.NewRequest("POST", "/api/auth/register", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusCreated, w.Code)

	tests := []struct {
		name           string
		requestBody    dto.LoginRequest
		expectedStatus int
		expectError    bool
	}{
		{
			name: "成功登录",
			requestBody: dto.LoginRequest{
				Email:    "<EMAIL>",
				Password: "password123",
				Platform: "web",
				DeviceID: "test-device-1",
			},
			expectedStatus: http.StatusOK,
			expectError:    false,
		},
		{
			name: "错误密码",
			requestBody: dto.LoginRequest{
				Email:    "<EMAIL>",
				Password: "wrongpassword",
				Platform: "web",
			},
			expectedStatus: http.StatusUnauthorized,
			expectError:    true,
		},
		{
			name: "不存在的用户",
			requestBody: dto.LoginRequest{
				Email:    "<EMAIL>",
				Password: "password123",
				Platform: "web",
			},
			expectedStatus: http.StatusUnauthorized,
			expectError:    true,
		},
		{
			name: "移动端登录",
			requestBody: dto.LoginRequest{
				Email:     "<EMAIL>",
				Password:  "password123",
				Platform:  "mobile",
				DeviceID:  "mobile-device-1",
				UserAgent: "TestApp/1.0",
			},
			expectedStatus: http.StatusOK,
			expectError:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			jsonData, _ := json.Marshal(tt.requestBody)
			req, _ := http.NewRequest("POST", "/api/auth/login", bytes.NewBuffer(jsonData))
			req.Header.Set("Content-Type", "application/json")
			req.Header.Set("User-Agent", "TestClient/1.0")

			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			var response map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &response)
			require.NoError(t, err)

			if tt.expectError {
				assert.Contains(t, response, "error")
			} else {
				assert.Contains(t, response, "token")
				assert.Contains(t, response, "session_id")
				assert.Contains(t, response, "expires_at")
				assert.Contains(t, response, "User")

				// 验证token不为空
				token, ok := response["token"].(string)
				assert.True(t, ok)
				assert.NotEmpty(t, token)

				// 验证过期时间合理
				expiresAt, ok := response["expires_at"].(float64)
				assert.True(t, ok)
				assert.Greater(t, expiresAt, float64(time.Now().Unix()))
			}
		})
	}
}

// testSessionManagement 测试会话管理功能
func testSessionManagement(t *testing.T, r *gin.Engine) {
	// 注册并登录获取token
	registerReq := dto.UserRequest{
		Name:     "会话测试用户",
		Email:    "<EMAIL>",
		Password: "password123",
	}
	jsonData, _ := json.Marshal(registerReq)
	req, _ := http.NewRequest("POST", "/api/auth/register", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusCreated, w.Code)

	// 登录获取token
	loginReq := dto.LoginRequest{
		Email:    "<EMAIL>",
		Password: "password123",
		Platform: "web",
		DeviceID: "session-test-device",
	}
	jsonData, _ = json.Marshal(loginReq)
	req, _ = http.NewRequest("POST", "/api/auth/login", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusOK, w.Code)

	var loginResponse dto.LoginResponse
	err := json.Unmarshal(w.Body.Bytes(), &loginResponse)
	require.NoError(t, err)
	token := loginResponse.Token

	t.Run("获取用户会话列表", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/api/users/sessions", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var sessionResponse map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &sessionResponse)
		require.NoError(t, err)
		assert.Contains(t, sessionResponse, "user_id")
		assert.Contains(t, sessionResponse, "sessions")
	})

	t.Run("获取用户个人信息", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/api/users/me", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var userProfile map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &userProfile)
		require.NoError(t, err)
		assert.Equal(t, "<EMAIL>", userProfile["email"])
	})
}

// testAuthMiddleware 测试认证中间件
func testAuthMiddleware(t *testing.T, r *gin.Engine) {
	tests := []struct {
		name           string
		token          string
		endpoint       string
		expectedStatus int
	}{
		{
			name:           "无token访问受保护接口",
			token:          "",
			endpoint:       "/api/users/me",
			expectedStatus: http.StatusUnauthorized,
		},
		{
			name:           "无效token访问受保护接口",
			token:          "invalid-token",
			endpoint:       "/api/users/me",
			expectedStatus: http.StatusUnauthorized,
		},
		{
			name:           "错误格式的Authorization头",
			token:          "WrongFormat token-here",
			endpoint:       "/api/users/me",
			expectedStatus: http.StatusUnauthorized,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, _ := http.NewRequest("GET", tt.endpoint, nil)
			if tt.token != "" {
				if tt.token == "invalid-token" {
					req.Header.Set("Authorization", "Bearer "+tt.token)
				} else {
					req.Header.Set("Authorization", tt.token)
				}
			}
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}

// testMultiPlatformLogin 测试多平台登录功能
func testMultiPlatformLogin(t *testing.T, r *gin.Engine) {
	// 注册测试用户
	registerReq := dto.UserRequest{
		Name:     "多平台测试用户",
		Email:    "<EMAIL>",
		Password: "password123",
	}
	jsonData, _ := json.Marshal(registerReq)
	req, _ := http.NewRequest("POST", "/api/auth/register", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusCreated, w.Code)

	platforms := []struct {
		platform string
		deviceID string
	}{
		{"web", "web-device-1"},
		{"mobile", "mobile-device-1"},
		{"miniprogram", "miniprogram-device-1"},
	}

	tokens := make(map[string]string)

	// 测试不同平台登录
	for _, p := range platforms {
		t.Run(fmt.Sprintf("平台%s登录", p.platform), func(t *testing.T) {
			loginReq := dto.LoginRequest{
				Email:    "<EMAIL>",
				Password: "password123",
				Platform: p.platform,
				DeviceID: p.deviceID,
			}
			jsonData, _ := json.Marshal(loginReq)
			req, _ := http.NewRequest("POST", "/api/auth/login", bytes.NewBuffer(jsonData))
			req.Header.Set("Content-Type", "application/json")
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			assert.Equal(t, http.StatusOK, w.Code)

			var response dto.LoginResponse
			err := json.Unmarshal(w.Body.Bytes(), &response)
			require.NoError(t, err)

			tokens[p.platform] = response.Token
			assert.NotEmpty(t, response.Token)
			assert.NotEmpty(t, response.SessionID)
		})
	}

	// 验证所有平台的token都有效
	for platform, token := range tokens {
		t.Run(fmt.Sprintf("验证%s平台token有效性", platform), func(t *testing.T) {
			req, _ := http.NewRequest("GET", "/api/users/me", nil)
			req.Header.Set("Authorization", "Bearer "+token)
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			assert.Equal(t, http.StatusOK, w.Code)
		})
	}
}
