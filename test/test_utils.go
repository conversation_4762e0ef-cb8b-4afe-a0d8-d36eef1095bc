package test

import (
	"os"
	"time"

	"github.com/azel-ko/final-ddd/internal/domain/entities"
	"github.com/gin-gonic/gin"
)

// MockRepository 共享的模拟仓库
type MockRepository struct {
	users       map[uint]*entities.User
	checkpoints map[uint]*entities.Checkpoint
}

func NewMockRepository() *MockRepository {
	return &MockRepository{
		users:       make(map[uint]*entities.User),
		checkpoints: make(map[uint]*entities.Checkpoint),
	}
}

// User相关方法
func (m *MockRepository) CreateUser(user *entities.User) error {
	m.users[user.ID] = user
	return nil
}

func (m *MockRepository) GetUser(id uint) (*entities.User, error) {
	if user, exists := m.users[id]; exists {
		return user, nil
	}
	return nil, nil
}

func (m *MockRepository) UpdateUser(user *entities.User) error {
	m.users[user.ID] = user
	return nil
}

func (m *MockRepository) DeleteUser(id uint) error {
	delete(m.users, id)
	return nil
}

func (m *MockRepository) GetUserByEmail(email string) (*entities.User, error) {
	for _, user := range m.users {
		if user.Email == email {
			return user, nil
		}
	}
	return nil, nil
}

func (m *MockRepository) UpdateUserProfile(user *entities.User) error {
	return m.UpdateUser(user)
}

// Checkpoint相关方法
func (m *MockRepository) CreateCheckpoint(checkpoint *entities.Checkpoint) error {
	m.checkpoints[checkpoint.ID] = checkpoint
	return nil
}

func (m *MockRepository) GetCheckpoint(id uint) (*entities.Checkpoint, error) {
	if checkpoint, exists := m.checkpoints[id]; exists {
		return checkpoint, nil
	}
	return nil, nil
}

func (m *MockRepository) UpdateCheckpoint(checkpoint *entities.Checkpoint) error {
	m.checkpoints[checkpoint.ID] = checkpoint
	return nil
}

func (m *MockRepository) DeleteCheckpoint(id uint) error {
	delete(m.checkpoints, id)
	return nil
}

func (m *MockRepository) GetNearbyCheckpoints(lat, lng float64, radius int) ([]*entities.Checkpoint, error) {
	var result []*entities.Checkpoint
	for _, checkpoint := range m.checkpoints {
		result = append(result, checkpoint)
	}
	return result, nil
}

func (m *MockRepository) GetAllCheckpoints() ([]*entities.Checkpoint, error) {
	var result []*entities.Checkpoint
	for _, checkpoint := range m.checkpoints {
		result = append(result, checkpoint)
	}
	return result, nil
}

// 测试路由器设置
func setupTestRouter() *gin.Engine {
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// 添加基本的测试路由
	api := router.Group("/api/v1")
	{
		// 认证相关
		auth := api.Group("/auth")
		{
			auth.POST("/login", mockLoginHandler)
			auth.POST("/register", mockRegisterHandler)
			auth.POST("/miniprogram/login", mockMiniprogramLoginHandler)
		}

		// 检查站相关
		checkpoints := api.Group("/checkpoints")
		{
			checkpoints.GET("/nearby", mockNearbyCheckpointsHandler)
			checkpoints.GET("/:id", mockCheckpointDetailHandler)
			checkpoints.POST("/report", mockReportCheckpointHandler)
		}

		// 导航相关
		navigation := api.Group("/navigation")
		{
			navigation.POST("/route", mockRouteHandler)
			navigation.GET("/history", mockRouteHistoryHandler)
		}

		// 用户相关
		users := api.Group("/users")
		{
			users.GET("/preferences", mockUserPreferencesHandler)
			users.PUT("/preferences", mockUpdatePreferencesHandler)
			users.GET("/subscription", mockSubscriptionHandler)
		}

		// 管理员相关
		admin := api.Group("/admin")
		{
			admin.GET("/checkpoints", mockAdminCheckpointsHandler)
			admin.POST("/checkpoints", mockCreateCheckpointHandler)
			admin.GET("/users", mockAdminUsersHandler)
			admin.GET("/subscriptions/stats", mockSubscriptionStatsHandler)
			admin.GET("/system/status", mockSystemStatusHandler)
			admin.GET("/analytics/user-behavior", mockAnalyticsHandler)
		}

		// 社区相关
		community := api.Group("/community")
		{
			community.POST("/share", mockShareHandler)
			community.GET("/nearby", mockCommunityNearbyHandler)
		}
	}

	return router
}

// Mock handlers
func mockLoginHandler(c *gin.Context) {
	c.JSON(200, gin.H{
		"token": "test-admin-token",
		"user": gin.H{
			"id":       "admin-1",
			"username": "admin",
			"role":     "admin",
		},
	})
}

func mockRegisterHandler(c *gin.Context) {
	c.JSON(200, gin.H{
		"token": "test-user-token",
		"user": gin.H{
			"id":       "user-1",
			"username": "test_user",
		},
		"subscription": gin.H{
			"type":         "trial",
			"trial_expiry": time.Now().Add(72 * time.Hour),
		},
	})
}

func mockMiniprogramLoginHandler(c *gin.Context) {
	c.JSON(200, gin.H{
		"token": "test-miniprogram-token",
		"user": gin.H{
			"id":       "mp-user-1",
			"nickname": "小程序测试用户",
			"platform": "miniprogram",
		},
		"subscription": gin.H{
			"type":         "trial",
			"trial_expiry": time.Now().Add(72 * time.Hour),
		},
	})
}

func mockNearbyCheckpointsHandler(c *gin.Context) {
	c.JSON(200, gin.H{
		"data": []gin.H{
			{
				"id":        "cp-1",
				"name":      "测试检查站1",
				"latitude":  39.9042,
				"longitude": 116.4074,
				"status":    "active",
				"severity":  3,
			},
		},
		"total": 1,
	})
}

func mockCheckpointDetailHandler(c *gin.Context) {
	c.JSON(200, gin.H{
		"id":        c.Param("id"),
		"name":      "测试检查站详情",
		"latitude":  39.9042,
		"longitude": 116.4074,
		"status":    "active",
		"severity":  3,
	})
}

func mockReportCheckpointHandler(c *gin.Context) {
	c.JSON(200, gin.H{"message": "举报成功"})
}

func mockRouteHandler(c *gin.Context) {
	// 检查用户权限
	authHeader := c.GetHeader("Authorization")
	if authHeader == "Bearer test-free-token" {
		c.JSON(403, gin.H{"error": "试用期已过期，请升级到付费版本"})
		return
	}

	c.JSON(200, gin.H{
		"routes": []gin.H{
			{
				"polyline":       "test-polyline-data",
				"distance":       15000,
				"estimated_time": 1800,
			},
		},
		"avoided_points": []gin.H{
			{"id": "cp-1", "name": "避开的检查站1"},
		},
		"estimated_time": 1800,
		"risk_level":     2,
	})
}

func mockRouteHistoryHandler(c *gin.Context) {
	c.JSON(200, gin.H{
		"data": []gin.H{
			{
				"id":             "route-1",
				"origin":         gin.H{"lat": 39.9042, "lng": 116.4074},
				"destination":    gin.H{"lat": 40.0042, "lng": 116.5074},
				"estimated_time": 1800,
				"created_at":     time.Now(),
			},
		},
		"total": 1,
	})
}

func mockUserPreferencesHandler(c *gin.Context) {
	c.JSON(200, gin.H{
		"default_avoid_level": 2,
		"notification_types":  []string{"checkpoint_update"},
		"car_plate":           "京A12345",
		"plate_region":        "北京",
	})
}

func mockUpdatePreferencesHandler(c *gin.Context) {
	c.JSON(200, gin.H{"message": "偏好设置更新成功"})
}

func mockSubscriptionHandler(c *gin.Context) {
	c.JSON(200, gin.H{
		"subscription": gin.H{
			"type":         "trial",
			"trial_expiry": time.Now().Add(48 * time.Hour),
		},
		"permissions": gin.H{
			"can_navigate":         true,
			"can_view_checkpoints": true,
		},
	})
}

func mockAdminCheckpointsHandler(c *gin.Context) {
	c.JSON(200, gin.H{
		"data": []gin.H{
			{
				"id":        "cp-1",
				"name":      "管理后台检查站1",
				"latitude":  39.9042,
				"longitude": 116.4074,
				"status":    "active",
			},
		},
		"total": 1,
	})
}

func mockCreateCheckpointHandler(c *gin.Context) {
	c.JSON(201, gin.H{
		"id":      "new-cp-1",
		"message": "检查站创建成功",
	})
}

func mockAdminUsersHandler(c *gin.Context) {
	c.JSON(200, gin.H{
		"data": []gin.H{
			{
				"id":           "user-1",
				"username":     "test_user",
				"subscription": "trial",
				"created_at":   time.Now(),
			},
		},
		"total": 1,
	})
}

func mockSubscriptionStatsHandler(c *gin.Context) {
	c.JSON(200, gin.H{
		"total_users":   100,
		"trial_users":   30,
		"free_users":    50,
		"premium_users": 20,
	})
}

func mockSystemStatusHandler(c *gin.Context) {
	c.JSON(200, gin.H{
		"database_status": "healthy",
		"redis_status":    "healthy",
		"api_status":      "healthy",
	})
}

func mockAnalyticsHandler(c *gin.Context) {
	c.JSON(200, gin.H{
		"daily_active_users": 150,
		"route_requests":     500,
		"checkpoint_queries": 800,
	})
}

func mockShareHandler(c *gin.Context) {
	c.JSON(200, gin.H{"message": "分享成功"})
}

func mockCommunityNearbyHandler(c *gin.Context) {
	c.JSON(200, gin.H{
		"data": []gin.H{
			{
				"id":       "share-1",
				"message":  "这里有检查站",
				"location": gin.H{"lat": 39.9042, "lng": 116.4074},
				"user":     gin.H{"nickname": "热心用户"},
			},
		},
	})
}

// writeToFile 写入文件的辅助函数
func writeToFile(filename, content string) error {
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	_, err = file.WriteString(content)
	return err
}
