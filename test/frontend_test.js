// 前端功能测试脚本
// 测试Web管理后台和小程序的兼容性和用户体验

const axios = require('axios');
const assert = require('assert');

// 测试配置
const config = {
    baseURL: process.env.API_BASE_URL || 'http://localhost:8080',
    timeout: 10000,
    adminCredentials: {
        username: 'admin',
        password: 'admin123'
    },
    testUser: {
        username: 'test_user',
        password: 'test123',
        platform: 'miniprogram'
    }
};

// 创建axios实例
const api = axios.create({
    baseURL: config.baseURL + '/api/v1',
    timeout: config.timeout,
    headers: {
        'Content-Type': 'application/json'
    }
});

// 测试结果记录
const testResults = {
    passed: 0,
    failed: 0,
    errors: []
};

// 测试工具函数
function logTest(testName, passed, error = null) {
    if (passed) {
        console.log(`✅ ${testName}`);
        testResults.passed++;
    } else {
        console.log(`❌ ${testName}: ${error}`);
        testResults.failed++;
        testResults.errors.push({ test: testName, error: error });
    }
}

// 主测试函数
async function runTests() {
    console.log('🚀 开始前端功能测试...\n');
    
    try {
        // 1. Web管理后台测试
        console.log('📊 Web管理后台功能测试');
        await testWebAdminFunctionality();
        
        // 2. 小程序功能测试
        console.log('\n📱 小程序功能测试');
        await testMiniprogramFunctionality();
        
        // 3. 兼容性测试
        console.log('\n🔄 兼容性和用户体验测试');
        await testCompatibilityAndUX();
        
        // 输出测试结果
        console.log('\n📋 测试结果汇总:');
        console.log(`通过: ${testResults.passed}`);
        console.log(`失败: ${testResults.failed}`);
        
        if (testResults.failed > 0) {
            console.log('\n❌ 失败的测试:');
            testResults.errors.forEach(error => {
                console.log(`  - ${error.test}: ${error.error}`);
            });
        }
        
        // 生成测试报告
        await generateTestReport();
        
    } catch (error) {
        console.error('测试执行出错:', error.message);
        process.exit(1);
    }
}

// Web管理后台功能测试
async function testWebAdminFunctionality() {
    let adminToken = null;
    
    try {
        // 管理员登录
        const loginResponse = await api.post('/auth/login', config.adminCredentials);
        adminToken = loginResponse.data.token;
        logTest('管理员登录', !!adminToken);
        
        // 设置认证头
        api.defaults.headers.common['Authorization'] = `Bearer ${adminToken}`;
        
        // 测试检查站管理
        const checkpointsResponse = await api.get('/admin/checkpoints');
        logTest('获取检查站列表', checkpointsResponse.status === 200);
        
        // 测试用户管理
        const usersResponse = await api.get('/admin/users');
        logTest('获取用户列表', usersResponse.status === 200);
        
        // 测试订阅统计
        const subscriptionStatsResponse = await api.get('/admin/subscriptions/stats');
        logTest('获取订阅统计', subscriptionStatsResponse.status === 200);
        
        // 测试系统监控
        const systemStatusResponse = await api.get('/admin/system/status');
        logTest('系统状态监控', systemStatusResponse.status === 200);
        
        // 测试数据分析
        const analyticsResponse = await api.get('/admin/analytics/user-behavior');
        logTest('用户行为分析', analyticsResponse.status === 200);
        
        // 测试检查站创建
        const newCheckpoint = {
            name: '测试检查站',
            latitude: 39.9042,
            longitude: 116.4074,
            address: '北京市朝阳区',
            status: 'active',
            severity: 3
        };
        
        try {
            const createResponse = await api.post('/admin/checkpoints', newCheckpoint);
            logTest('创建检查站', createResponse.status === 201);
        } catch (error) {
            logTest('创建检查站', false, error.response?.data?.message || error.message);
        }
        
    } catch (error) {
        logTest('Web管理后台测试', false, error.response?.data?.message || error.message);
    }
}

// 小程序功能测试
async function testMiniprogramFunctionality() {
    let userToken = null;
    
    try {
        // 小程序用户注册/登录
        const registerData = {
            ...config.testUser,
            code: 'test-wx-code',
            user_info: {
                nickName: '测试用户',
                avatarUrl: 'https://example.com/avatar.jpg',
                gender: 1,
                city: '北京',
                province: '北京',
                country: '中国'
            }
        };
        
        // 设置小程序请求头
        api.defaults.headers.common['User-Agent'] = 'miniprogram';
        
        try {
            const loginResponse = await api.post('/auth/miniprogram/login', registerData);
            userToken = loginResponse.data.token;
            logTest('小程序用户登录', !!userToken);
            
            // 验证试用期设置
            const subscription = loginResponse.data.subscription;
            logTest('新用户获得试用期', subscription.type === 'trial');
            
        } catch (error) {
            // 如果登录失败，尝试注册
            try {
                const registerResponse = await api.post('/auth/register', config.testUser);
                userToken = registerResponse.data.token;
                logTest('小程序用户注册', !!userToken);
            } catch (regError) {
                logTest('小程序用户认证', false, regError.response?.data?.message || regError.message);
                return;
            }
        }
        
        // 设置用户认证头
        api.defaults.headers.common['Authorization'] = `Bearer ${userToken}`;
        
        // 测试检查站查询
        const nearbyCheckpoints = await api.get('/checkpoints/nearby?lat=39.9042&lng=116.4074&radius=10');
        logTest('查询附近检查站', nearbyCheckpoints.status === 200);
        
        // 测试路线规划（试用期用户应该可以使用）
        const routeData = {
            origin: { lat: 39.9042, lng: 116.4074 },
            destination: { lat: 40.0042, lng: 116.5074 },
            car_plate: '冀A12345',
            avoid_level: 2
        };
        
        try {
            const routeResponse = await api.post('/navigation/route', routeData);
            logTest('路线规划功能', routeResponse.status === 200);
            
            // 验证返回数据结构
            const routeResult = routeResponse.data;
            logTest('路线规划结果完整性', 
                routeResult.routes && 
                routeResult.avoided_points !== undefined && 
                routeResult.estimated_time !== undefined
            );
            
        } catch (error) {
            logTest('路线规划功能', false, error.response?.data?.message || error.message);
        }
        
        // 测试用户偏好设置
        const preferences = {
            default_avoid_level: 3,
            notification_types: ['checkpoint_update', 'route_change'],
            car_plate: '冀A12345',
            plate_region: '河北'
        };
        
        try {
            const prefsResponse = await api.put('/users/preferences', preferences);
            logTest('用户偏好设置', prefsResponse.status === 200);
        } catch (error) {
            logTest('用户偏好设置', false, error.response?.data?.message || error.message);
        }
        
        // 测试订阅状态查询
        try {
            const subscriptionResponse = await api.get('/users/subscription');
            logTest('查询订阅状态', subscriptionResponse.status === 200);
        } catch (error) {
            logTest('查询订阅状态', false, error.response?.data?.message || error.message);
        }
        
        // 测试检查站举报
        const reportData = {
            checkpoint_id: 'test-checkpoint-id',
            status: 'active',
            severity: 3,
            comment: '刚刚经过，检查很严格'
        };
        
        try {
            const reportResponse = await api.post('/checkpoints/report', reportData);
            logTest('检查站状态举报', reportResponse.status === 200);
        } catch (error) {
            logTest('检查站状态举报', false, error.response?.data?.message || error.message);
        }
        
    } catch (error) {
        logTest('小程序功能测试', false, error.response?.data?.message || error.message);
    }
}

// 兼容性和用户体验测试
async function testCompatibilityAndUX() {
    try {
        // 测试API响应时间
        const startTime = Date.now();
        await api.get('/checkpoints/nearby?lat=39.9042&lng=116.4074&radius=10');
        const responseTime = Date.now() - startTime;
        logTest('API响应时间 (<2秒)', responseTime < 2000);
        
        // 测试错误处理
        try {
            await api.get('/nonexistent-endpoint');
        } catch (error) {
            logTest('404错误处理', error.response?.status === 404);
        }
        
        // 测试权限控制
        // 移除认证头测试未授权访问
        delete api.defaults.headers.common['Authorization'];
        
        try {
            await api.get('/users/preferences');
        } catch (error) {
            logTest('未授权访问控制', error.response?.status === 401);
        }
        
        // 测试不同平台的User-Agent处理
        api.defaults.headers.common['User-Agent'] = 'web-admin';
        try {
            const webResponse = await api.get('/checkpoints');
            logTest('Web端User-Agent处理', webResponse.status === 200 || webResponse.status === 401);
        } catch (error) {
            logTest('Web端User-Agent处理', true); // 401也是正常的
        }
        
        api.defaults.headers.common['User-Agent'] = 'miniprogram';
        try {
            const miniprogramResponse = await api.get('/checkpoints');
            logTest('小程序User-Agent处理', miniprogramResponse.status === 200 || miniprogramResponse.status === 401);
        } catch (error) {
            logTest('小程序User-Agent处理', true); // 401也是正常的
        }
        
        // 测试数据格式一致性
        try {
            // 重新获取管理员token进行测试
            const loginResponse = await api.post('/auth/login', config.adminCredentials);
            api.defaults.headers.common['Authorization'] = `Bearer ${loginResponse.data.token}`;
            
            const checkpointsResponse = await api.get('/admin/checkpoints');
            const checkpoints = checkpointsResponse.data.data;
            
            if (checkpoints && checkpoints.length > 0) {
                const checkpoint = checkpoints[0];
                const hasRequiredFields = checkpoint.id && checkpoint.name && 
                                        checkpoint.latitude !== undefined && 
                                        checkpoint.longitude !== undefined;
                logTest('数据格式一致性', hasRequiredFields);
            } else {
                logTest('数据格式一致性', true); // 没有数据也算通过
            }
        } catch (error) {
            logTest('数据格式一致性', false, error.response?.data?.message || error.message);
        }
        
    } catch (error) {
        logTest('兼容性测试', false, error.message);
    }
}

// 生成测试报告
async function generateTestReport() {
    const report = {
        timestamp: new Date().toISOString(),
        summary: {
            total: testResults.passed + testResults.failed,
            passed: testResults.passed,
            failed: testResults.failed,
            success_rate: ((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(2) + '%'
        },
        errors: testResults.errors,
        environment: {
            api_base_url: config.baseURL,
            node_version: process.version,
            platform: process.platform
        }
    };
    
    // 写入测试报告文件
    const fs = require('fs');
    const reportPath = './test/frontend_test_report.json';
    
    try {
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        console.log(`\n📄 测试报告已生成: ${reportPath}`);
    } catch (error) {
        console.error('生成测试报告失败:', error.message);
    }
}

// 运行测试
if (require.main === module) {
    runTests().then(() => {
        const exitCode = testResults.failed > 0 ? 1 : 0;
        process.exit(exitCode);
    }).catch(error => {
        console.error('测试运行失败:', error);
        process.exit(1);
    });
}

module.exports = {
    runTests,
    testWebAdminFunctionality,
    testMiniprogramFunctionality,
    testCompatibilityAndUX
};